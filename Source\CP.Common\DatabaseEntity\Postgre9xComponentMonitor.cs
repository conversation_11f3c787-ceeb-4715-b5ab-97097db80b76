﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using CP.Common.Base;
using System.Runtime.Serialization;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    public class Postgre9xComponentMonitor : BaseEntity 
    {
        #region Properties
        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string DBVersionPR { get; set; }

        [DataMember]
        public string DBVersionDR { get; set; }

        [DataMember]
        public string DBServiceStatusPR { get; set; }

        [DataMember]
        public string DBServiceStatusDR { get; set; }

        [DataMember]
        public string DBClusterStatusPR { get; set; }

        [DataMember]
        public string DBClusterStatusDR { get; set; }

        [DataMember]
        public string DBRecoveryStatusDR { get; set; }

        [DataMember]
        public string DBRecoveryStatusPR { get; set; }


        #endregion
    }
}
