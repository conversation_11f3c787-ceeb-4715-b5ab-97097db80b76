﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class ApplicationServiceBuilder : IEntityBuilder<ApplicationService>
    {
        IList<ApplicationService> IEntityBuilder<ApplicationService>.BuildEntities(IDataReader reader)
        {
            var applicationservices = new List<ApplicationService>();

            while (reader.Read())
            {
                applicationservices.Add(((IEntityBuilder<ApplicationService>)this).BuildEntity(reader,
                    new ApplicationService()));
            }

            return (applicationservices.Count > 0) ? applicationservices : null;
        }

        ApplicationService IEntityBuilder<ApplicationService>.BuildEntity(IDataReader reader,
            ApplicationService applicationservice)
        {
            //const int FLD_ID = 0;
            //const int FLD_APPLICATIONID = 1;
            //const int FLD_SERVICENAME = 2;
            //const int FLD_STATUS = 3;
            //const int FLD_ISACTIVE = 4;
            //const int FLD_CREATORID = 5;
            //const int FLD_CREATEDATE = 6;
            //const int FLD_UPDATORID = 7;
            //const int FLD_UPDATEDATE = 8;

            //applicationservice.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //applicationservice.ApplicationId = reader.IsDBNull(FLD_APPLICATIONID) ? 0 : reader.GetInt32(FLD_APPLICATIONID);
            //applicationservice.ServiceName = reader.IsDBNull(FLD_SERVICENAME) ? string.Empty : reader.GetString(FLD_SERVICENAME);
            //applicationservice.Status = !reader.IsDBNull(FLD_STATUS) && reader.GetBoolean(FLD_STATUS);
            //applicationservice.IsActive = !reader.IsDBNull(FLD_ISACTIVE) && reader.GetBoolean(FLD_ISACTIVE);
            //applicationservice.CreatorId = reader.IsDBNull(FLD_CREATORID) ? 0 : reader.GetInt32(FLD_CREATORID);
            //applicationservice.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);
            //applicationservice.UpdatorId = reader.IsDBNull(FLD_UPDATORID) ? 0 : reader.GetInt32(FLD_UPDATORID);
            //applicationservice.UpdateDate = reader.IsDBNull(FLD_UPDATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_UPDATEDATE);

            //Fields in bcms_application_service_status table on 16/07/2013 : Id, ApplicationId, ServiceName, Status, IsActive, CreatorId, CreateDate, UpdatorId, UpdateDate

            applicationservice.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            applicationservice.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"])
                ? 0
                : Convert.ToInt32(reader["InfraObjectId"]);
            applicationservice.ServiceName = Convert.IsDBNull(reader["ServiceName"])
                ? string.Empty
                : Convert.ToString(reader["ServiceName"]);

            if (!Convert.IsDBNull(reader["Status"]))
                applicationservice.Status = Convert.ToBoolean(reader["Status"]);

            applicationservice.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);

            applicationservice.CreatorId = Convert.IsDBNull(reader["CreatorId"])
                ? 0
                : Convert.ToInt32(reader["CreatorId"]);
            applicationservice.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            applicationservice.UpdatorId = Convert.IsDBNull(reader["UpdatorId"])
                ? 0
                : Convert.ToInt32(reader["UpdatorId"]);
            applicationservice.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());
            applicationservice.ServerType = Convert.IsDBNull(reader["ServerType"])
                ? string.Empty
                : Convert.ToString(reader["ServerType"]);

            return applicationservice;
        }
    }
}