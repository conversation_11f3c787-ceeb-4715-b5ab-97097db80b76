﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "RSyncMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class RSyncMonitor : BaseEntity
    {
        //#region Properties
      
        //[DataMember]
        //public int InfraobjectId { get; set; }

        //[DataMember]
        //public string PRPrimaryServer { get; set; }

        //[DataMember]
        //public string DRPrimaryServer { get; set; }

        //[DataMember]
        //public string PRSecondaryServer { get; set; }

        //[DataMember]
        //public string DRSecondaryServer { get; set; }


        //[DataMember]
        //public string PRSourceRepliPath { get; set; }

        //[DataMember]
        //public string DRSourceRepliPath { get; set; }
    
        //[DataMember]
        //public string PRDestinationPath { get; set; }
        //[DataMember]
        //public string DRDestinationPath { get; set; }
        //[DataMember]
        //public string PRNumberofFiles { get; set; }
        //[DataMember]
        //public string DRNumberofFiles { get; set; }
        //[DataMember]
        //public string PRTotalFileSize { get; set; }
        //[DataMember]
        //public string DRTotalFileSize { get; set; }

        //[DataMember]
        //public string PRNumofTransferFiles { get; set; }
        //[DataMember]
        //public string DRNumofTransferFiles { get; set; }
        //[DataMember]
        //public string PRTotalTransferFileSize { get; set; }
        //[DataMember]
        //public string DRTotalTransferFileSize { get; set; }

        //[DataMember]
        //public int JobId { get; set; }

        //#endregion Properties

        #region Properties

        [DataMember]
        public int RSyncJobId
        {
            get;
            set;
        }

        [DataMember]
        public int InfraobjectId
        {
            get;
            set;
        }

        [DataMember]
        public string SourceIP
        {
            get;
            set;
        }

        [DataMember]
        public string DestinationIP
        {
            get;
            set;
        }

        [DataMember]
        public string SourcePath
        {
            get;
            set;
        }

        [DataMember]
        public string DestinationPath
        {
            get;
            set;
        }

        [DataMember]
        public string RepStartTime
        {
            get;
            set;
        }

        [DataMember]
        public string RepEndTime
        {
            get;
            set;
        }

        [DataMember]
        public string Status
        {
            get;
            set;
        }

        [DataMember]
        public string SelectedOptions
        {
            get;
            set;
        }

        [DataMember]
        public string TotalFilesSize
        {
            get;
            set;
        }

        [DataMember]
        public string Datalag
        {
            get;
            set;
        }

        [DataMember]
        public string TotalNumberoffiles
        {
            get;
            set;
        }

        [DataMember]
        public string TotalTransferfileSize
        {
            get;
            set;
        }

        [DataMember]
        public string NumberOfRegFilesTransfer
        {
            get;
            set;
        }

       

        #endregion Properties
    }
}
