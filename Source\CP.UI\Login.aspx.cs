using System;
using System.Collections.Generic;
using System.Configuration;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Linq;
using System.ComponentModel;
using System.IO;
using System.Management;
using CP.Common.Shared;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using CP.Helper;
using System.Web.Services;
using log4net;
using System.Text;
using System.DirectoryServices;
using System.DirectoryServices.ActiveDirectory;
using System.DirectoryServices.AccountManagement;
using System.Text.RegularExpressions;
using Microsoft.Security.Application;
using CP.UI.Controls;
using System.Collections;
using System.Web.Security;
using System.Web.SessionState;
using System.Reflection;

namespace CP.UI
{
    public partial class Login : BasePage
    {
        private IFacade _facade = new Facade();

        private string _companyname = string.Empty;

        private const int MaxLoginAttempt = 3;// 9999;

        private static readonly ILog _logger = LogManager.GetLogger(typeof(Login));

        public int cId;
        User userDetail = new User();
        string newSessionId = string.Empty;
        private object staticGuid;

        /// <summary>
        /// Gets the static GUID from session for encryption/decryption operations
        /// </summary>
        /// <returns>Static GUID string from session</returns>
        private string GetStaticGuidFromSession()
        {
            string newGuid = Guid.NewGuid().ToString();
            Session["StaticGuid"] = newGuid;
            return newGuid;
        }

        /// <summary>
        /// Web method to get static GUID for JavaScript calls
        /// </summary>
        /// <returns>Static GUID from session</returns>
        [System.Web.Services.WebMethod]
        public static string GetStaticGuid()
        {
            var session = HttpContext.Current.Session;

            if (session["StaticGuid"] == null)
            {
                session["StaticGuid"] = Guid.NewGuid().ToString();
            }

            return session["StaticGuid"].ToString();
        }

        /// <summary>
        /// Gets the encrypted password from session
        /// </summary>
        /// <returns>Encrypted password string from session</returns>
        private string GetEncryptedPasswordFromSession()
        {
            return Session["EncryptedPassword"]?.ToString() ?? string.Empty;
        }

        /// <summary>
        /// Sets the encrypted password in session
        /// </summary>
        /// <param name="encryptedPassword">Encrypted password to store</param>
        private void SetEncryptedPasswordInSession(string encryptedPassword)
        {
            Session["EncryptedPassword"] = encryptedPassword;
        }

        /// <summary>
        /// Web method to set encrypted password in session from JavaScript
        /// </summary>
        /// <param name="encryptedPassword">Encrypted password value</param>
        /// <returns>Success status</returns>
        [System.Web.Services.WebMethod]
        public static bool SetEncryptedPassword(string encryptedPassword)
        {
            try
            {
                HttpContext.Current.Session["EncryptedPassword"] = encryptedPassword;
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Web method to get encrypted password from session for JavaScript calls
        /// </summary>
        /// <returns>Encrypted password from session</returns>
        [System.Web.Services.WebMethod]
        public static string GetEncryptedPassword()
        {
            return HttpContext.Current.Session["EncryptedPassword"]?.ToString() ?? string.Empty;
        }



        private void AddCookie()
        {
            FormsAuthenticationTicket ticket = new FormsAuthenticationTicket(1, "TestCookie", DateTime.Now, DateTime.Now.AddSeconds(5), false, "");
            string encryptedText = FormsAuthentication.Encrypt(ticket);
           // Response.Cookies.Add(new HttpCookie("ASP.NET_SessionId", encryptedText));
            HttpCookie sessionId = new HttpCookie("ASP.NET_SessionId", encryptedText)
            {
                Path = ConfigurationManager.AppSettings["SessionCookiesPath"].ToString()
            };
            Response.Cookies.Add(sessionId);
        }      

        protected override void OnLoad(EventArgs e)

        {
            Utility.setDBParameterPrefix();

            if (!IsPostBack)
            {
                try
                {
                    //var test = _facade.UpdateParallelWFActionResultByStatus(9333,"Error");
                    Session["ReplicationConfig_token"] = null;
                    /////////////////////////////////////////////////////////////
                    Session.Abandon();
                    Response.Cookies.Add(new HttpCookie("ASP.NET_SessionId", ""));
                    AddCookie();
                    /////////////////////////////////////////////////////////////


                    // Store GUID in Session instead of hidden field for security
                    Session["StaticGuid"] = Guid.NewGuid().ToString();

                    ViewState["_token"] = UrlHelper.AddTokenToRequest();
                    if (ViewState["_token"] != null)
                    {
                        hdtokenKey.Value = ViewState["_token"].ToString();
                    }
                    lblVersion.Text = ConfigurationManager.AppSettings["Version"].ToString();
                    if (ConfigurationManager.AppSettings["IsCompanyId"].Equals("false"))
                    {
                        Utility.PopulateCompanyProfile(ddlCompanyId, false);
                        pnlCmpvalue.Visible = true;
                        pnlcompvalue.Visible = false;
                    }
                    else
                    {
                        pnlCmpvalue.Visible = false;
                        pnlcompvalue.Visible = true;
                    }
                   
                    var company = _facade.GetAllCompanyProfiles();
                    var users = _facade.GetAllUsers();
                    if (company.Count == 0 || users.Count == 0)
                    {
                        Response.Redirect("~/CompanyInfoPage.aspx", false);

                    }
                }
                catch (Exception ex)
                {
                    var bcms = new CpException(CpExceptionType.CommonUnhandled, " ", ex);
                    _logger.Info("Exception Occured while populating company names :" + ex.InnerException.Message);
                    ExceptionManager.Manage(bcms);
                }
            }
        }

        private void VerifyLicenseKey()
        {
            StreamReader sread = null;

            string filePath = Server.MapPath(@"~/Common/Key.inf");

            if (File.Exists(filePath))
            {
                IList<Licencekey> liecenKeyList = Facade.GetAllLicenceKeies();

                if (liecenKeyList != null)
                {
                    using (sread = File.OpenText(filePath))
                    {
                        string read = sread.ReadToEnd();

                        foreach (Licencekey licence in liecenKeyList)
                        {
                            if (licence.Key.EndsWith(read.Replace("\r\n", "")))
                            {
                                DateTime dto = Convert.ToDateTime(licence.ValidTo);
                                int id = licence.Id;

                                if (dto > DateTime.Now)
                                {
                                    licensebg.Visible = false;
                                }
                                else
                                {
                                    Licencekey keyststus = Facade.UpdateLicencekeyByStatus(id, 0);
                                    Expirekey.Visible = true;
                                    licensebg.Visible = true;
                                }
                            }
                        }
                    }
                }
                else
                {
                    licensebg.Visible = true;
                }
            }
        }

        private int GetLoginAttempts()
        {
            #region Commented By priyanka

            //if (ViewState["loginAttempts"] != null)
            //{
            //    ViewState["loginAttempts"] = Convert.ToInt32(ViewState["loginAttempts"].ToString()) + 1;
            //    return Convert.ToInt32(ViewState["loginAttempts"].ToString());
            //}
            //ViewState["loginAttempts"] = 1;
            //return 1;
            #endregion

            if (ViewState["loginAttempts"] != null)
            {
                ViewState["loginAttempts"] = Convert.ToInt32(ViewState["loginAttempts"].ToString()) + 1;
                if (Convert.ToInt32(ViewState["loginAttempts"].ToString()) > 3)
                {
                    pnlCaptcha.Visible = true;
                    rfvCaptcha.Enabled = true;
                }
                else
                {
                    pnlCaptcha.Visible = false;
                    rfvCaptcha.Enabled = false;
                }
                return Convert.ToInt32(ViewState["loginAttempts"].ToString());
            }
            ViewState["loginAttempts"] = 1;
            return 1;
        }

        protected void lnkbtnCaptcha_Click(object sender, EventArgs e)
        {
            rfvCaptcha.Visible = false;

            captcha1.ValidateCaptcha(txtcaptcha.Text.Trim());
        }

        public string GetMacAddress()
        {
            using (var mc = new ManagementClass("Win32_NetworkAdapterConfiguration"))
            {
                ManagementObjectCollection moc = mc.GetInstances();

                string macAddress = String.Empty;

                foreach (ManagementObject mo in moc)
                {
                    if (macAddress == String.Empty) // only return MAC Address from first card
                    {
                        if ((bool)mo["IPEnabled"]) macAddress = mo["MacAddress"].ToString();
                    }
                    mo.Dispose();
                }

                macAddress = macAddress.Replace(":", "-");
                return macAddress;
            }
        }

        private string DecryptString(string encrString)
        {
            string decryptedConnectionString = null;
            try
            {
                byte[] b = Convert.FromBase64String(encrString);
                decryptedConnectionString = System.Text.Encoding.ASCII.GetString(b);
                if (!decryptedConnectionString.Contains("/"))
                    decryptedConnectionString = null;
            }
            catch
            {
                decryptedConnectionString = null;
            }

            return decryptedConnectionString;
        }

        public void CreateLicenceKeyFile(string key)
        {
            string filePath = Server.MapPath(@"~/Common/Key.inf");

            using (var sr = new StreamWriter(filePath))
            {
                try
                {
                    sr.WriteLine(key);
                    sr.Close();
                }
                catch (Exception)
                {
                    sr.Close();
                }
            }
        }

        private bool CheckCompanyNameExist()
        {
            return Facade.IsExistCompanyProfileByName(_companyname.ToLower());
        }

        protected void BtnLoginButtonClick(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null))
            {
                if (!UrlHelper.IsTokenValidated(ViewState["_token"].ToString()) || Request.HttpMethod != "POST")
                {
                    Response.Redirect("~/Logout.aspx");
                    return;
                }
            }
            else
            {
                Response.Redirect("~/Logout.aspx");
                return;
            }

            try
            {
                string orignalStr = string.Empty;
                string userEncryptPass = string.Empty;
                newSessionId = string.Empty;

                userDetail = Facade.GetUserByLoginName(Utility.getOriginalEncryData(UserEncrypt.Value, GetStaticGuidFromSession()));

                int loginAttempts = 0;
                var disname = Facade.GetCompanyDisplayName(txtCompanyCode.Text);
                if (disname != null)
                    cId = disname.Id;

                if (ViewState["loginAttempts"] != null)
                    loginAttempts = Convert.ToInt32((ViewState["loginAttempts"]).ToString());
                else
                    loginAttempts = 0;
                if (chkActiveDirectory.Checked == true)
                {
                    _logger.Info("Active directory user selected");
                      HttpContext.Current.Session["Ad"] = "Aduser";
                    //bool isAuth = AuthenticateADUser(combobox1.Value, UserEncrypt.Value, Decodeval(PassEncyptHidden.Value));
                    Session["_domainNamee"] = combobox1.Value;
                    bool isAuth = AuthenticateADUser(combobox1.Value, Utility.getOriginalEncryData(UserEncrypt.Value, GetStaticGuidFromSession()), Utility.getOriginalEncryData(GetEncryptedPasswordFromSession(), GetStaticGuidFromSession()));
                    if (isAuth == true)
                    {
                        _logger.InfoFormat("AD user authenticated " + UserEncrypt.Value + " with domain " + combobox1.Value);

                        if (ConfigurationManager.AppSettings["IsCaptchaRequired"].Equals("true", StringComparison.OrdinalIgnoreCase))
                        {
                            captcha1.ValidateCaptcha(txtcaptcha.Text.Trim());

                        }

                        if (!captcha1.UserValidated)
                        {
                            divClass.Visible = true;
                            lblerror.Visible = false;
                            FailureText.Text = "please Enter valid Captcha".ToUpper();
                            UserName.Focus();

                            /////////////////////////////////////////////////////////////
                            Session.Abandon();
                            Response.Cookies.Add(new HttpCookie("ASP.NET_SessionId", ""));
                            AddCookie();
                            /////////////////////////////////////////////////////////////

                        }
                        else
                        {

                            var loginType = LoginType.AD;

                            string LoginName = Utility.getOriginalEncryData(UserEncrypt.Value, GetStaticGuidFromSession());
                            Session["LoggedIn"] = Utility.getOriginalEncryData(UserEncrypt.Value, GetStaticGuidFromSession());
                            //string domainValue = combobox1.Value.Substring(0, combobox1.Value.IndexOf('.'));
                            string domainValue = combobox1.Value;
                            string domainName = domainValue + "\\" + LoginName;
                            _logger.InfoFormat("    Searching saved AD user " + domainName + " in continuity patrol database");
                            var activeInfo = Facade.GetUserByLoginName(domainName);
                            if (activeInfo == null)
                            {
                                _logger.InfoFormat("AD user with name " + domainName + " not found with domain in continuity patrol database", CpExceptionType.ADInvalidADAuthentication);
                                throw new CpException(CpExceptionType.ActiveDrUserNotFound);
                            }
                            var userInfo = Facade.GetUserById(activeInfo.Id);

                            _logger.InfoFormat("AD userInfo " + userInfo);
                            _logger.InfoFormat("AD userInfo " + userInfo.UserName);
                           
                            if (ConfigurationManager.AppSettings["IsCompanyId"].Equals("true"))
                            {
                                _logger.InfoFormat("companyinfo if IsCompanyId is true  ");
                                var companyinfo = Facade.GetCompanyInfoByCompanyProfileId(cId);
                                var superUser = Facade.GetSuperUserId(cId);
                                _logger.InfoFormat("companyinfo if IsCompanyId is true  " + companyinfo);
                                _logger.InfoFormat("superUser in Ad  " + superUser.Count);
                                if (userInfo.LastLoginDate == Convert.ToDateTime("1/1/0001 12:00:00 AM"))
                                {
                                    _logger.InfoFormat("userInfo.LastLoginDate  Convert.ToDateTime(1 / 1 / 0001 12:00:00 AM) is true" );
                                    var smtpInfo = Facade.GetSmtpConfigurations();
                                   
                                    _logger.InfoFormat("smtpInfo in AD: "+ smtpInfo.Count);
                                    if (smtpInfo == null)
                                    {
                                        _logger.InfoFormat("smtpInfo if is null  ");
                                         ReGenerateSessionId();
                                        _facade.ValidateCredentialsByLoginType(domainName, Utility.getOriginalEncryData(GetEncryptedPasswordFromSession(), GetStaticGuidFromSession()), Convert.ToInt32(ddlCompanyId.SelectedValue), loginType.ToString());
                                       // _facade.UpdateUserSessionIdByID(userInfo.Id, Session.SessionID);
                                        _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId); 
                                      //  ErrorSuccessNotifier.AddErrorMessage("SMTP Not configured. Please contact Administrator");
                                    }
                                    else
                                    {
                                        _logger.InfoFormat("companyinfo if IsCompanyId is true  ");
                                        _logger.InfoFormat("smtpInfo if not null is true  ");
                                        _logger.InfoFormat("smtpInfo count  1 ;"+smtpInfo.Count);
                                        try
                                        {
                                            _logger.InfoFormat("smtpInfo if IsCompanyId is true  started");
                                            foreach (var smtpDetails in smtpInfo)
                                            {
                                                _logger.InfoFormat("smtpInfo  " + smtpDetails.UserName);
                                                _logger.InfoFormat("smtpInfo  " + smtpDetails.SmtpHost);
                                                _logger.InfoFormat("smtpInfo  " + smtpDetails.Port);

                                                CP.Helper.EmailManager emailManager = new CP.Helper.EmailManager();
                                                emailManager.From = CryptographyHelper.Md5Decrypt(smtpDetails.UserName);
                                                emailManager.Body = string.Concat("Domain user: ", userInfo.LoginName, " is logged  in successfully into Continuity Patrol at ", userInfo.CreateDate, "<br/>", "\r\n\n\n Regards,", "<br/>", "Perpetuuiti Technosoft Pvt.Ltd");
                                                emailManager.Subject = "Active Ditectory Login Info";
                                                SmtpConfiguration smtpConfig = new SmtpConfiguration();
                                                smtpConfig.SmtpHost = CryptographyHelper.Md5Decrypt(smtpDetails.SmtpHost);
                                                smtpConfig.Port = smtpDetails.Port;

                                                if (!string.IsNullOrEmpty(smtpDetails.Password))
                                                    smtpConfig.Password = CryptographyHelper.Md5Decrypt(smtpDetails.Password);

                                                smtpConfig.EnableSSL = true;
                                                smtpConfig.IsBodyHTML = true;
                                                //  smtpConfig.UserName = companyinfo.Email;
                                                foreach (var info in superUser)
                                                {
                                                    smtpConfig.UserName = info.UserInformation.Email;
                                                    string output = emailManager.SendTestMail(smtpConfig);
                                                }

                                                ReGenerateSessionId();
                                                _facade.ValidateCredentialsByLoginType(domainName, Decodeval(GetEncryptedPasswordFromSession()), cId, loginType.ToString());
                                                _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId);

                                            }
                                        }
                                        catch(Exception exe)
                                        {
                                            _logger.Error("Exception in smtpInfo if IsCompanyId is true  :" + exe.InnerException);
                                        }
                                    }
                                }
                                else
                                {
                                    _logger.InfoFormat("userInfo.LastLoginDate  Convert.ToDateTime(1 / 1 / 0001 12:00:00 AM) is false");
                                    ReGenerateSessionId();
                                    _facade.ValidateCredentialsByLoginType(domainName, Decodeval(GetEncryptedPasswordFromSession()), cId, loginType.ToString());
                                    _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId);
                                }
                            }
                            else
                            {
                                _logger.InfoFormat("companyinfo if IsCompanyId is false  ");
                                var companyinfo = Facade.GetCompanyInfoByCompanyProfileId(Convert.ToInt32(ddlCompanyId.SelectedValue));
                                _logger.InfoFormat("companyinfo.Id  2 :" + companyinfo.Id);
                                var superUser = Facade.GetSuperUserId(Convert.ToInt32(ddlCompanyId.SelectedValue));
                                _logger.InfoFormat("superUser.Id  2 :" + superUser.Count);
                                if (userInfo.LastLoginDate == Convert.ToDateTime("1/1/0001 12:00:00 AM"))
                                {
                                    var smtpInfo = Facade.GetSmtpConfigurations();
                                   // _logger.InfoFormat("smtpInfo  2 :" + smtpInfo.Count);
                                    if (smtpInfo == null)
                                    {
                                        _logger.InfoFormat("smtpInfo is null");
                                        ReGenerateSessionId();
                                        _facade.ValidateCredentialsByLoginType(domainName, Utility.getOriginalEncryData(GetEncryptedPasswordFromSession(), GetStaticGuidFromSession()), Convert.ToInt32(ddlCompanyId.SelectedValue), loginType.ToString());
                                        _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId);
                                        //  ErrorSuccessNotifier.AddErrorMessage("SMTP Not configured. Please contact Administrator");
                                    }
                                    else
                                    {
                                        _logger.InfoFormat("smtpInfo 2 " + smtpInfo.Count);
                                        _logger.InfoFormat("smtpInfo if not null is true 2  ");
                                        try
                                        {
                                            _logger.InfoFormat("smtpInfo if IsCompanyId is false is started : ");
                                            foreach (var smtpDetails in smtpInfo)
                                            {

                                                CP.Helper.EmailManager emailManager = new CP.Helper.EmailManager();
                                                emailManager.From = CryptographyHelper.Md5Decrypt(smtpDetails.UserName);
                                                _logger.InfoFormat("smtpDetails.UserName 2 : " + smtpDetails.UserName);
                                                _logger.InfoFormat("emailManager.From : 2" + emailManager.From);
                                                emailManager.Body = string.Concat("Domain user: ", userInfo.LoginName, " is logged  in successfully into Continuity Patrol at ", userInfo.CreateDate, "<br/>", "\r\n\n\n Regards,", "<br/>", "Perpetuuiti Technosoft Pvt.Ltd");
                                                emailManager.Subject = "Active Ditectory Login Info";
                                                SmtpConfiguration smtpConfig = new SmtpConfiguration();
                                                smtpConfig.SmtpHost = CryptographyHelper.Md5Decrypt(smtpDetails.SmtpHost);
                                                smtpConfig.Port = smtpDetails.Port;
                                                if (!string.IsNullOrEmpty(smtpDetails.Password))
                                                    smtpConfig.Password = CryptographyHelper.Md5Decrypt(smtpDetails.Password);

                                                smtpConfig.EnableSSL = true;
                                                smtpConfig.IsBodyHTML = true;
                                                //  smtpConfig.UserName = companyinfo.Email;
                                                foreach (var info in superUser)
                                                {
                                                    smtpConfig.UserName = info.UserInformation.Email;
                                                    string output = emailManager.SendTestMail(smtpConfig);
                                                }

                                                ReGenerateSessionId();
                                                _facade.ValidateCredentialsByLoginType(domainName, Utility.getOriginalEncryData(GetEncryptedPasswordFromSession(), GetStaticGuidFromSession()), Convert.ToInt32(ddlCompanyId.SelectedValue), loginType.ToString());
                                                //_facade.ValidateCredentialsByLoginType(domainName, Decodeval(PassEncyptHidden.Value), Convert.ToInt32(ddlCompanyId.SelectedValue), loginType.ToString());
                                                _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId);

                                            }
                                        }
                                        catch (Exception exe)
                                        {
                                            _logger.Error("Exception in smtpInfo if IsCompanyId is false  :" + exe.InnerException);
                                        }
                                    }
                                }
                                else
                                {
                                    ReGenerateSessionId();
                                    //_facade.ValidateCredentialsByLoginType(domainName, Decodeval(PassEncyptHidden.Value), Convert.ToInt32(ddlCompanyId.SelectedValue), loginType.ToString());
                                    _facade.ValidateCredentialsByLoginType(domainName, Utility.getOriginalEncryData(GetEncryptedPasswordFromSession(), GetStaticGuidFromSession()), Convert.ToInt32(ddlCompanyId.SelectedValue), loginType.ToString());
                                    _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId);
                                }
                            }
                        }

                    }//here isauth is finishing
                    else
                    {
                        _logger.InfoFormat("AD user not authenticated " + UserEncrypt.Value + " " + " with domain " + combobox1.Value, CpExceptionType.ADInvalidADAuthentication);
                        throw new CpException(CpExceptionType.ADInvalidADAuthentication);

                    }// here is isauth is finishing
                    //}//here is locked is ending
                    //else
                    //{
                    //    throw new CpException(CpExceptionType.ADAccountIsLocked);
                    //}

                } // above userinfo
                else
                {
                    if (userDetail != null)
                    {
                        orignalStr = CryptographyHelper.Md5Decrypt(userDetail.LoginPassword);

                        // Debug logging for password hash comparison
                        var sessionGuid = GetStaticGuidFromSession();
                        var sessionPassword = GetEncryptedPasswordFromSession();

                        _logger.InfoFormat("=== PASSWORD HASH DEBUG START ===");
                        _logger.InfoFormat("Original password from DB: {0}", orignalStr);
                        _logger.InfoFormat("Session GUID: {0}", sessionGuid);
                        _logger.InfoFormat("Session encrypted password: {0}", sessionPassword);

                        userEncryptPass = Utility.getHashKeyByString(orignalStr, sessionGuid);
                        _logger.InfoFormat("Server-generated hash: {0}", userEncryptPass);
                        _logger.InfoFormat("Hash lengths - Server: {0}, Session: {1}", userEncryptPass?.Length ?? 0, sessionPassword?.Length ?? 0);
                        _logger.InfoFormat("Hashes match: {0}", userEncryptPass?.Equals(sessionPassword) ?? false);
                        _logger.InfoFormat("=== PASSWORD HASH DEBUG END ===");

                        HttpContext.Current.Session["Ad"] = "admin";
                        if (userEncryptPass.Equals(sessionPassword))
                        {
                            if (ConfigurationManager.AppSettings["IsCaptchaRequired"].Equals("true", StringComparison.OrdinalIgnoreCase))
                            {
                                captcha1.ValidateCaptcha(txtcaptcha.Text.Trim());
                            }
                            //if(captcha1.ValidateCaptcha(txtcaptcha.Text.Trim()))
                            if (!captcha1.UserValidated)
                            {
                                ShowErrorDiv("Please Enter valid Captcha".ToUpper());
                            }
                            else
                            {
                                if (ConfigurationManager.AppSettings["IsCompanyId"].Equals("false"))
                                {
                                    ReGenerateSessionId();
                                    //_facade.ValidateCredentials(CryptographyHelper.Md5Encrypt(UserEncrypt.Value), CryptographyHelper.Md5Encrypt(Decodeval(PassEncyptHidden.Value)), Convert.ToInt32(ddlCompanyId.SelectedValue));
                                    _facade.ValidateCredentials(CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(UserEncrypt.Value, GetStaticGuidFromSession())), CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(userEncryptPass, GetStaticGuidFromSession())), Convert.ToInt32(ddlCompanyId.SelectedValue));
                                    bool result = _facade.UpdateUserSessionIdByID(userDetail.Id, newSessionId);
                                }
                                else
                                {
                                    ReGenerateSessionId();
                                    _facade.ValidateCredentials(CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(UserEncrypt.Value, GetStaticGuidFromSession())), CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(userEncryptPass, GetStaticGuidFromSession())), cId);
                                    //_facade.ValidateCredentials(CryptographyHelper.Md5Encrypt(UserEncrypt.Value), CryptographyHelper.Md5Encrypt(Decodeval(PassEncyptHidden.Value)), cId);
                                    _facade.UpdateUserSessionIdByID(userDetail.Id, newSessionId);
                                }
                                string guid = Guid.NewGuid().ToString();
                                Session["LoggedIn"] = Utility.getOriginalEncryData(UserEncrypt.Value, GetStaticGuidFromSession());
                                // Session["AuthToken"] = guid;  
                                HiddenField hdnID = (HiddenField)Page.Master.FindControl("hdAuthToken");
                                hdnID.Value = guid;
                                var cookie = new HttpCookie("AuthToken", guid);
                                cookie.Domain = ConfigurationManager.AppSettings["SessionCookiesDomain"].ToString();
                                cookie.Path = ConfigurationManager.AppSettings["SessionCookiesPath"].ToString();

                                Response.Cookies.Add(new HttpCookie("AuthToken", guid));
                            }
                        }
                        else
                        {
                            ShowErrorDiv("Invalid User Name Or Password".ToUpper());
                            throw new CpException(CpExceptionType.InvalidUserOrPassword);
                        }
                    }
                    else
                    {
                        ShowErrorDiv("Invalid User Name Or Password".ToUpper());

                        /////////////////////////////////////////////////////////////
                        Session.Abandon();
                        Response.Cookies.Add(new HttpCookie("ASP.NET_SessionId", ""));
                        AddCookie();
                        /////////////////////////////////////////////////////////////
                    }

                }
            }
             catch (CpException exc)
            {
                UserName.Focus();
                //  Password.Focus();
                divClass.Visible = true;

                /////////////////////////////////////////////////////////////
                Session.Abandon();
                Response.Cookies.Add(new HttpCookie("ASP.NET_SessionId", ""));
                AddCookie();
                /////////////////////////////////////////////////////////////
                string LoginName = Utility.getOriginalEncryData(UserEncrypt.Value, GetStaticGuidFromSession());
               
                if (chkActiveDirectory.Checked == true)
                {
                    _logger.InfoFormat(" Unable to login with user " + "'" + UserEncrypt.Value + "'" + " and domain " + "'" + combobox1.Value + "'" + " " + exc.Message);
                }
                else
                {
                    _logger.InfoFormat(" Unable to login with user " + "'" + UserEncrypt.Value + "'" + " " + exc.Message);
                }

                if (exc.ExceptionType == CpExceptionType.URAccountLocked)
                {
                    FailureText.Text = exc.Message.ToUpper();
                    _logger.InfoFormat("Error while login" + FailureText.Text);
                }
                else if (exc.ExceptionType == CpExceptionType.CommonUnhandled)
                {
                    _logger.InfoFormat("Error while login" + exc.Message);
                    ExceptionManager.Manage(exc);
                }
                else
                {
                    //int attempt = GetLoginAttempts();
                                 
                    if(userDetail != null)
                    {
                        int attempt = userDetail.Accessrole == string.Empty ? GetLoginAttempts() : 0;
                        if (attempt == MaxLoginAttempt)
                        {
                            if (chkActiveDirectory.Checked == false)
                            {
                                if (_facade.LockUserAccount(LoginName))
                                {
                                    FailureText.Text = "Your account is locked.Contact your Administrator.".ToUpper();
                                    _logger.ErrorFormat("{0} - Account is locked due to unsuccessful login limit exceeded - {1}", HostAddress, UserEncrypt.Value);
                                }
                            }
                            else
                            {
                                FailureText.Text = exc.Message.ToUpper() + "" + UserEncrypt.Value;
                                _logger.InfoFormat("AD user not authenticated " + UserEncrypt.Value + " with domain" + combobox1.Value, FailureText.Text);
                                //    FailureText.Text = "Your account is locked.Contact your Administrator.".ToUpper();
                                //    _logger.ErrorFormat("{0} - Account is locked due to unsuccessful login limit exceeded - {1}", HostAddress, UserName.Text);
                            }
                        }
                        else
                        {
                            FailureText.Text = exc.Message.ToUpper();
                            //_logger.ErrorFormat("{0} - {1} - {2}", HostAddress, exc.Message, UserEncrypt.Value);
                            _logger.ErrorFormat("{0} - Please Check Username and Password ", HostAddress);
                        }
                        //This else condition added by Naren && Miru for AD invalid username and password isssue 
                    }
                    else
                    {
                        FailureText.Text = exc.Message.ToUpper();
                        if (FailureText.Text.Contains("FOR")) { FailureText.Text = FailureText.Text.Replace("FOR", ""); };
                        {
                            _logger.ErrorFormat("{0} - Please Check Username and Password ");
                        }
                    }
                    
                }
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured while login", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }
        public void ShowErrorDiv(string msg)
        {
            divClass.Visible = true;
            lblerror.Visible = false;
            FailureText.Text = msg;
            UserName.Focus();
        }

        protected void BtnContinueClick(object sender, EventArgs e)
        {
            try
            {
                if (txtKey.Text != "" && rbKey.Checked && chkKey.Checked)
                {
                    string activekey = txtKey.Text;

                    string decryptString = DecryptString(activekey);

                    if (string.IsNullOrEmpty(decryptString))
                    {
                        companymac.Visible = true;

                        return;
                    }
                    string[] check = decryptString.Split('/');
                    _companyname = check[1];
                    string ipAddress = check[3];
                    if (CheckCompanyNameExist())
                    {
                        if (ipAddress == GetMacAddress())
                        {
                            CreateLicenceKeyFile(activekey);
                            var licence = new Licencekey();
                            IList<Licencekey> str = Facade.GetAllLicenceKeies();

                            if (str != null)
                            {
                                licence.Id = 1;
                                licence.Key = activekey;
                                licence.ValidFrom = DateTime.Now;
                                licence.ValidTo = DateTime.Now.AddYears(50);
                                licence.Status = 1;
                                licence.UpdatorId = 1;
                                var update = Facade.UpdateLicencekey(licence);
                                licensebg.Visible = false;
                            }
                            else
                            {
                                licence.Key = activekey;
                                licence.ValidFrom = DateTime.Now;
                                licence.ValidTo = DateTime.Now.AddYears(50);
                                licence.Status = 1;
                                licence.CreatorId = 1;
                                var test = Facade.AddLicenceKey(licence);
                                licensebg.Visible = false;
                            }
                        }
                        else
                        {
                            companymac.Visible = true;
                        }
                    }
                    else
                    {
                        companymac.Visible = true;
                    }
                }
                else
                {
                    SelectAll.Visible = true;
                }
            }
            catch (Exception)
            {
            }
        }

        public override void PrepareView()
        {
        }


        [WebMethod]
        public static string DiscoverDomains()
        {
            try
            {
                StringBuilder _domain = new StringBuilder();
                var returnValue = "";
                List<Domain> _domains = new List<Domain>();

                Forest currentForest = Forest.GetCurrentForest();
                DomainCollection domains = currentForest.Domains;
                //foreach (Domain objDomain in domains)
                //{
                //    _domains.Add(objDomain);

                //    returnValue = string.Format("{0}{1}", returnValue, objDomain.Name);
                //    _logger.Info("Discovered domains are " + returnValue);
                //    return returnValue.TrimEnd(',');
                //}
                //return returnValue;
                foreach (Domain objDomain in domains)
                {
                    _domains.Add(objDomain);
                    _domain.Append(objDomain.Name);
                    _domain.Append(",");
                    // _domains.Add(objDomain);
                    // _domain.Append(",petc,petch12");
                    //returnValue = string.Format("{0}{1}", returnValue, objDomain.Name);
                    _logger.Info("Discovered domains are " + objDomain.Name);
                    //return returnValue.TrimEnd(',');
                    // return returnValue;
                }
                //return returnValue;
                returnValue = string.Format("{0}{1}", returnValue, _domain.ToString());
                returnValue.Remove(returnValue.Length - 1, 1);
                return returnValue;
                // return null;

            }
            catch (Exception ex)
            {
                // _logger.Info("Exception Occured while populating company names :" + ex.InnerException.Message);
                // ActivityLogger.
                _logger.Info("Discover domains return empty domains");
                return null;
            }
        }

        public static bool AuthenticateADUser(string _domainName, string _domainUser, string _domianPassword)
        {
            try
            {
                Forest currentForest = Forest.GetCurrentForest();
                DomainCollection domains = currentForest.Domains;

                using (PrincipalContext pc = new PrincipalContext(ContextType.Domain, _domainName))
                {
                    bool isValid = pc.ValidateCredentials(_domainUser, _domianPassword);

                    if (isValid)
                    {
                        _logger.Info("User " + _domainUser + " authenticated with domain " + _domainName);
                        return true;
                    }
                    else
                    {
                        _logger.Info("User " + _domainUser + " not authenticated with domain " + _domainName);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Info("Authentication failed for: " + _domainUser + " " + " with domain " + _domainName + " with error message " + ex.Message);
                return false;
            }
        }


        protected void chkActiveDirectory_CheckedChanged(object sender, EventArgs e)
        {
            if (chkActiveDirectory.Checked)
            {
                combobox1.Visible = true;
                lbldomainName.Visible = true;
                //combobox1.Visible = true;
                pnlDomain.Visible = true;


            }
            else
            {
                combobox1.Visible = false;
                // combobox1.Visible = false;
                lbldomainName.Visible = false;
                pnlDomain.Visible = false;
                FailureText.Visible = false;
                divClass.Visible = false;
            }
        }


        public static bool IsAccountLocked(string _domainName, string _domainUser)
        {
            try
            {
                bool IsLocked = false;
                PrincipalContext ctx = new PrincipalContext(ContextType.Domain);
                UserPrincipal user = UserPrincipal.FindByIdentity(ctx, _domainUser);
                if (user != null)
                {
                    string displayName = user.DisplayName;

                    if (user.IsAccountLockedOut())
                    {
                        IsLocked = true;

                    }
                    else
                        IsLocked = false;
                }
                return IsLocked;
            }
            catch (Exception ex)
            {
                return true;
            }
        }

        protected string Decodeval(string input)
        {
            var decode = "";
            var base64EncodedBytes = System.Convert.FromBase64String(input);
            decode = System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
            return decode;

        }
        /// <summary>
        /// Generate New Session ID
        /// </summary>
        protected void ReGenerateSessionId()
        {
            try
            {
                newSessionId = string.Empty;
                SessionIDManager manager = new SessionIDManager();
                string oldId = manager.GetSessionID(Context);
                string newId = manager.CreateSessionID(Context);
                bool isAdd = false, isRedir = false;
                manager.RemoveSessionID(Context);
                manager.SaveSessionID(Context, newId, out isRedir, out isAdd);



                HttpApplication ctx = (HttpApplication)HttpContext.Current.ApplicationInstance;
                HttpModuleCollection mods = ctx.Modules;
                System.Web.SessionState.SessionStateModule ssm = (SessionStateModule)mods.Get("Session");
                System.Reflection.FieldInfo[] fields = ssm.GetType().GetFields(BindingFlags.NonPublic | BindingFlags.Instance);
                SessionStateStoreProviderBase store = null;
                System.Reflection.FieldInfo rqIdField = null, rqLockIdField = null, rqStateNotFoundField = null;



                SessionStateStoreData rqItem = null;
                foreach (System.Reflection.FieldInfo field in fields)
                {
                    if (field.Name.Equals("_store")) store = (SessionStateStoreProviderBase)field.GetValue(ssm);
                    if (field.Name.Equals("_rqId")) rqIdField = field;
                    if (field.Name.Equals("_rqLockId")) rqLockIdField = field;
                    if (field.Name.Equals("_rqSessionStateNotFound")) rqStateNotFoundField = field;

                    if ((field.Name.Equals("_rqItem")))
                    {
                        rqItem = (SessionStateStoreData)field.GetValue(ssm);
                    }
                }
                object lockId = rqLockIdField.GetValue(ssm);

                if ((lockId != null) && (oldId != null))
                {
                    store.RemoveItem(Context, oldId, lockId, rqItem);
                }

                rqStateNotFoundField.SetValue(ssm, true);
                rqIdField.SetValue(ssm, newId);
                newSessionId = newId;

            }
            catch (Exception ex)
            {
                string msg = ex.InnerException != null ? ex.InnerException.Message : string.Empty;
                _logger.Info("Error ouccred while executing ReGenerateSessionId() : " + ex.Message + msg + ex.StackTrace);

            }
        }
    }
}