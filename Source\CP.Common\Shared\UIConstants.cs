﻿namespace CP.Common.Shared
{
    public sealed partial class Constants
    {
        public static class UIConstants
        {
            public const string DropDownlItemPleaseSelectBusinessService = "- Select Business Service -";
            public const string DropDownlItemPleaseSelectBusinessFunction = "- Select Business Function -";
            public const string DropDownlItemPleaseSelectBusinessType = "- Select Type -";
            public const string DropDownlItemPleaseSelectServerType = "- Select Server Type-";
            public const string DropDownlItemPleaseSelectSiteType = "- Select Site Type -";
            public const string DropDownlItemPleaseSelectGroupName = "- Select InfraObject Name -";
            public const string DropDownlItemPleaseSelectCompanyName = "- Select Company Name -";
            public const string DropDownlItemPleaseSelectSiteName = "- Select Site -";
            public const string DropDownlItemPleaseSelectServerName = "- Select Server Name -";
            public const string DropDownlItemPleaseSelectHmcServerName = "- Select HMCServer Name -";
            public const string DropDownlItemPleaseSelectApplicationName = "- Select Application Name -";
            public const string DropDownItemPleaseSelectReplicationName = "- Select Replication Name -";
            public const string DropDownItemPleaseSelectDBName = "- Select DataBase Name -";
            public const string DropDownItemPleaseSelectDBPassword = "- Select Password Type-";
            public const string DropDownlItemPleaseSelectStorageimageName = "- Select StorageImage Name -";
            public const string DropDownlItemPleaseSelectInfraObjectWithStorage = "- Select InfraObjectWithStorageImage Name -";
            public const string DropDownItemPleaseSelectDBVersion = "- Select DB Version-";
            public const string DropDownItemPleaseSelectJobName = "- Select Job Name -";
            public const string DropDownlItemPleaseSelectCompanyCode = "- Select Company Code-";
            public const string DropDownlItemPleaseSelectParallelworkflow = "-Select Parallel Workflow-";
            public const string DropDownlItemPleaseSelectParallalprofile = "- Select Parallel Profile -";
            public const string DropDownlItemPleaseSelectDroperationName = "- Select DROperation Name -";
            public const string DropDownlItemPleaseSelectArchiveName = "- Select Table Name -";
            public const string DropDownlItemPleaseSelectWorkflowName = "- Select Workflow Name -";
            public const string DropDownlItemPleaseSelectInfraName = "- Select InfraObject -";
            public const string DropDownlItemPleaseSelectBusinessServicePriority = "- Select Business Service Priority-";
            public const string DropDownlItemPleaseSelectDataSyncProperties = "- Select DataSync Properties -";
            public const string DropDownlItemPleaseSelectImpact = "- Select Impact -";
            public const string DropDownlItemPleaseSelectAuthentication = "- Select Authentication Type -";
            public const string TextBoxDataLagValue = "RPO Time Selected";
            public const string DropDownlItemPleaseSelectDiscoveryProfile = "Select Discovery Profile";
            public const string DropDownItemPleaseSelectWorkflowName = "- Select Workflow Name -";
            public const string DropDownlItemPleaseSelectDependencyProfile = "Select Dependency Profile";
            public const string DropDownItemPleaseSelectImpactTypeMaster = "- Select Impact Category Name - ";
            public const string DropDownItemPleaseSelectUser = "- Select User - ";
            public const string DropDownItemPleaseSelectSSOProfile = "- Select SSO Profile - ";
            public const string DropDownItemeBDRProfile = "- Select eBDR Profile - ";
            public const string DropDownItemPleaseSelectBusinessServiceAll = "All";
            public const string DropDownlItemPleaseSelectClusterProfileName = "- Select Cluster Name -";
            public const string DropDownlItemPleaseSelectType = "- Select Type -";
            public const string DropDownItemPleaseSelectLeapNutanixServerName = "-Select Nutanix Leap Server Name-";
        }
    }
}