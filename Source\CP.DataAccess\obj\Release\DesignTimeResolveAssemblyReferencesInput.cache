   .winmd.dll.exe )   ;D:\TCS\TCS_Internal\UI\bin\Release\BCMS.AlertController.dll;D:\TCS\TCS_Internal\UI\bin\Release\BCMS.AlertController.pdbJD:\TCS\TCS_Internal\UI\bin\Release\Microsoft.Practices.ServiceLocation.dll@D:\TCS\TCS_Internal\UI\bin\Release\Microsoft.Practices.Unity.dllMD:\TCS\TCS_Internal\UI\bin\Release\Microsoft.Practices.Unity.Interception.dll'D:\TCS\TCS_Internal\UI\release.licenses<D:\TCS\TCS_Internal\UI\ApplicationDependency\BCMS.Common.pfx5D:\TCS\TCS_Internal\UI\DataBaseMySql\.svn\all-wcprops1D:\TCS\TCS_Internal\UI\DataBaseMySql\.svn\entriesTD:\TCS\TCS_Internal\UI\DataBaseMySql\.svn\text-base\DatabaseMySqlBuilder.cs.svn-baseZD:\TCS\TCS_Internal\UI\DataBaseMySql\.svn\text-base\DatabaseMySqlDataAccess.cs.cs.svn-base:D:\TCS\TCS_Internal\UI\DB2DataSyncMonitor\.svn\all-wcprops6D:\TCS\TCS_Internal\UI\DB2DataSyncMonitor\.svn\entries^D:\TCS\TCS_Internal\UI\DB2DataSyncMonitor\.svn\text-base\DB2DataSyncMonitorBuilder.cs.svn-baseaD:\TCS\TCS_Internal\UI\DB2DataSyncMonitor\.svn\text-base\DB2DataSyncMonitorDataAccess.cs.svn-base@D:\TCS\TCS_Internal\UI\MySqlGlobalMirrorMonitor\.svn\all-wcprops<D:\TCS\TCS_Internal\UI\MySqlGlobalMirrorMonitor\.svn\entriesjD:\TCS\TCS_Internal\UI\MySqlGlobalMirrorMonitor\.svn\text-base\MySqlGlobalMirrorMonitorBuilder.cs.svn-basemD:\TCS\TCS_Internal\UI\MySqlGlobalMirrorMonitor\.svn\text-base\MySqlGlobalMirrorMonitorDataAccess.cs.svn-baseSD:\TCS\TCS_Internal\UI\Source\CP.CacheController\bin\Release\CP.CacheController.dllAD:\TCS\TCS_Internal\UI\Source\CP.Common\bin\Release\CP.Common.dllUD:\TCS\TCS_Internal\UI\Source\CP.ExceptionHandler\bin\Release\CP.ExceptionHandler.dllAD:\TCS\TCS_Internal\UI\Source\CP.Helper\bin\Release\CP.Helper.dllKD:\TCS\TCS_Internal\UI\Source\CP.IDataAccess\bin\Release\CP.IDataAccess.dllCD:\TCS\TCS_Internal\UI\Source\CP.IFacade\bin\Release\CP.IFacade.dll8D:\TCS\TCS_Internal\UI\Thirdparty\Devart\Devart.Data.dll?D:\TCS\TCS_Internal\UI\Thirdparty\Devart\Devart.Data.Oracle.dll\D:\TCS\TCS_Internal\UI\Thirdparty\Microsoft\Microsoft.Practices.EnterpriseLibrary.Common.dllZD:\TCS\TCS_Internal\UI\Thirdparty\Microsoft\Microsoft.Practices.EnterpriseLibrary.Data.dllQD:\TCS\TCS_Internal\UI\Thirdparty\Microsoft\Microsoft.Practices.ObjectBuilder.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\mscorlib.dll6D:\TCS\TCS_Internal\UI\Thirdparty\Postgress\npgsql.dllOD:\TCS\TCS_Internal\UI\Thirdparty\OracleManageData\Oracle.ManagedDataAccess.dllkC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\System.Configuration.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\System.Core.dlltC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\System.Data.DataSetExtensions.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\System.Data.dll]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\System.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\System.Web.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\System.Xml.dllfC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\System.Xml.Linq.dll       SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}B{Registry:Software\Microsoft\.NETFramework,v4.5,AssemblyFoldersEx}
{RawFileName}8D:\TCS\TCS_Internal\UI\Source\CP.DataAccess\bin\Release\     B{Registry:Software\Microsoft\.NETFramework,v4.5,AssemblyFoldersEx}aD:\TCS\TCS_Internal\UI\Source\CP.DataAccess\obj\Release\DesignTimeResolveAssemblyReferences.cache   SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\[C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\Facades\.NETFramework,Version=v4.5.NET Framework 4.5v4.5msil
v4.0.30319         