﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DropDown", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class DropDown : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Id { get; set; }

        #endregion Properties
    }
}