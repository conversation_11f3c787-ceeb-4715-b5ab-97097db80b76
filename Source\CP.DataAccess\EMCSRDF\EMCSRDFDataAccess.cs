﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class EmcSrdfDataAccess : BaseDataAccess, IEMCSRDFDataAccess
    {
        #region Constructors

        public EmcSrdfDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<EmcSrdf> CreateEntityBuilder<EmcSrdf>()
        {
            return (new EmcSrdfBuilder()) as IEntityBuilder<EmcSrdf>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="DataGuardMonitor" /> bcms_emc_srdf table.
        /// </summary>
        /// <param name="emcSrdf">EmcSrdf</param>
        /// <returns>EmcSrdf</returns>
        /// <author><PERSON></author>
        EMCSRDF IEMCSRDFDataAccess.Add(EMCSRDF emcSrdf)
        {
            try
            {
                const string sp = "EMCSRDF_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iReplicationId", DbType.Int32, emcSrdf.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring+"iServerId", DbType.Int32, emcSrdf.ServerId);
                    Database.AddInParameter(cmd, Dbstring+"iDGroupName", DbType.String, emcSrdf.DGroupName);
                    Database.AddInParameter(cmd, Dbstring+"iDGType", DbType.String, emcSrdf.DGType);
                    Database.AddInParameter(cmd, Dbstring+"iDGSummetrixID", DbType.String, emcSrdf.DGSummetrixID);
                    Database.AddInParameter(cmd, Dbstring+"iRemoteSymmetrixID", DbType.String, emcSrdf.RemoteSymmetrixID);
                    Database.AddInParameter(cmd, Dbstring+"iRDF_RA_GroupNumber", DbType.String, emcSrdf.RdfRaGroupNumber);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        emcSrdf = reader.Read() ? CreateEntityBuilder<EMCSRDF>().BuildEntity(reader, emcSrdf) : null;
                    }
                    return emcSrdf;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting EmcSrdf Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Upadet <see cref="DataGuardMonitor" /> into bcms_emc_srdf table.
        /// </summary>
        /// <param name="emcSrdf">EmcSrdf</param>
        /// <returns>EmcSrdf</returns>
        /// <author>Kiran Ghadge</author>
        EMCSRDF IEMCSRDFDataAccess.Update(EMCSRDF emcSrdf)
        {
            try
            {
                const string sp = "EMCSRDF_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, emcSrdf.Id);
                    Database.AddInParameter(cmd, Dbstring+"iReplicationId", DbType.String, emcSrdf.ReplicationId);

                    Database.AddInParameter(cmd, Dbstring+"iServerId", DbType.Int32, emcSrdf.ServerId);
                    Database.AddInParameter(cmd, Dbstring+"iDGroupName", DbType.String, emcSrdf.DGroupName);

                    Database.AddInParameter(cmd, Dbstring+"iDGType", DbType.String, emcSrdf.DGType);
                    Database.AddInParameter(cmd, Dbstring+"iDGSummetrixID", DbType.String, emcSrdf.DGSummetrixID);
                    Database.AddInParameter(cmd, Dbstring+"iRemoteSymmetrixID", DbType.String, emcSrdf.RemoteSymmetrixID);
                    Database.AddInParameter(cmd, Dbstring+"iRDF_RA_GroupNumber", DbType.String, emcSrdf.RdfRaGroupNumber);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        emcSrdf = reader.Read() ? CreateEntityBuilder<EMCSRDF>().BuildEntity(reader, emcSrdf) : null;
                    }
                    return emcSrdf;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating EmcSrdf Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        EMCSRDF IEMCSRDFDataAccess.UpdateByReplicationId(EMCSRDF emcSrdf)
        {
            try
            {
                const string sp = "EMCSRDF_UpdateByReplicationId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iReplicationId", DbType.String, emcSrdf.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring+"iServerId", DbType.Int32, emcSrdf.ServerId);
                    Database.AddInParameter(cmd, Dbstring+"iDGroupName", DbType.String, emcSrdf.DGroupName);
                    Database.AddInParameter(cmd, Dbstring+"iDGType", DbType.String, emcSrdf.DGType);
                    Database.AddInParameter(cmd, Dbstring+"iDGSummetrixID", DbType.String, emcSrdf.DGSummetrixID);
                    Database.AddInParameter(cmd, Dbstring+"iRemoteSymmetrixID", DbType.String, emcSrdf.RemoteSymmetrixID);
                    Database.AddInParameter(cmd, Dbstring+"iRDF_RA_GroupNumber", DbType.String, emcSrdf.RdfRaGroupNumber);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        emcSrdf = reader.Read() ? CreateEntityBuilder<EMCSRDF>().BuildEntity(reader, emcSrdf) : null;
                    }
                    return emcSrdf;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating EmcSrdf By Replication id Entry " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Check <see cref="DataGuardMonitor" /> from bcms_emc_srdf table by Name.
        /// </summary>
        /// <param name="name">Name of the EmcSrdf</param>
        /// <returns>EmcSrdf</returns>
        /// <author>Kiran Ghadge</author>
        EMCSRDF IEMCSRDFDataAccess.IsExistsByName(string name)
        {
            try
            {
                const string sp = "EMCSRDF_GetbyName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.String, name);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<EMCSRDF>()).BuildEntity(reader, new EMCSRDF())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEMCSRDFDataAccess.IsExistByName (" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DataGuardMonitor" /> from bcms_emc_srdf table by Name by Id.
        /// </summary>
        /// <param name="id">Id of the EmcSrdf</param>
        /// <returns>EmcSrdf</returns>
        /// <author>Kiran Ghadge</author>
        EMCSRDF IEMCSRDFDataAccess.GetById(int id)
        {
            try
            {
                const string sp = "EMCSRDF_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<EMCSRDF>()).BuildEntity(reader, new EMCSRDF());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEMCSRDFDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        EMCSRDF IEMCSRDFDataAccess.GetByReplicationId(int id)
        {
            try
            {
                const string sp = "EMCSRDF_GetByReplicationId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<EMCSRDF>()).BuildEntity(reader, new EMCSRDF());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEMCSRDFDataAccess.GetByReplicationId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     GetAll <see cref="DataGuardMonitor" /> from bcms_emc_srdf table by Name.
        /// </summary>
        /// <returns>EmcSrdf List</returns>
        /// <author>Kiran Ghadge</author>
        IList<EMCSRDF> IEMCSRDFDataAccess.GetAll(string replicationtype)
        {
            try
            {
                const string sp = "EMCSRDF_GETALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iTypeId", DbType.AnsiString, replicationtype);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildEMCSRDFEntities(reader, replicationtype);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEMCSRDFDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<EMCSRDF> IEMCSRDFDataAccess.GetByCompanyId(int companyId, bool isParent, string replicationtype)
        {
            try
            {
                const string sp = "EMCSRDF_GetByCompanyId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iCompanyId", DbType.Int32, companyId);
                    Database.AddInParameter(cmd, Dbstring+"iisParent", DbType.Int32, isParent);
                    Database.AddInParameter(cmd, "TypeId", DbType.String, replicationtype);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildEMCSRDFEntities(reader, replicationtype);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEMCSRDFDataAccess.GetByCompanyId(" + companyId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="DataGuardMonitor" /> from bcms_emc_srdf table by Name by Id.
        /// </summary>
        /// <param name="id">Id of the EmcSrdf</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IEMCSRDFDataAccess.DeleteById(int id)
        {
            try
            {
                const string sp = "EmcSrdf_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting EmcSrdf Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        IList<EMCSRDF> IEMCSRDFDataAccess.GetEmcSrdfsDatalagByReplicationId(int replicationId)
        {
            try
            {
                const string sp = "EMCSRDFDataLag_GetByReplicationId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iReplicationId", DbType.Int32, replicationId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildEMCSRDFsDatalagEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEMCSRDFDataAccess.GetEmcSrdfsDatalagByReplicationId(" +
                    replicationId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        private IList<EMCSRDF> BuildEMCSRDFEntities(IDataReader reader, string replicationtype)
        {
            var emcsrdfs = new List<EMCSRDF>();

            while (reader.Read())
            {
                var type = Convert.ToString(reader["Type"]);
                if (Convert.ToString(reader["Type"]).Trim() == replicationtype)
                {
                    var emc = new EMCSRDF
                    {
                        ReplicationBase =
                        {
                            Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                            Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                            // reptype = reader.IsDBNull(2) ? string.Empty : reader.GetString(2)\
                            Type = Convert.IsDBNull(reader["Type"])
                ? ReplicationType.Undefined
                : (ReplicationType)Enum.Parse(typeof(ReplicationType), Convert.ToString(reader["Type"]), true)
                        }
                    };

                    emc.DGroupName = Convert.IsDBNull(reader["DGroupName"])
                ? string.Empty
                : Convert.ToString(reader["DGroupName"]);
                    emc.DGType = Convert.IsDBNull(reader["DGType"]) ? string.Empty : Convert.ToString(reader["DGType"]);
                    emc.DGSummetrixID = Convert.IsDBNull(reader["DGSummetrixID"])
                ? string.Empty
                : Convert.ToString(reader["DGSummetrixID"]);
                    emc.RemoteSymmetrixID = Convert.IsDBNull(reader["RemoteSymmetrixID"])
                ? string.Empty
                : Convert.ToString(reader["RemoteSymmetrixID"]);
                    emc.State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]);
                    emc.PendingTracks = Convert.IsDBNull(reader["PendingTracks"])
                ? string.Empty
                : Convert.ToString(reader["PendingTracks"]);
                    emcsrdfs.Add(emc);
                }
            }

            return (emcsrdfs.Count > 0) ? emcsrdfs : null;
        }

        private IList<EMCSRDF> BuildEMCSRDFsDatalagEntities(IDataReader reader)
        {
            var emcsrdfs = new List<EMCSRDF>();

            while (reader.Read())
            {
                var emc = new EMCSRDF();
                emc.DataLag = Convert.IsDBNull(reader["DataLag"]) ? string.Empty : Convert.ToString(reader["DataLag"]);
                emc.State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]);
                emc.PendingTracks = Convert.IsDBNull(reader["PendingTracks"])
                    ? string.Empty
                    : Convert.ToString(reader["PendingTracks"]);
                emc.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                    ? DateTime.MinValue
                    : Convert.ToDateTime(reader["CreateDate"].ToString());
                emcsrdfs.Add(emc);
            }

            return (emcsrdfs.Count > 0) ? emcsrdfs : null;
        }

        /// <summary>
        ///     GetAll <see cref="EmcSrdfMonitor" /> from bcms_emc_srdf table by Name.
        /// </summary>
        /// <returns>EmcSrdf List</returns>
        /// <author>Ranjithsingh</author>
        IList<EMCSRDF> IEMCSRDFDataAccess.GetByDate(int iInfraId, string startDate, string endDate)
        {
            IList<EMCSRDF> OracleList = new List<EMCSRDF>();
            try
            {
           const string sp = "EMCSRDFS_GETBYDATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    //startDate = Convert.ToDateTime(startDate).ToString("dd-MMM-yy");
                    //endDate = Convert.ToDateTime(endDate).ToString("dd-MMM-yy");
#endif
                    Database.AddInParameter(cmd, Dbstring + "iInfraId", DbType.Int32, iInfraId);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.String, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.String, endDate);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var srdf = new EMCSRDF();
                            srdf.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            srdf.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                                ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);
                            srdf.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                            srdf.DGroupName = Convert.IsDBNull(reader["DGroupName"])
                                ? string.Empty
                                : Convert.ToString(reader["DGroupName"]);
                            srdf.DGType = Convert.IsDBNull(reader["DGType"]) ? string.Empty : Convert.ToString(reader["DGType"]);
                            srdf.DGSummetrixID = Convert.IsDBNull(reader["DGSummetrixID"])
                                ? string.Empty
                                : Convert.ToString(reader["DGSummetrixID"]);
                            srdf.RemoteSymmetrixID = Convert.IsDBNull(reader["RemoteSymmetrixID"])
                                ? string.Empty
                                : Convert.ToString(reader["RemoteSymmetrixID"]);
                            srdf.RdfRaGroupNumber = Convert.IsDBNull(reader["RDF_RA_GroupNumber"])
                                ? string.Empty
                                : Convert.ToString(reader["RDF_RA_GroupNumber"]);
                            srdf.State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]);
                            srdf.PendingTracks = Convert.IsDBNull(reader["PendingTracks"])
                                ? string.Empty
                                : Convert.ToString(reader["PendingTracks"]);
                            srdf.DataLag = Convert.IsDBNull(reader["DataLag"]) ? string.Empty : Convert.ToString(reader["DataLag"]);
                            srdf.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);

                            srdf.R2_IMAGE_CAPTURE_TIME = Convert.IsDBNull(reader["R2_IMAGE_CAPTURE_TIME"]) ? string.Empty : Convert.ToString(reader["R2_IMAGE_CAPTURE_TIME"]);
                            OracleList.Add(srdf);
                        }
                        return OracleList;
                    }
                }
            }


            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEMCSRDFDataAccess.GetByDate" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
            return OracleList;
        }


        IList<EMCSRDF> IEMCSRDFDataAccess.GetEmcSrdfsHourly_ByInfraid(int replicationId)
        {
            IList<EMCSRDF> EMCSRDFlist = new List<EMCSRDF>();
            try
            {
                const string sp = "EMCSRDF_GETBYHRSID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iReplicationId", DbType.Int32, replicationId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        //return CreateEntityBuilder<EMCSRDF>().BuildEntities(reader);
                        while (reader.Read())
                        {
                            EMCSRDF srdf = new EMCSRDF();
                            srdf.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            srdf.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                                ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);
                            //srdf.Name = Convert.IsDBNull(reader["Name"])
                            //    ? string.Empty
                            //    : Convert.ToString(reader["Name"]);
                            srdf.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                            srdf.DGroupName = Convert.IsDBNull(reader["DGroupName"])
                                ? string.Empty
                                : Convert.ToString(reader["DGroupName"]);
                            srdf.DGType = Convert.IsDBNull(reader["DGType"]) ? string.Empty : Convert.ToString(reader["DGType"]);
                            srdf.DGSummetrixID = Convert.IsDBNull(reader["DGSummetrixID"])
                                ? string.Empty
                                : Convert.ToString(reader["DGSummetrixID"]);
                            srdf.RemoteSymmetrixID = Convert.IsDBNull(reader["RemoteSymmetrixID"])
                                ? string.Empty
                                : Convert.ToString(reader["RemoteSymmetrixID"]);
                            srdf.RdfRaGroupNumber = Convert.IsDBNull(reader["RDF_RA_GroupNumber"])
                                ? string.Empty
                                : Convert.ToString(reader["RDF_RA_GroupNumber"]);
                            srdf.State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]);
                            srdf.PendingTracks = Convert.IsDBNull(reader["PendingTracks"])
                                ? string.Empty
                                : Convert.ToString(reader["PendingTracks"]);
                            srdf.DataLag = Convert.IsDBNull(reader["DataLag"]) ? string.Empty : Convert.ToString(reader["DataLag"]);
                            srdf.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);

                            srdf.R2_IMAGE_CAPTURE_TIME = Convert.IsDBNull(reader["R2_IMAGE_CAPTURE_TIME"]) ? string.Empty : Convert.ToString(reader["R2_IMAGE_CAPTURE_TIME"]);

                            EMCSRDFlist.Add(srdf);
                        }
                    }
                    return EMCSRDFlist;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEMCSRDFDataAccess.GetEmcSrdfsHourly_ByInfraid" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);

                return EMCSRDFlist = null;
            }
        }

        /// <summary>
        ///     GetAll <see cref="EmcSrdfMonitor" /> from bcms_emc_srdf table by Name.
        /// </summary>
        /// <returns>EmcSrdf List</returns>
        /// <author>Ranjithsingh</author>
        IList<EMCSRDF> IEMCSRDFDataAccess.GetHourlyByReplicationId(int replicationId)
        {
            try
            {
                const string sp = "EMCSRDFGETBYHRS_REPLICATIONID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iReplicationId", DbType.Int32, replicationId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<EMCSRDF>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEMCSRDFDataAccess.GetHourlyByReplicationId" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }                

        private IList<EMCSRDF> BuildEMCSRDFEntities(IDataReader reader)
        {
            var EMCSRDFReplications = new List<EMCSRDF>();

            while (reader.Read())
            {
                var EMCSRDFReplication = new EMCSRDF
                {
                    ReplicationBase =
                    {
                        Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["Id"]),
                        Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                        Type = Convert.IsDBNull(reader["Type"]) ? ReplicationType.Undefined : (ReplicationType)Enum.Parse(typeof(ReplicationType), Convert.ToString(reader["Type"]), true),
                        reptype = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"])
                    }
                };
                EMCSRDFReplication.DGroupName = Convert.IsDBNull(reader["DGroupName"])
                ? string.Empty
                : Convert.ToString(reader["DGroupName"]);
                EMCSRDFReplication.DGType = Convert.IsDBNull(reader["DGType"]) ? string.Empty : Convert.ToString(reader["DGType"]);
                EMCSRDFReplication.DGSummetrixID = Convert.IsDBNull(reader["DGSummetrixID"])
                    ? string.Empty
                    : Convert.ToString(reader["DGSummetrixID"]);
                EMCSRDFReplication.RemoteSymmetrixID = Convert.IsDBNull(reader["RemoteSymmetrixID"])
                    ? string.Empty
                    : Convert.ToString(reader["RemoteSymmetrixID"]);
                EMCSRDFReplication.State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]);
                EMCSRDFReplication.PendingTracks = Convert.IsDBNull(reader["PendingTracks"])
                    ? string.Empty
                    : Convert.ToString(reader["PendingTracks"]);

                EMCSRDFReplications.Add(EMCSRDFReplication);
            }
            return (EMCSRDFReplications.Count > 0) ? EMCSRDFReplications : null;
        }
        IList<EMCSRDF> IEMCSRDFDataAccess.GetByLoginId(string replicationtype, int id)
        {
            try
            {
                const string sp = "EMCSRDFReplication_GetByLoginId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    Database.AddInParameter(cmd, Dbstring + "iTypeId", DbType.AnsiString, replicationtype);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildEMCSRDFEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEMCSRDFDataAccess.GetByLoginId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods
    }
}