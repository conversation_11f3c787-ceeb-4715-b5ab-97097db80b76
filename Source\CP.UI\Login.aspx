<%@ Page Language="C#" MasterPageFile="~/Master/BcmsLogin.Master" AutoEventWireup="true"
    CodeBehind="Login.aspx.cs" Inherits="CP.UI.Login" Title="Welcome to ContinuityPatrol - LogIn"
    ViewStateEncryptionMode="Always" %>

<%@ Register Assembly="MSCaptcha" Namespace="MSCaptcha" TagPrefix="rsv" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="App_Themes/CPTheme/jquery.combobox/style.css" rel="stylesheet" />
    <script src="Script/jquery-3.5.1.min.js"></script>
    <script src="Script/jquery-migrate.min.js"></script>
    <script src="Script/less.min.js"></script>
    <script src="Script/Custom-chkbox-rdbtn.js"></script>
    <script type="text/javascript" src="Script/jquery.combobox.js"></script>
    <script src="Script/Login.js" type="text/javascript"></script>
    <script src="Script/MaskedPassword.js"></script>
    <script src="Script/EncryptDecrypt.js"></script>
    <style type="text/css">
        .pull-left.innerB.margin-right > img {
            height: 33px;
            width: 165px;
        }

        .reset-icon {
            margin-left: 5px;
        }

        .widget {
            box-shadow: 0 0 0 0 #dbdbdb;
            margin: 0px;
        }

            .widget .widget-head {
                /*background-image:linear-gradient(to bottom, #fdfdfd, #f4f4f4);*/
                background-repeat: repeat-x;
                border-bottom: 1px solid #dbdbdb;
                border-radius: 5px 5px 0 0;
                height: 35px;
                line-height: 35px;
                overflow: hidden;
                padding: 0 15px 0 5px;
                position: relative;
                background-color: #d6e6f5; /*#e2f6fd;*/
                color: #333333;
            }

        #captch .widget-body {
            background-image: linear-gradient(to bottom, #fdfdfd, #f4f4f4);
            /*background-color:#f4f4f4;*/
        }

        .combobox {
        width:100%;
        }

        #ctl00_cphBody_pnlDomain #ctl00_cphBody_combobox1 {
            width: 83%;
        }

        .caps-error {
            width: 100%;
            display: block;
        }

        .combobox_button {
            left:84% !important;
        }

    </style>
    <script type="text/javascript">
        $(document).ready(function () {
            $('input[type="checkbox"]').checkbox();
            //$('[id$=ctl00_cphBody_chkLDap]').checkbox();
            //$('[id$=ctl00_cphBody_chkActiveDirectory]').checkbox();
            //$('[id$=ctl00_cphBody_chkSSOSaml]').checkbox();



            return true;
        });






    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <ul id="divClass" runat="server" visible="false" class="notyfy_container i-am-new">
        <li class="notyfy_wrapper notyfy_error">
            <div id="notyfy_1155564924086075600" class="notyfy_bar">
                <div class="notyfy_message">
                    <span class="notyfy_text">
                        <strong>
                            <asp:Literal ID="FailureText" Text="Error message" runat="server" EnableViewState="False"></asp:Literal></strong>
                    </span>
                </div>
            </div>
        </li>
    </ul>
    <div class="row margin-none loginPageBg" onkeypress="checkKey(event)">
        <div class="col-md-5 col-md-push-1">
            <div id="LoginUser" class="margin-top90">
                <div class="widget box-shadow-none border-none">
                    <div class="widget-body" style="margin-top: -30px">
                        <div class="center">
                            <img src="Images/ContinuityPatrol_logo_black1.png" alt="Continuity Patrol" />
                        </div>
                        <div class="row col-md-12 ">
                            <div class="col-md-5 col-md-push-7">
                                <asp:Label ID="lblVersion" runat="server" Text=""></asp:Label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <div class="col-md-1">
                                    </div>
                                    <div class="col-md-11">
                                        <div class="input-icon left">
                                            <label>User Name</label>
                                            <span class="glyphicons user log-ext"><i></i></span>
                                            <input type="hidden" id="UserEncrypt" runat="server" />
                                            <asp:TextBox ID="UserName" runat="server" autocomplete="off" CssClass="form-control" placeholder="Username" onfocus="cleartext()" onblur="getUserNameHash(this)"></asp:TextBox>
                                            <asp:RequiredFieldValidator ID="UserNameRequired" runat="server" ControlToValidate="UserName" ErrorMessage="Required." ToolTip="User Name is required." CssClass="error"
                                                ValidationGroup="LoginUser"></asp:RequiredFieldValidator>
                                        </div>

                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-md-1">
                                    </div>
                                    <div class="col-md-11">
                                        <div class="input-icon left">
                                            <label>Password</label>
                                            <span class="glyphicons keys log-ext1"><i></i></span>
                                            <div>
                                                <input type="hidden" id="passHidden" />
                                                <asp:TextBox ID="Password" runat="server" EnableViewState="false" TextMode="Password" autocomplete="off" CssClass="form-control chk-caps"
                                                     placeholder="Password" ReadOnly="false" onblur="getPasswordHash(this)" onfocus="clearControlData(this)"></asp:TextBox>

                                                <asp:RequiredFieldValidator ID="PasswordRequired" runat="server" ControlToValidate="Password" Display="Dynamic" ErrorMessage="Required." CssClass="error"
                                                    ToolTip="Password is required." ValidationGroup="LoginUser">
                                                 </asp:RequiredFieldValidator>

                                                    <span class="caps-error">Caps Lock is ON.</span>
                                                

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <asp:UpdatePanel ID="Updatepanel4" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlCaptcha" Visible="false" runat="server">
                                            <div class="form-group">
                                                <div class="col-md-1">
                                                </div>
                                                <div class="col-md-11">
                                                    <div class="widget" id="captch" style="width: 86%;">

                                                        <div class="widget-head">
                                                            Captcha Code  <%--<span class="inactive">*</span>--%>
                                                        </div>
                                                        <div class="widget-body">
                                                            <asp:TextBox ID="txtcaptcha" placeholder="Enter Captcha Code" runat="server" CssClass="form-control" Width="45%" Style="margin-right: 15px;"></asp:TextBox>
                                                            <div style="display: inline-block; vertical-align: super">
                                                                <rsv:CaptchaControl ID="captcha1" runat="server" CaptchaLength="5" CssClass="pull-left innerB  margin-right"
                                                                    CaptchaHeight="50" CaptchaWidth="200" CaptchaLineNoise="None" ForeColor="#00FFCC" CaptchaMinTimeout="5" CaptchaMaxTimeout="240"
                                                                    BackColor="White" CaptchaChars="ABCDEFGHIJKLNPQRTUVXYZ12346789"
                                                                    FontColor="Darkblue" />
                                                                <asp:LinkButton runat="server" ID="LinkButton1" CssClass="reset-icon margin" CausesValidation="false" ToolTip="Refresh" OnClick="lnkbtnCaptcha_Click" OnClientClick="generateCode()"></asp:LinkButton>
                                                            </div>


                                                            <asp:RequiredFieldValidator ID="rfvCaptcha" runat="server" ControlToValidate="txtcaptcha" CssClass="error"
                                                                ErrorMessage="Enter Captcha" Display="Dynamic"></asp:RequiredFieldValidator>
                                                            <asp:Label ID="lblerror" runat="server" CssClass="error" Text="" Visible="true"></asp:Label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                                <asp:Panel ID="pnlCmpvalue" runat="server" Visible="true">
                                    <div class="form-group">
                                        <div class="col-md-1">
                                        </div>
                                        <div class="col-md-11">
                                            <div class="input-icon left">
                                                <label>Company Name</label>
                                                <span class="glyphicons building log-ext2"><i></i></span>
                                                <div>
                                                    <asp:DropDownList ID="ddlCompanyId" runat="server"></asp:DropDownList>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </asp:Panel>

                                <asp:Panel ID="pnlcompvalue" runat="server" Visible="false">
                                    <div class="form-group">
                                        <div class="col-md-1">
                                        </div>
                                        <div class="col-md-11">
                                            <div class="input-icon left">
                                                <label>Company Id</label>
                                                <span class="glyphicons building log-ext-2"><i></i></span>
                                                <asp:TextBox ID="txtCompanyCode" runat="server" CssClass="form-control" placeholder="Company Id" autocomplete="off"></asp:TextBox>
                                                <%-- <div>
                                                <asp:DropDownList ID="ddlCompanyId" runat="server"></asp:DropDownList>
                                            </div>--%>
                                            </div>
                                        </div>
                                    </div>
                                </asp:Panel>
                                <%--     <div class="form-group">
                                    <div class="col-md-1">
                                    </div>
                                 <div class="col-md-6">
                                        <asp:CheckBox ID="chkLDap" runat="server" />

                                        <asp:Label ID="Label1" Text="LDAP" runat="server" ></asp:Label>

                                        <asp:CheckBox ID="chkActiveDirectory" runat="server" />

                                        <asp:Label ID="Label2" Text="AD" runat="server" ></asp:Label>

                                        <asp:CheckBox ID="chkSSOSaml" runat="server" />

                                        <asp:Label ID="Label3" Text="SAML" runat="server"></asp:Label>
                                    </div>--%>
                                <div class="form-group">
                                    <div class="col-md-1">
                                    </div>
                                    <div class="col-md-11">

                                      

                                     <%--   <asp:CheckBox ID="chkLDap" runat="server" />

                                        <asp:Label ID="Label1" Text="LDAP" runat="server" CssClass="padding padding-none-TB"></asp:Label>--%>

                                        <asp:CheckBox ID="chkActiveDirectory" runat="server" AutoPostBack="true" OnCheckedChanged="chkActiveDirectory_CheckedChanged" />

                                      <%--  <asp:Label ID="Label2" Text="AD" runat="server" CssClass="padding padding-none-TB"></asp:Label>--%>
                                          <asp:Label ID="Label2" Text="Active Directory" runat="server" CssClass="padding padding-none-TB"></asp:Label>

                                     <%--   <asp:CheckBox ID="chkSSOSaml" runat="server" />

                                        <asp:Label ID="Label3" Text="SAML" runat="server" CssClass="padding padding-none-TB"></asp:Label>--%>

                                    </div>
                                    <%--<div class="col-md-5">
                                    </div>--%>
                                </div>

                                <asp:Panel ID="pnlDomain" CssClass="form-group" runat="server" Visible="false">
                                    <div class="col-md-1">
                                    </div>
                                    <div class="col-md-11">
                                        <div class="input-icon left">
                                            <label>
                                                <asp:Label ID="lbldomainName" runat="server" Text="Domain" Visible="false"></asp:Label></label>
                                            <span class="glyphicons globe log-ext3"><i></i></span>
                                            <div>
                                                <input id="combobox1" readonly="readonly" type="text" runat="server" visible="false" placeholder="Enter Domain" />

                                            </div>
                                        </div>

                                    </div>
                                </asp:Panel>

                                <div class="form-group">
                                    <div class="col-md-7"></div>

                                    <div class="col-md-5">

                                        <asp:Button ID="LoginButton" runat="server" CommandName="Login" Width="68%" CssClass="btn btn-block btn-inverse" Text="Login" ValidationGroup="LoginUser" OnClick="BtnLoginButtonClick" OnClientClick="return validateAndSubmitLogin();"></asp:Button>
                                    </div>
                                </div>
                            </div>
                            <div class=" center ">
                                Copyright © <%--2019/2020--%> <span id='spFY'></span> by Perpetuuiti. All Rights Reserved.
                            </div>
                        </div>
                    </div>
                    <asp:Panel ID="licensebg" runat="server" Visible="False" Width="450px" Height="480px">
                        <div id="modal">
                            <div class="modal-window " style="top: 138px; width: 450px; margin-left: 30%">
                                <div class="block-content margin-right margin-top1em no-title license-bg">
                                    <div class="margin-bottom35 white-text">
                                        version 4.0
                                    </div>
                                    <div class=" margin-top45 margin-bottom8">
                                        <asp:RadioButton ID="rbKey" runat="server" />Register BCMS License Key
                                    </div>
                                    <div class="margin-bottom8">
                                        <asp:TextBox ID="txtKey" runat="server" Style="width: 290px"></asp:TextBox>
                                        <img src="Images/icons/key.png" alt="#" />
                                    </div>
                                    <div class="margin-bottom8">
                                        <asp:CheckBox ID="chkKey" runat="server" />
                                        Activate product now
                                    </div>
                                    <div id="SelectAll" class="message warning" runat="server" visible="false">
                                        Enter Activation key,Select Radio button,select check box <span class="close-bt"></span>
                                    </div>
                                    <div id="companymac" class="message error " runat="server" visible="false">
                                        Invalid License key, Please contact System Administrator <span class="close-bt"></span>
                                    </div>
                                    <div id="Expirekey" class="message error " runat="server" visible="false">
                                        Your License key is Expired, Please Update with Administrator <span class="close-bt"></span>
                                    </div>
                                    <div class="block-footer align-right">
                                        <asp:Button ID="btnContinue" runat="server" Text="Continue" CssClass="btn btn-primary"
                                            OnClick="BtnContinueClick" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </asp:Panel>
                </div>
            </div>
        </div>
    </div>
    <script src="Script/jquery.capslockstate.js"></script>
    <script type="text/javascript">

        $('[id$=divClass]').click(function () {
            $(this).fadeOut("slow");
        });

    </script>

    <script type="text/javascript">
        $(document).ready(function () {

            $(window).bind("capsOn", function (event) {
                if ($(".chk-caps:focus").length > 0) {
                    $(".caps-error").show();
                }
            });
            $(window).bind("capsOff capsUnknown", function (event) {
                $(".caps-error").hide();
            });
            $(".chk-caps").bind("focusout", function (event) {
                $(".caps-error").hide();
            });
            $(".chk-caps").bind("focusin", function (event) {
                if ($(window).capslockstate("state") === true) {
                    $(".caps-error").show();
                }
            });
            /* 
           * Initialize the capslockstate plugin.
           * Monitoring is happening at the window level.
           */
            $(window).capslockstate();

            document.getElementById("spFY").innerHTML = getCurrentFinancialYear();
        });
        function getCurrentFinancialYear() {
            var fiscalyear = "";
            var today = new Date();
            if ((today.getMonth() + 1) <= 3) {
                fiscalyear = (today.getFullYear() - 1) + "-" + today.getFullYear()
            } else {
                fiscalyear = today.getFullYear() + "-" + (today.getFullYear() + 1)
            }
            return fiscalyear
        }
     </script>
</asp:Content>
