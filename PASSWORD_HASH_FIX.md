# Fix for PassEncyptHidden.Value Coming Empty

## Problem Description
After implementing the session-based GUID storage to replace hidden fields, the `PassEncyptHidden.Value` field was coming empty during form submission. This happened because the password hashing functions became asynchronous (due to AJAX calls to get the GUID from the server), but the form submission was happening before the AJAX calls completed.

## Root Cause
1. **Original Implementation**: The GUID was immediately available from a hidden field, so password hashing was synchronous
2. **New Implementation**: The GUID must be retrieved via AJAX call, making password hashing asynchronous
3. **Timing Issue**: When users clicked the login button immediately after entering their password, the form submitted before the password hashing completed

## Solution Implemented

### 1. Made Password Hashing Functions Support Callbacks
Updated `getPasswordHash()` and `getUserNameHash()` functions to accept callback parameters:

```javascript
function getPasswordHash(control, callback) {
    // Copy password to hidden field first
    var passHiddenElement = document.getElementById('passHidden');
    if (passHiddenElement) {
        passHiddenElement.value = control.value;
    }
    
    getStaticGuidFromServer(function(guid) {
        var strData = genrateUserNameHash(passHiddenElement, guid);
        $('[id$=ctl00_cphBody_PassEncyptHidden]').val(strData);
        $('[id$=ctl00_cphBody_Password]').val(strData);
        passHiddenElement.value = strData;
        
        // Call callback when hashing is complete
        if (callback && typeof callback === 'function') {
            callback();
        }
    });
}
```

### 2. Added Form Submission Control
Created `validateAndSubmitLogin()` function to ensure proper sequencing:

```javascript
function validateAndSubmitLogin() {
    // First run ASP.NET validation
    if (typeof Page_ClientValidate === 'function') {
        if (!Page_ClientValidate('LoginUser')) {
            return false; // Validation failed
        }
    }
    
    // Ensure username is hashed first, then password, then submit
    var userNameField = $('[id$=ctl00_cphBody_UserName]');
    var userEncryptField = $('[id$=ctl00_cphBody_UserEncrypt]');
    
    function proceedWithPasswordHashing() {
        ensurePasswordHashedBeforeSubmit(function() {
            __doPostBack('ctl00$cphBody$LoginButton', '');
        });
    }
    
    if (userNameField.val() && !userEncryptField.val()) {
        getUserNameHash(userNameField[0], proceedWithPasswordHashing);
    } else {
        proceedWithPasswordHashing();
    }
    
    return false; // Prevent default submission
}
```

### 3. Added Password Hash Validation
Created `ensurePasswordHashedBeforeSubmit()` function:

```javascript
function ensurePasswordHashedBeforeSubmit(callback) {
    var passwordField = $('[id$=ctl00_cphBody_Password]');
    var passEncryptedField = $('[id$=ctl00_cphBody_PassEncyptHidden]');
    
    // Check if password field has value but encrypted field is empty
    if (passwordField.val() && !passEncryptedField.val()) {
        // Hash the password and then proceed with submission
        getPasswordHash(passwordField[0], callback);
    } else {
        // Password already hashed, proceed immediately
        callback();
    }
}
```

### 4. Updated Login Button
Modified the login button to use client-side validation:

```html
<asp:Button ID="LoginButton" runat="server" CommandName="Login" Width="68%" 
    CssClass="btn btn-block btn-inverse" Text="Login" ValidationGroup="LoginUser" 
    OnClick="BtnLoginButtonClick" OnClientClick="return validateAndSubmitLogin();">
</asp:Button>
```

### 5. Added Missing passHidden Element
Added the required hidden field that the JavaScript expects:

```html
<input type="hidden" id="passHidden" />
```

## Key Changes Made

### Files Modified:
- `Source/CP.UI/Login.aspx` - Added passHidden field and OnClientClick
- `Source/CP.UI/Script/Login.js` - Updated all password hashing functions

### Execution Flow:
1. User clicks Login button
2. `validateAndSubmitLogin()` runs ASP.NET validation first
3. If validation passes, ensures username is hashed
4. Then ensures password is hashed
5. Only after both are complete, submits the form via `__doPostBack()`

## Debugging Features Added
Added console logging to help troubleshoot:
- GUID retrieval from server
- Password hashing process
- Form submission timing

## Testing Checklist
- [ ] Login with valid credentials works
- [ ] Login with invalid credentials shows proper error
- [ ] Password field gets properly encrypted
- [ ] Username field gets properly encrypted
- [ ] Form validation still works (required fields, etc.)
- [ ] Console shows proper logging during login process
- [ ] No JavaScript errors in browser console

## Potential Issues to Monitor
1. **Network Latency**: Slow AJAX calls could delay form submission
2. **Multiple Clicks**: Users clicking login button multiple times
3. **Browser Compatibility**: Ensure __doPostBack works across browsers
4. **Session Timeout**: GUID retrieval failing if session expires

## Rollback Plan
If issues persist:
1. Remove OnClientClick from login button
2. Revert to synchronous password hashing with session-stored GUID
3. Consider pre-loading GUID on page load instead of on-demand retrieval
