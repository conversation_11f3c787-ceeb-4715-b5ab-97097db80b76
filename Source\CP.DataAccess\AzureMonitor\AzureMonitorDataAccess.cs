﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.AzureMonitor
{
    internal sealed class AzureMonitorDataAccess : BaseDataAccess, IAzure_MonitoringDataAccess
    {
            #region Constructors

        public AzureMonitorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<Azure_Monitoring> CreateEntityBuilder<Azure_Monitoring>()
        {
            return (new AzureMonitorBuilder()) as IEntityBuilder<Azure_Monitoring>;
        }

        #endregion Constructors

        #region Methods


        Azure_Monitoring IAzure_MonitoringDataAccess.Azure_StatusGetByInfraId(int infraobjectId)
        {
            try
            {
                const string sp = "Azure_MoniStatus_GetByInfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, infraobjectId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<Azure_Monitoring>()).BuildEntity(reader, new Azure_Monitoring()) : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAzure_MonitoringDataAccess.Azure_StatusGetByInfraId(" + infraobjectId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<Azure_Monitoring> IAzure_MonitoringDataAccess.Azure_MonitorGetByDate(int infraobjectId, string sdate, string edate)
        {
            
            try
            {
                const string sp = "Azure_MonitorGetByDate";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    sdate = Convert.ToDateTime(sdate).ToString("dd-MM-yy");
                    edate = Convert.ToDateTime(edate).ToString("dd-MM-yy");
#endif
                    Database.AddInParameter(cmd, Dbstring + "IINFRAOBJECTID", DbType.Int32, infraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "ISTARTDATE", DbType.AnsiString, sdate);
                    Database.AddInParameter(cmd, Dbstring + "IENDDATE", DbType.AnsiString, edate);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Azure_Monitoring>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAzure_MonitoringDataAccess.Azure_MonitorGetByDate(" + infraobjectId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<Azure_Monitoring> IAzure_MonitoringDataAccess.Azure_MonitorHourlyByInfra(int infraObjectId)
        {
            try
            {
                const string sp = "AZUREMONITOR_HOURBYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Azure_Monitoring>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAzure_MonitoringDataAccess.Azure_MonitorHourlyByInfra(" +
                    infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
        #endregion Methods
    }
}
