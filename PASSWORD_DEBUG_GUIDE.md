# Password Hash Debugging Guide

## Current Issue
The password hash generated by JavaScript is still not matching the server-side hash, causing login failures.

## Debugging Steps

### Step 1: Test JavaScript Hashing Function
1. Open the login page in your browser
2. Open browser Developer Tools (F12)
3. Go to the Console tab
4. Run this command to test the hashing function:

```javascript
testPasswordHashing("test123");
```

This will show you:
- The GUID being used
- Step-by-step hash generation
- The final hash result

### Step 2: Check Server-Side Logs
1. Attempt to login with a known password
2. Check the application logs for entries like:
```
=== PASSWORD HASH DEBUG START ===
Original password from DB: [password]
Session GUID: [guid]
Session encrypted password: [client hash]
Server-generated hash: [server hash]
Hash lengths - Server: [length], Session: [length]
Hashes match: [true/false]
=== PASSWORD HASH DEBUG END ===
```

### Step 3: Compare Hash Generation

#### JavaScript Process (check console):
```
=== PASSWORD HASHING DEBUG START ===
Password value to hash: [password]
Got GUID from server: [guid]
Base64 encoded result: [base64]
Generated password hash: [hash]
=== PASSWORD HASHING DEBUG END ===
```

#### Server Process (check logs):
```
Original password from DB: [password]
Session GUID: [guid]
Server-generated hash: [hash]
```

### Step 4: Manual Verification

#### Test with Known Values:
1. In browser console, run:
```javascript
// Test with specific values
var testGuid = "12345678-1234-1234-1234-123456789012";
var testPassword = "test123";
testPasswordHashing(testPassword, testGuid);
```

2. Compare with server-side equivalent (add this to Login.aspx.cs temporarily):
```csharp
var testHash = Utility.getHashKeyByString("test123", "12345678-1234-1234-1234-123456789012");
_logger.InfoFormat("Test hash: {0}", testHash);
```

### Step 5: Check for Common Issues

#### Issue 1: GUID Mismatch
- **Symptom**: Different GUIDs on client vs server
- **Check**: Compare GUID values in console vs logs
- **Fix**: Ensure both use `GetStaticGuid()` web method

#### Issue 2: Character Encoding
- **Symptom**: Different base64 encoding results
- **Check**: Compare base64 values in console vs server
- **Fix**: Verify UTF-8 encoding consistency

#### Issue 3: Algorithm Differences
- **Symptom**: Same inputs, different outputs
- **Check**: Step through both algorithms character by character
- **Fix**: Ensure exact algorithm match

### Step 6: Detailed Algorithm Comparison

#### JavaScript Base64Encode:
```javascript
function base64EncodeWithSwap(plainText) {
    var utf8Bytes = new TextEncoder().encode(plainText);
    var base64 = btoa(String.fromCharCode.apply(null, utf8Bytes));
    
    var actual = "";
    for (var i = 0; i < base64.length; i += 2) {
        if (i + 1 < base64.length) {
            actual += base64[i + 1] + base64[i];
        } else {
            actual += base64[i];
        }
    }
    return actual;
}
```

#### Server-Side Base64Encode:
```csharp
public static string Base64Encode(string plainText)
{
    var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
    var input = System.Convert.ToBase64String(plainTextBytes);
    string actual = string.Empty;
    for (int i = 0; i < input.Length; i++)
    {
        actual = actual + input[i + 1] + input[i];
        i++;
    }
    return actual;
}
```

### Step 7: Quick Test Commands

#### Browser Console Commands:
```javascript
// Test base64 encoding
var test = "test123";
var encoded = base64EncodeWithSwap(test);
console.log("Base64 result:", encoded);

// Test full hash with current GUID
getStaticGuidFromServer(function(guid) {
    var hash = getHashKeyByString("test123", guid);
    console.log("Hash with current GUID:", hash);
});

// Check what's in session
getEncryptedPasswordFromSession(function(sessionHash) {
    console.log("Current session hash:", sessionHash);
});
```

### Step 8: Expected Results

For password "test123" with GUID "12345678-1234-1234-1234-123456789012":

1. **Base64 encoding**: Should produce same result on both sides
2. **Hash generation**: Should produce identical final hash
3. **Session storage**: Should store and retrieve same hash
4. **Comparison**: `userEncryptPass.Equals(sessionPassword)` should be `true`

### Step 9: If Still Not Working

#### Possible Issues:
1. **Session timing**: Hash not stored before comparison
2. **Multiple GUIDs**: Different GUID instances being used
3. **Character encoding**: UTF-8 vs ASCII differences
4. **Browser compatibility**: TextEncoder not supported

#### Alternative Approach:
If the issue persists, consider temporarily adding a server-side web method to generate the hash:

```csharp
[WebMethod]
public static string GeneratePasswordHashForTesting(string password, string guid)
{
    return Utility.getHashKeyByString(password, guid);
}
```

Then compare:
```javascript
// Generate hash on server
$.ajax({
    type: "POST",
    url: "Login.aspx/GeneratePasswordHashForTesting",
    data: JSON.stringify({ password: "test123", guid: currentGuid }),
    contentType: "application/json; charset=utf-8",
    dataType: "json",
    success: function(response) {
        console.log("Server-generated hash:", response.d);
        var clientHash = getHashKeyByString("test123", currentGuid);
        console.log("Client-generated hash:", clientHash);
        console.log("Hashes match:", response.d === clientHash);
    }
});
```

### Step 10: Next Actions

1. Run the debugging steps above
2. Compare the console output with server logs
3. Identify where the difference occurs
4. Report back with the specific values you're seeing

The detailed logging should help us pinpoint exactly where the hash generation is diverging between client and server.
