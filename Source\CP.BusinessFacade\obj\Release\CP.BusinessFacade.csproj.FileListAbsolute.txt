C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\log4net.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.ObjectBuilder.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
C:\NewSVNClient\CPRoot4.0_WorkingCopy_05022016\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\log4net.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
C:\Users\<USER>\Desktop\Projects\2016\**********\UITest\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\log4net.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
D:\Root\New_SVN\CPRoot  4.0\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\log4net.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\Devart.Data.Oracle.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.ObjectBuilder.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\Devart.Data.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\Mono.Security.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
E:\CP SVN\CP Root\Committed Root Copy\CP\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\log4net.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.ObjectBuilder.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\Mono.Security.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
E:\Client_04092017\CPRoot_4.5\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\log4net.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.ObjectBuilder.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\Mono.Security.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
D:\CPROOT\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\log4net.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.ObjectBuilder.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\Mono.Security.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
D:\CP 2019\May 2019\CPROOT 4.5 UI Sonali 27052019\CpRoot_MsSql_new_CheckOut\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\log4net.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.ObjectBuilder.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Mono.Security.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
D:\Projects\CP 4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Oracle.ManagedDataAccess.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\log4net.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Oracle.ManagedDataAccess.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.ObjectBuilder.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Mono.Security.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
D:\Client\2021\satyam_Root4.5\Current_CP4.5\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\log4net.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Oracle.ManagedDataAccess.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.ObjectBuilder.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\Mono.Security.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
E:\Projects\Current_CP4.5\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\log4net.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Oracle.ManagedDataAccess.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Mono.Security.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.ObjectBuilder.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
D:\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
D:\TCS_Internal\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
D:\TCS_Internal\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
D:\TCS_Internal\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.BusinessFacade.pdb
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\log4net.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Oracle.ManagedDataAccess.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Telerik.Web.UI.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Mono.Security.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\Microsoft.Practices.ObjectBuilder.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.CacheController.pdb
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.Common.pdb
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.DataAccess.pdb
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.Helper.pdb
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.IDataAccess.pdb
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.IFacade.pdb
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\bin\Release\CP.ExceptionHandler.pdb
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.csprojResolveAssemblyReference.cache
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.dll
D:\TCS\TCS_Internal\UI\Source\CP.BusinessFacade\obj\Release\CP.BusinessFacade.pdb
