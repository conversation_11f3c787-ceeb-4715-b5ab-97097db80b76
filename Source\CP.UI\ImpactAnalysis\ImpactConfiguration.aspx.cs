﻿using System;
using System.Web.Services;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.IO;
using System.Web;
using System.Web.UI;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Collections;
using System.Data;
using CP.Common.DatabaseEntity;
using System.Text;
using log4net;


namespace CP.UI
{
    public partial class ImpactConfiguration : ImpactBasePageEditor
    {
        #region Variables

        private static readonly ILog _logger = LogManager.GetLogger(typeof(ImpactConfiguration));
        private static IFacade _facade = new Facade();
        public static string queryParam = "";
        public static string ErrorMsg = "";

        #endregion Variables

        #region Properties

        public override string MessageInitials
        {
            get { return "Business Impact Relationship"; }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin)
                {
                    return Constants.UrlConstants.Urls.ImpactAnalysis.EntityImpactList;
                }
                return string.Empty;
            }
        }

        #endregion Properties

        #region Methods

        public override void PrepareView()
        {
            queryParam = CurrentConfig;

            //queryParam = Utility.IsMD5EncryptedString(queryParam) ? CryptographyHelper.Decrypt(queryParam) : queryParam;
            String Qurystring = "";
            int BSid = 0, BFid = 0, infraObjectid = 0, infraComponentid = 0;
            String ActionType = "";
            if (Request.QueryString != null)
            {
                if (Request.QueryString.Count == 2)
                {
                    Qurystring = Request.QueryString[0].ToString();
                    ActionType = Request.QueryString[1].ToString();

                    usefortitle.Text = " InfraObject To Business Function ";
                    pnlInfraToBF.Visible = true;
                    pnlBFtoBF.Visible = false;
                    //pnlBFtoBS.Visible = false;
                    lblInfrFilter.Visible = true;
                    ddlInfraobject.Visible = true;
                    queryParam = "FromDigram";
                    CurrentConfig = queryParam;
                }
            }
            rdEffectDate.SelectedDate = DateTime.Now;
            if (!Page.IsPostBack)
            {
                #region bind common comtrol
                usefortitle.Text = "";
                //Binding BS dropdown for filtering purpose

                #endregion  bind Defualt comtrol

                #region Binding controls as per configuration type
                //Binding controls as per configuration type
                if (queryParam.Equals("InfraToBF"))
                {
                    usefortitle.Text = " InfraObject To Business Function ";
                    pnlInfraToBF.Visible = true;
                    pnlBFtoBF.Visible = false;
                    //pnlBFtoBS.Visible = false;
                    lblInfrFilter.Visible = true;
                    ddlInfraobject.Visible = true;
                    upFilter.Visible = true;
                    BindControlsValueInfraToBF();

                }
                else if (queryParam.Equals("BStoBS"))
                {
                    usefortitle.Text = " Business Service To Business Service ";
                    pnlBStoBS.Visible = true;
                    pnlInfraToBF.Visible = false;
                    pnlBFtoBF.Visible = false;
                    //pnlBFtoBS.Visible = false;
                    lblInfrFilter.Visible = true;
                    ddlInfraobject.Visible = true;
                    upFilter.Visible = false;
                    BindControlsValueBSToBS();

                }
                else if (queryParam.Equals("BFtoBF"))
                {
                    usefortitle.Text = " Business Function To Business Function ";
                    pnlBFtoBF.Visible = true;
                    pnlInfraToBF.Visible = false;
                    //pnlBFtoBS.Visible = false;
                    lblInfrFilter.Visible = false;
                    ddlInfraobject.Visible = false;
                    upFilter.Visible = false;
                    BindControlsValueBFToBF();
                }
                else if (queryParam.Equals("FromDigram"))
                {
                    usefortitle.Text = " InfraObject To Business Function ";

                }
                //else if (queryParam.Equals("BFtoBS"))
                //{
                //    usefortitle.Text = " Business Function To Business Service ";
                //    //pnlBFtoBS.Visible = true;
                //    pnlBFtoBF.Visible = false;
                //    pnlInfraToBF.Visible = false;
                //    lblInfrFilter.Visible = false;
                //    ddlInfraobject.Visible = false;
                //    BindControlsValueBFToBS();
                //}
                #endregion

                #region bindControls from rule diagram
                if (ActionType.Equals("Edit") || ActionType.Equals("Add"))
                {
                    String[] lstID = Qurystring.Split('|');

                    String lstCnt = lstID.Count().ToString();
                    switch (lstCnt)
                    {
                        case "1":
                            BSid = Convert.ToInt32(lstID[0]);
                            break;

                        case "2":
                            BSid = Convert.ToInt32(lstID[0]);
                            BFid = Convert.ToInt32(lstID[1]);
                            break;

                        case "3":
                            BSid = Convert.ToInt32(lstID[0]);
                            BFid = Convert.ToInt32(lstID[1]);
                            infraObjectid = Convert.ToInt32(lstID[2]);
                            break;

                        case "4":
                            BSid = Convert.ToInt32(lstID[0]);
                            BFid = Convert.ToInt32(lstID[1]);
                            infraObjectid = Convert.ToInt32(lstID[2]);
                            infraComponentid = Convert.ToInt32(lstID[3]);
                            break;
                    }
                    bindInfraToBFcontrol();
                    BindControlsValueInfraToBFforDiagram(BSid, BFid, infraObjectid, infraComponentid);
                }
                #endregion bindControls from rule diagram


            }
        }
        #region From Digram to bind controls
        private void BindControlsValueInfraToBFforDiagram(int BSid, int BFid, int InfraobjectId, int InfraComponentId)
        {
            rfvBusinessServ_Fltr.Enabled = true;
            rfvInfraPbject_Fltr.Enabled = true;

            var ImpactDetails = _facade.GetAllImpactRelType();
            ddlImpactTypeBF.DataTextField = "RelTypeDescription";
            ddlImpactTypeBF.DataValueField = "Id";
            ddlImpactTypeBF.DataSource = ImpactDetails;
            ddlImpactTypeBF.DataBind();
            btnSave.Text = "Save";
            ddlImpactTypeBF.Items.Insert(0, new ListItem("-Select Impact-", "0"));

            ddlBSImpactType_BFtoBS.DataTextField = "RelTypeDescription";
            ddlBSImpactType_BFtoBS.DataValueField = "Id";
            ddlBSImpactType_BFtoBS.DataSource = ImpactDetails;
            ddlBSImpactType_BFtoBS.DataBind();
            DropDownListExtension.AddDefaultItem(ddlBSImpactType_BFtoBS, "-Select Impact-");

            var infraTobf = Facade.GetALLBusinessImpactInfratoBF();
            var byCompIDInfratobf = from i in infraTobf where i.InfraComponentId == InfraComponentId select i;
            BusinessImpact current = new BusinessImpact();

            if (byCompIDInfratobf.Count() > 0)
            {
                current = byCompIDInfratobf.First();
                CurrentEntity = current;
                CurrentImpactId = CurrentEntity.Id;
                CurrentEntityId = CurrentEntity.Id;
                BindControlsValueInfraToBF();
                Session["CurrentEntityId"] = CurrentEntityId;
            }
            else
            {
                //DropDownListExtension.AddDefaultItem(ddlImpactTypeBF, "-Select Impact-");
                btnSave.Text = "Save";
                // var BS = Facade.GetInfraObjectByName(CurrentEntity.InfraObjectName);
                if (BSid > 0)
                {
                    ddlBusinessService.SelectedValue = BSid.ToString();
                    ddlBusinessService_SelectedIndexChanged(null, null);
                }
                if (InfraobjectId > 0)
                {
                    ddlInfraobject.SelectedValue = InfraobjectId.ToString();
                    ddlInfraobject_SelectedIndexChanged(null, null);
                }
                if (BFid > 0)
                {
                    ddlBusinessFunction.SelectedValue = BFid.ToString();
                    ddlBusinessFunction_SelectedIndexChanged(null, null);
                }
                if (BSid > 0)
                {
                    ddlBS_BFtoBS.SelectedValue = BSid.ToString();
                    //ddlBSImpactType_BFtoBS.SelectedValue = CurrentEntity.BSImpactID.ToString();
                }
                if (InfraComponentId > 0)
                {
                    String SetSelectedIdDatabase = InfraComponentId.ToString() + "-" + InfraobjectComponentType.Database.ToDescription();
                    String SetSelectedIdServer = InfraComponentId.ToString() + "-" + InfraobjectComponentType.Server.ToDescription();
                    for (int i = 0; i < ddlinfraObjectComponent.Items.Count; i++)
                    {
                        if (ddlinfraObjectComponent.Items[i].Value == SetSelectedIdServer)
                        {
                            ddlinfraObjectComponent.SelectedValue = SetSelectedIdServer;
                            break;
                        }
                        else if (ddlinfraObjectComponent.Items[i].Value == SetSelectedIdDatabase)
                        {
                            ddlinfraObjectComponent.SelectedValue = SetSelectedIdDatabase;
                            break;
                        }
                    }
                }
                //CurrentEntity.BIRelationType = BusinessImpactRelationType.InfratoBF;
                //ddlImpactTypeBF.SelectedValue = CurrentEntity.BFImpactID.ToString();
                // ddlinfraObjectComponent.SelectedValue = CurrentEntity.InfraComponentId.ToString();//+ "-" + CurrentEntity.InfraComponentType.ToDescription();
                //txtDescription.Text = CurrentEntity.Description;
                rdEffectDate.SelectedDate = DateTime.Now;
                //CurrentEntity.Id = CurrentEntityId;
            }


        }
        #endregion
        #region Bind BF to BS windows Control

        private void BindControlsValueBFToBS()
        {
            rfvBusinessServ_Fltr.Enabled = false;
            rfvInfraPbject_Fltr.Enabled = false;
            var appGroupList = Facade.GetAllBusinessFunctions();

            //ddlBF_BFtoBS.DataSource = appGroupList;
            //ddlBF_BFtoBS.DataTextField = "Name";
            //ddlBF_BFtoBS.DataValueField = "Id";
            //ddlBF_BFtoBS.DataBind();
            //DropDownListExtension.AddDefaultItem(ddlBF_BFtoBS, "-Select Business function-");

            //var BFList = Facade.GetAllBusinessServices();





            //ddlBFImpactType_BFtoBS.DataTextField = "RelTypeDescription";
            //ddlBFImpactType_BFtoBS.DataValueField = "Id";
            //ddlBFImpactType_BFtoBS.DataSource = ImpactDetails;
            //ddlBFImpactType_BFtoBS.DataBind();
            //DropDownListExtension.AddDefaultItem(ddlBFImpactType_BFtoBS, "-Select Impact-");
            //var ImpactDetails = _facade.GetAllImpactRelType();

            //btnSave.Text = "Save";
            if (CurrentImpactId > 0)
            {
                btnSave.Text = "Update";

                //ddlBF_BFtoBS.SelectedValue = CurrentEntity.BFId.ToString();
                //ddlBFImpactType_BFtoBS.SelectedValue = CurrentEntity.BFImpactID.ToString();

            }
        }

        #endregion

        #region Bind BF to BF windows Control
        private void BindControlsValueBFToBF()
        {
            rfvBusinessServ_Fltr.Enabled = false;
            rfvInfraPbject_Fltr.Enabled = false;
            var appGroupList = Facade.GetAllBusinessFunctions();

            var appGroupListBS = Facade.GetAllBusinessServices();
            IList<InfraObject> allInfraObject1 = Facade.GetAllInfraObject();
            if (!IsSuperAdmin && allInfraObject1 != null)
            {
                allInfraObject1 = Facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);
                appGroupList.Clear();
                appGroupListBS.Clear();

                var allInfraObject = allInfraObject1.OrderBy(a => a.Name);
                foreach (var infraobjectDetails in allInfraObject)
                {
                    var bs = Facade.GetBusinessServiceById(infraobjectDetails.BusinessServiceId);
                    var bf = Facade.GetBusinessFunctionById(infraobjectDetails.BusinessFunctionId);

                    appGroupList.Add(bf);
                    appGroupListBS.Add(bs);
                }
            }
            ddlBusinessService.DataSource = appGroupListBS;
            ddlBusinessService.DataTextField = "Name";
            ddlBusinessService.DataValueField = "Id";
            ddlBusinessService.DataBind();
            DropDownListExtension.AddDefaultItem(ddlBusinessService, "-Select Business service-");

            ddlParentdBF.DataSource = appGroupList;
            ddlParentdBF.DataTextField = "Name";
            ddlParentdBF.DataValueField = "Id";
            ddlParentdBF.DataBind();
            DropDownListExtension.AddDefaultItem(ddlParentdBF, "-Select Business function-");

         

            var ImpactDetails = _facade.GetAllImpactRelType();

            ddlParentImpactType.DataTextField = "RelTypeDescription";
            ddlParentImpactType.DataValueField = "Id";
            ddlParentImpactType.DataSource = ImpactDetails;
            ddlParentImpactType.DataBind();
            DropDownListExtension.AddDefaultItem(ddlParentImpactType, "-Select Impact-");

            ddlChildImpactType.DataTextField = "RelTypeDescription";
            ddlChildImpactType.DataValueField = "Id";
            ddlChildImpactType.DataSource = ImpactDetails;
            ddlChildImpactType.DataBind();
            DropDownListExtension.AddDefaultItem(ddlChildImpactType, "-Select Impact-");

            if (CurrentImpactId > 0)
            {
                btnSave.Text = "Update";

                ddlParentdBF.SelectedValue = CurrentEntity.ParentBFId.ToString();
                ddlParentdBF_SelectedIndexChanged(null, null);
                ddlChildBF.SelectedValue = CurrentEntity.ChildBFId.ToString();
                ddlParentImpactType.SelectedValue = CurrentEntity.ParentBFImpactID.ToString();
                ddlChildImpactType.SelectedValue = CurrentEntity.ChildBFImpactID.ToString();
                CurrentEntity.BIRelationType = BusinessImpactRelationType.BFtoBF;
                txtDescription.Text = CurrentEntity.Description;
                rdEffectDate.SelectedDate = CurrentEntity.EffectiveDateFrom;
                CurrentEntity.Id = CurrentEntityId;
            }


        }

        protected void ddlParentdBF_SelectedIndexChanged(object sender, EventArgs e)
        {
            ddlChildBF.Items.Clear();
            var appGroupList = Facade.GetAllBusinessFunctions();

            var lstChildBF = appGroupList.Where(x => x.Name != ddlParentdBF.SelectedItem.Text).ToList();

            ddlChildBF.DataSource = lstChildBF;
            ddlChildBF.DataTextField = "Name";
            ddlChildBF.DataValueField = "Id";
            ddlChildBF.DataBind();
            DropDownListExtension.AddDefaultItem(ddlChildBF, "-Select Business function-");
        }


        #endregion Bind Infra to BF windows Control

        #region Bind BS to BS Control

        private void BindControlsValueBSToBS()
        {
            rfvBusinessServ_Fltr.Enabled = false;
            rfvInfraPbject_Fltr.Enabled = false;
            var appGroupList = Facade.GetAllBusinessServices();
            IList<InfraObject> allInfraObject1 = Facade.GetAllInfraObject();
            if (!IsSuperAdmin && allInfraObject1 != null)
            {
                allInfraObject1 = Facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);
                appGroupList.Clear();


                var allInfraObject = allInfraObject1.OrderBy(a => a.Name);
                foreach (var infraobjectDetails in allInfraObject)
                {
                    var bs = Facade.GetBusinessServiceById(infraobjectDetails.BusinessServiceId);

                    appGroupList.Add(bs);
                }
            }

            ddlParentBusinessServiceBStoBS.DataSource = appGroupList;
            ddlParentBusinessServiceBStoBS.DataTextField = "Name";
            ddlParentBusinessServiceBStoBS.DataValueField = "Id";
            ddlParentBusinessServiceBStoBS.DataBind();
            DropDownListExtension.AddDefaultItem(ddlParentBusinessServiceBStoBS, "-Select Business Service-");

          
            var ImpactDetails = _facade.GetAllImpactRelType();

            dllParentImpactTypeBStoBs.DataTextField = "RelTypeDescription";
            dllParentImpactTypeBStoBs.DataValueField = "Id";
            dllParentImpactTypeBStoBs.DataSource = ImpactDetails;
            dllParentImpactTypeBStoBs.DataBind();
            DropDownListExtension.AddDefaultItem(dllParentImpactTypeBStoBs, "-Select Impact-");

            dllChildImpactTypeBStoBs.DataTextField = "RelTypeDescription";
            dllChildImpactTypeBStoBs.DataValueField = "Id";
            dllChildImpactTypeBStoBs.DataSource = ImpactDetails;
            dllChildImpactTypeBStoBs.DataBind();
            DropDownListExtension.AddDefaultItem(dllChildImpactTypeBStoBs, "-Select Impact-");

            if (CurrentImpactId > 0)
            {
                btnSave.Text = "Update";

                ddlParentBusinessServiceBStoBS.SelectedValue = CurrentEntity.ParentBSId.ToString();
                ddlParentBusinessServiceBStoBS_SelectedIndexChanged(null, null);
                ddlChildBusinessServiceBStoBS.SelectedValue = CurrentEntity.ChildBSId.ToString();
                dllParentImpactTypeBStoBs.SelectedValue = CurrentEntity.ParentBSImpactID.ToString();
                dllChildImpactTypeBStoBs.SelectedValue = CurrentEntity.ChildBSImpactID.ToString();
                CurrentEntity.BIRelationType = BusinessImpactRelationType.BStoBS;
                txtDescription.Text = CurrentEntity.Description;
                rdEffectDate.SelectedDate = CurrentEntity.EffectiveDateFrom;
                CurrentEntity.Id = CurrentEntityId;
            }


        }

        #endregion Bind BS to BS Control
        #region Bind Infra to BF windows Control
        private void BindControlsValueInfraToBF()
        {
            rfvBusinessServ_Fltr.Enabled = true;
            rfvInfraPbject_Fltr.Enabled = true;
            IList<BusinessService> appGroupList = new List<BusinessService>();

             appGroupList = Facade.GetAllBusinessServices();

            IList<InfraObject> allInfraObject1 = Facade.GetAllInfraObject();
            if (!IsSuperAdmin && allInfraObject1 != null)
            {
                allInfraObject1 = Facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);
                appGroupList.Clear();


                var allInfraObject = allInfraObject1.OrderBy(a => a.Name);
                foreach (var infraobjectDetails in allInfraObject)
                {
                    var bs = Facade.GetBusinessServiceById(infraobjectDetails.BusinessServiceId);

                    appGroupList.Add(bs);
                }
            }
            ddlBusinessService.DataSource = appGroupList;
            ddlBusinessService.DataTextField = "Name";
            ddlBusinessService.DataValueField = "Id";
            ddlBusinessService.DataBind();
            DropDownListExtension.AddDefaultItem(ddlBusinessService, "-Select Business service-");

            var ImpactDetails = _facade.GetAllImpactRelType();
            ddlImpactTypeBF.DataTextField = "RelTypeDescription";
            ddlImpactTypeBF.DataValueField = "Id";
            ddlImpactTypeBF.DataSource = ImpactDetails;
            ddlImpactTypeBF.DataBind();
            btnSave.Text = "Save";
            ddlImpactTypeBF.Items.Insert(0, new ListItem("-Select Impact-", "0"));

            ddlBSImpactType_BFtoBS.DataTextField = "RelTypeDescription";
            ddlBSImpactType_BFtoBS.DataValueField = "Id";
            ddlBSImpactType_BFtoBS.DataSource = ImpactDetails;
            ddlBSImpactType_BFtoBS.DataBind();
            DropDownListExtension.AddDefaultItem(ddlBSImpactType_BFtoBS, "-Select Impact-");

            //DropDownListExtension.AddDefaultItem(ddlImpactTypeBF, "-Select Impact-");
            if (CurrentImpactId > 0)
            {
                btnSave.Text = "Update";
                var BS = Facade.GetInfraObjectByName(CurrentEntity.InfraObjectName);
                ddlBusinessService.SelectedValue = BS.BusinessServiceId.ToString();
                ddlBusinessService_SelectedIndexChanged(null, null);
                ddlInfraobject.SelectedValue = CurrentEntity.InfraID.ToString();
                ddlInfraobject_SelectedIndexChanged(null, null);

                //Code for process name according to job
                if (CurrentEntity.jobname != null)
                {
                    if (CurrentEntity.jobname.ToLower().ToString().Equals("monitorapplicationprocess"))
                    {
                        Chkprocess.Checked = true;
                        ChkQueue.Checked = false;


                        ddlbindProcessAndServiceByInfraid(CurrentEntity.InfraID);

                        lblrulesubtype.Visible = true;
                        lblrulesubtype.InnerText = "IF Process/Service";
                        ddlAppliactionProcess.Visible = true;
                        //updAllRulepnl.Update();
                        lblProcess.Visible = true;

                    }
                    else if (CurrentEntity.jobname.ToLower().ToString().Equals("monitorqueueprocess"))
                    {
                        ChkQueue.Checked = true;
                        Chkprocess.Checked = false;
                        ddlbindQueueByInfraid(CurrentEntity.InfraID, CurrentEntity.BSId);
                        lblrulesubtype.Visible = true;
                        ddlAppliactionProcess.Visible = true;
                        lblrulesubtype.InnerText = "IF Queue";
                        //updAllRulepnl.Update();
                        lblProcess.Visible = true;

                    }
                    else
                    {
                        ChkQueue.Checked = false;
                        Chkprocess.Checked = false;
                        lblrulesubtype.Visible = false;
                        ddlAppliactionProcess.Visible = false;
                        lblProcess.Visible = false;
                        //updAllRulepnl.Update();

                    }
                }
                else
                {
                    ChkQueue.Checked = false;
                    Chkprocess.Checked = false;
                    lblrulesubtype.Visible = false;
                    ddlAppliactionProcess.Visible = false;
                    lblProcess.Visible = false;
                
                }

                ddlBusinessFunction.SelectedValue = CurrentEntity.BFId.ToString();
                ddlBusinessFunction_SelectedIndexChanged(null, null);
                ddlBS_BFtoBS.SelectedValue = CurrentEntity.BSId.ToString();
                ddlBSImpactType_BFtoBS.SelectedValue = CurrentEntity.BSImpactID.ToString();
                if (btnSave.Text == "Update")
                {
                    if (ddlAppliactionProcess.Items.Count > 0 && ddlAppliactionProcess!=null)
                    {
                        if (CurrentEntity.jobname.ToLower().ToString().Equals("monitorqueueprocess"))
                            ddlAppliactionProcess.SelectedValue = CurrentEntity.ProcessName;
                        if (CurrentEntity.jobname.ToLower().ToString().Equals("monitorapplicationprocess"))
                            ddlAppliactionProcess.SelectedValue = CurrentEntity.ProcessName;
                    }
                   
                }
                CurrentEntity.BIRelationType = BusinessImpactRelationType.InfratoBF;
                ddlImpactTypeBF.SelectedValue = CurrentEntity.BFImpactID.ToString();
                ddlinfraObjectComponent.SelectedValue = CurrentEntity.InfraComponentId.ToString() + "-" + CurrentEntity.InfraComponentType.ToDescription();
                txtDescription.Text = CurrentEntity.Description;
                rdEffectDate.SelectedDate = CurrentEntity.EffectiveDateFrom;
                CurrentEntity.Id = CurrentEntityId;

            }
        }
        #endregion Bind Infra to BF windows Control

        #region Methods for Queue and Process

        public void ddlbindProcessAndServiceByInfraid(int InfraObject)
        {

            string isprocess = string.Empty;
            string IsService = string.Empty;
            string[] str = { };
            IList<MonitorServices> ProcessList = new List<MonitorServices>();


            try
            {
                if (InfraObject > 0)
                {
                    var isAppprocess = Facade.GetMonitoringServicesByInfraObjectId(InfraObject);

                    if (isAppprocess != null && isAppprocess.Count() > 0)
                    {

                        foreach (var items in isAppprocess)
                        {
                            var server = Facade.GetServerById(items.ServerId);
                            if (server.Type.ToString().Contains("PRDBServer") || server.Type.ToString().Contains("PRESXIServer") || server.Type.ToString().Contains("PRAppServer"))
                            {
                                ProcessList.Add(items);
                            }
                        }

                        if (ProcessList != null)
                        {
                            if (ProcessList.Count() > 0)
                            {
                                ddlAppliactionProcess.Items.Clear();
                                ddlAppliactionProcess.DataSource = ProcessList;
                                ddlAppliactionProcess.DataTextField = "ServicePath";
                                ddlAppliactionProcess.DataValueField = "Id";
                                ddlAppliactionProcess.DataBind();
                            }
                            else
                            { lblProcess.Visible = false; ddlAppliactionProcess.Visible = false; }
                        }
                        else
                        { lblProcess.Visible = false; ddlAppliactionProcess.Visible = false; }
                    }
                    else
                    {
                        lblProcess.Visible = false; ddlAppliactionProcess.Visible = false;
                    }

                }


            }
            catch (Exception ex)
            {

                var bcms = new CpException(CpExceptionType.CommonUnhandled, " ", ex);
                _logger.Error("Exception Occured while populating GetProcessByInfraObjectid method in ImpactConfiguration.cs:" + ex.Message);
                ExceptionManager.Manage(bcms);


            }

        }

        public void ddlbindQueueByInfraid(int InfraObject, int BserviceId)
        {

            string isprocess = string.Empty; string IsQueueName = string.Empty;
            string IsService = string.Empty;
            string[] str = { };
            IList<QueueMoniter> QueueList = new List<QueueMoniter>();
            List<string> ProcessList = new List<string>();
            try
            {
                if (InfraObject > 0)
                {
                    var isQueueprocess = Facade.GetInfraObjectById(InfraObject);


                    if (isQueueprocess != null)
                        QueueList = Facade.QueueMon_Status_GetByInfraBSId(InfraObject, BserviceId);

                    if (QueueList != null && QueueList.Count > 0)
                    {
                        ddlAppliactionProcess.Items.Clear();
                        ddlAppliactionProcess.DataSource = QueueList;
                        ddlAppliactionProcess.DataTextField = "QUEUENAME";
                        ddlAppliactionProcess.DataValueField = "ID";

                        ddlAppliactionProcess.DataBind();

                    }
                    else
                    {
                        lblProcess.Visible = false; ddlAppliactionProcess.Visible = false;
                    }
                }


            }
            catch (Exception ex)
            {

                var bcms = new CpException(CpExceptionType.CommonUnhandled, " ", ex);
                _logger.Error("Exception Occured while populating ddlbindQueueByInfraid method in ImpactConfiguration.cs:" + ex.Message);
                ExceptionManager.Manage(bcms);


            }

        }

        #endregion Methods for Queue and Process

        public override void PrepareEditView()
        {


        }

        public override void SaveEditor()
        {

            if (btnSave.Text == "Save")
            {

                CurrentEntity.CreatorId = LoggedInUserId;
                CurrentEntity.UpdatorId = LoggedInUserId;

                CurrentEntity = Facade.AddBusinessImpact(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUserName, "Rules Configuration", UserActionType.CreateCompanyProfile, "Rules Configuration '" + CurrentEntity.BIRelationType.ToDescription() + "' was added to the company profile table", LoggedInUserId);
            }
            else
            {

                CurrentEntity.UpdatorId = LoggedInUserId;
                CurrentEntity.Id = CurrentEntityId;
                CurrentEntity = Facade.UpdateBusinessImpact(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUserName, "Rules Configuration", UserActionType.UpdateCompanyProfile, "Rules Configuration '" + CurrentEntity.BIRelationType.ToDescription() + "' was updated to the company profile table", LoggedInUserId);
            }
            Session["Currententity"] = CurrentEntity;

        }

        public override void BuildEntities()
        {
            #region set the properties as per selected Configuration rules
            if (queryParam.Equals("InfraToBF") || queryParam.Equals("FromDigram"))
            {

                CurrentEntity.InfraID = Convert.ToInt32(ddlInfraobject.SelectedValue);
                CurrentEntity.BFId = Convert.ToInt32(ddlBusinessFunction.SelectedValue);
                CurrentEntity.BIRelationType = BusinessImpactRelationType.InfratoBF;
                CurrentEntity.BFImpactID = Convert.ToInt32(ddlImpactTypeBF.SelectedValue);
                String[] InfraComponentText = ddlinfraObjectComponent.GetSelectedText().Split('-');

                string[] InfraComponentDatavalue = ddlinfraObjectComponent.SelectedValue.ToString().Split('-');
                CurrentEntity.BSImpactID = Convert.ToInt32(ddlBSImpactType_BFtoBS.SelectedValue);
                CurrentEntity.BSId = Convert.ToInt32(ddlBS_BFtoBS.SelectedValue);
                if (InfraComponentDatavalue.Count() > 0)
                {
                    CurrentEntity.InfraComponentId = Convert.ToInt32(InfraComponentDatavalue[0]);
                    CurrentEntity.InfraComponentType = (InfraobjectComponentType)Enum.Parse(typeof(InfraobjectComponentType), InfraComponentDatavalue[1].ToString().Trim());
                }
                if (Chkprocess.Checked)
                {
                    var isAppprocess = Facade.GetMonitoringServicesByInfraObjectId(Convert.ToInt32(ddlInfraobject.SelectedValue));
                    if (isAppprocess != null)
                    {
                        CurrentEntity.ProcessName = ddlAppliactionProcess.SelectedValue;
                        CurrentEntity.jobname = "MonitorApplicationProcess";

                    }
                }
                else if (ChkQueue.Checked)
                {
                    var isQueueProcess = Facade.GetInfraObjectById(Convert.ToInt32(ddlInfraobject.SelectedValue));
                    if (isQueueProcess != null)
                    {
                        CurrentEntity.ProcessName = ddlAppliactionProcess.SelectedValue;
                        CurrentEntity.jobname = "MonitorQueueProcess";

                    }

                }
                else
                {
                    if (CurrentEntity.InfraComponentType.ToString().ToLower() == "server")
                    {
                        CurrentEntity.ProcessName = "server";
                        CurrentEntity.jobname = "MonitorPRServerStatus";
                    }
                    else
                    {
                        CurrentEntity.ProcessName = "database";
                        CurrentEntity.jobname = "MonitorPRDatabaseStatus";
                    }
                }
            }
            else if (queryParam.Equals("BFtoBF"))
            {
                CurrentEntity.ParentBFId = Convert.ToInt32(ddlParentdBF.SelectedValue);
                CurrentEntity.ChildBFId = Convert.ToInt32(ddlChildBF.SelectedValue);
                CurrentEntity.ParentBFImpactID = Convert.ToInt32(ddlParentImpactType.SelectedValue);
                CurrentEntity.ChildBFImpactID = Convert.ToInt32(ddlChildImpactType.SelectedValue);
                CurrentEntity.BIRelationType = BusinessImpactRelationType.BFtoBF;

            }
            else if (queryParam.Equals("BStoBS"))
            {
                CurrentEntity.ParentBSId = Convert.ToInt32(ddlParentBusinessServiceBStoBS.SelectedValue);
                CurrentEntity.ChildBSId = Convert.ToInt32(ddlChildBusinessServiceBStoBS.SelectedValue);
                CurrentEntity.ParentBSImpactID = Convert.ToInt32(dllParentImpactTypeBStoBs.SelectedValue);
                //CurrentEntity.ChildBSImpactID = Convert.ToInt32(dllChildImpactTypeBStoBs.SelectedValue);
                CurrentEntity.BIRelationType = BusinessImpactRelationType.BStoBS;

            }
            #endregion set the properties as per selected Configuration rules

            #region Common proerties for all three configuration

            CurrentEntity.EffectiveDateFrom = Convert.ToDateTime(rdEffectDate.SelectedDate);
            CurrentEntity.EffectiveDateTo = DateTime.Today.AddYears(100);
            CurrentEntity.CreatorId = LoggedInUser != null ? LoggedInUser.Id : 0;
            CurrentEntity.IsEffective = 1;
            CurrentEntity.Description = txtDescription.Text;
            if (queryParam == "FromDigram")
            {
                if (Session["CurrentEntityId"] != null)
                {
                    CurrentEntity.Id = Convert.ToInt32(Session["CurrentEntityId"].ToString());
                    CurrentEntityId = CurrentEntity.Id;
                    CurrentConfig = queryParam;
                }
            }
            else
                CurrentEntity.Id = CurrentEntityId;


            #endregion Common proerties for all three configuration



        }

        #endregion Methods

        #region Event

        protected void ddlBusinessService_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (queryParam.Equals("InfraToBF"))
            {
                bindInfraToBFcontrol();
            }
            else if (queryParam.Equals("BFtoBF"))
            {
                bindBFToBFcontrol();
            }
            else if (queryParam.Equals("BFtoBS"))
            {
                bindBFToBScontrol();
            }
            else if (queryParam.Equals("FromDigram"))
            {
                bindInfraToBFcontrol();
            }
        }

        private void bindBFToBScontrol()
        {

        }

        private void bindBFToBFcontrol()
        {

        }

        #region bind Infraobject dropdown on selected BS filter
        /// <summary>
        /// bind the infraobject list dropdown as per selected BS for Filtering purpose.
        /// </summary>
        private void bindInfraToBFcontrol()
        {
            ddlinfraObjectComponent.DataSource = null;
            ddlinfraObjectComponent.DataBind();
            //DropDownListExtension.AddDefaultItem(ddlinfraObjectComponent, "-Select Infraobject Component-");
            ddlBusinessFunction.DataSource = null;
            ddlBusinessFunction.DataBind();
            //DropDownListExtension.AddDefaultItem(ddlBusinessFunction, "-Select Business Function-");
            ddlBS_BFtoBS.DataSource = null;
            ddlBS_BFtoBS.DataBind();
            //DropDownListExtension.AddDefaultItem(ddlBS_BFtoBS, "-Select Business service-");
            var InfraObjectlist = _facade.GetAllInfraObject();
            if (!IsSuperAdmin && InfraObjectlist != null)
            {
                InfraObjectlist = _facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);
            }
            var InfraObjectListByBsId = from a in InfraObjectlist where a.BusinessServiceId == Convert.ToInt32(ddlBusinessService.SelectedValue) && a.IsActive == 1 select a;
            ddlInfraobject.DataSource = InfraObjectListByBsId;
            ddlInfraobject.DataTextField = "Name";
            ddlInfraobject.DataValueField = "Id";
            ddlInfraobject.DataBind();
            DropDownListExtension.AddDefaultItem(ddlInfraobject, "-Select Infraobject-");


        }
        #endregion bind Infraobject dropdown on selected BS filter

        protected void ddlInfraobject_SelectedIndexChanged(object sender, EventArgs e)
        {
            /* Binding Infraobject components 1.PR IP address-OS Type with Id for (DataValueField).
             * 2. Database type- Database Name with id for (DataValueField)*/
            #region Bind Infraobject Component dropdown of selected infraobject
            DataTable dt = new DataTable("InfraComponent");
            int infraId = Convert.ToInt32(ddlInfraobject.SelectedValue);

            var infraobjectDetails = _facade.GetInfraObjectById(infraId);

            int infraBfID = infraobjectDetails.BusinessFunctionId;

            var businessFunctionlist = _facade.GetAllBusinessFunctions();
            var bsldt = from b in businessFunctionlist where b.Id == infraBfID select b;

            ddlBusinessFunction.DataSource = bsldt.ToList<BusinessFunction>();
            ddlBusinessFunction.DataTextField = "Name";
            ddlBusinessFunction.DataValueField = "Id";
            ddlBusinessFunction.DataBind();
            DropDownListExtension.AddDefaultItem(ddlBusinessFunction, "-Select Business Function-");


            dt.Columns.Add("Id", typeof(String));
            dt.Columns.Add("ComponentType", typeof(String));
            dt.Columns.Add("Name", typeof(String));

            var serverDetails = _facade.GetServerById(infraobjectDetails.PRServerId);
            DataRow row1 = dt.NewRow();
            row1["Id"] = serverDetails.Id + "-" + InfraobjectComponentType.Server.ToDescription();
            row1["ComponentType"] = InfraobjectComponentType.Server;
            row1["Name"] = "IP - " + CryptographyHelper.Md5Decrypt(serverDetails.IPAddress) + " OS - " + serverDetails.OSType;
            dt.Rows.Add(row1);

            DatabaseBase DbBase = null;

            if (infraobjectDetails.PRDatabaseId > 0)
                DbBase = _facade.GetDatabaseBaseById(infraobjectDetails.PRDatabaseId);

            DatabaseType DBType = DatabaseType.Undefined;
            int DatabaseId = 0;
            String sidName = "";
            //var DBTypeDBId = from a in DbBase where a.ServerId == serverid select a;
            if (DbBase != null)
            {
                DBType = DbBase.DatabaseType;
                DatabaseId = DbBase.Id;

                sidName = getDataBaseName(DBType, DatabaseId);
                DataRow row2 = dt.NewRow();
                row2["Id"] = DatabaseId + "-" + InfraobjectComponentType.Database.ToDescription();
                row2["ComponentType"] = InfraobjectComponentType.Database;
                row2["Name"] = "DB - " + DBType.ToDescription() + " Name - " + sidName;
                dt.Rows.Add(row2);
            }
            //foreach (var a in DbBase)
            //{
            //    if (a.ServerId == serverDetails.Id)
            //    {
            //DBType = a.DatabaseType;
            //DatabaseId = a.Id;

            // break;
            //    }
            //}

            ddlinfraObjectComponent.DataSource = dt;
            ddlinfraObjectComponent.DataTextField = "Name";
            ddlinfraObjectComponent.DataValueField = "Id";
            ddlinfraObjectComponent.DataBind();
            DropDownListExtension.AddDefaultItem(ddlinfraObjectComponent, "-Select Infraobject Component-");

            #endregion Bind Infraobject Component dropdown of selected infraobject
        }

        /// <summary>
        /// Get database name by databaseType and Databasebase id
        /// </summary>
        /// <param name="databaseType">databaseType</param>
        /// <param name="DatabaseId">DatabaseId</param>
        /// <returns>DB name as string</returns>
        /// <author>Gorak Khule -24/04/2015</author>
        private String getDataBaseName(DatabaseType databaseType, int DatabaseId)
        {
            String sidName = "";
            try
            {

                switch (databaseType)
                {

                    case DatabaseType.PostgreSQL:
                        var db = (Facade.GetDatabasePostgreSqlByDatabaseBaseId(DatabaseId));
                        if (db != null)
                            sidName = db.DatabaseName;
                        break;

                    case DatabaseType.Oracle:
                        var db1 = Facade.GetDatabaseOracleByDatabaseBaseId(DatabaseId);
                        if (db1 != null)
                            sidName = db1.OracleSID;
                        break;

                    case DatabaseType.OracleRac:
                        var db2 = (Facade.GetDatabaseOracleRacByDatabaseBaseId(DatabaseId));
                        if (db2 != null)
                            sidName = db2.OracleSID;
                        break;

                    case DatabaseType.Sql:
                        var db3 = (Facade.GetDatabaseSqlByDatabaseBaseId(DatabaseId));
                        if (db3 != null)
                            sidName = db3.DatabaseSID;

                        break;

                    case DatabaseType.Exchange:
                        var db4 = (Facade.GetDatabaseExchangeByDatabaseBaseId(DatabaseId));
                        if (db4 != null)
                            sidName = db4.MailBoxDBName;

                        break;

                    case DatabaseType.DB2:
                        var db5 = (Facade.GetDatabaseDb2ByDatabaseBaseId(DatabaseId));
                        if (db5 != null)
                            sidName = db5.DatabaseSID;

                        break;

                    case DatabaseType.ExchangeDAG:
                        var db6 = (Facade.GetDatabaseExchangeDAGByDatabaseBaseId(DatabaseId));
                        if (db6 != null)
                            sidName = db6.MailBoxDBName;


                        break;

                    case DatabaseType.MySQL:
                        var db7 = (Facade.GetDatabaseMySqlByDatabaseBaseId(DatabaseId));
                        if (db7 != null)
                            sidName = db7.DatabaseID;


                        break;

                    case DatabaseType.Postgres9x:
                        var db8 = (Facade.GetDatabasePostgre9xByDatabaseBaseId(DatabaseId));
                        if (db8 != null)
                            sidName = db8.DatabaseName;


                        break;

                    case DatabaseType.SQLNative2008:
                        var db9 = (Facade.GetDatabaseMSSqlByDatabaseBaseId(DatabaseId));
                        if (db9 != null)
                            sidName = db9.DatabaseName;

                        break;
                }
            }
            catch (Exception)
            {

                sidName = "";
                return sidName;
            }
            return sidName;
        }
        protected void btnSave_Click(object sender, EventArgs e)
        {

            var returnUrl = Helper.Url.SecureUrl.ReturnUrl;

            if (returnUrl.IsNullOrEmpty())
            {
                returnUrl = ReturnUrl;
            }
            var submitButton = (Button)sender;
            var buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            {
                currentTransactionType = TransactionType.Save;
            }
            else if (buttionText.Contains(" update "))
            {
                currentTransactionType = TransactionType.Update;

            }

            try
            {
                if (currentTransactionType != TransactionType.Undefined)
                {

                    BuildEntities();
                    if (!BusinessRuleValidation())
                    {
                        //Set error message
                        lblErr.Text = ErrorMsg;
                        lblErr.Visible = true;
                        return;
                    }

                    StartTransaction();
                    SaveEditor();
                    EndTransaction();
                    //string message = MessageInitials;
                    //ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, currentTransactionType));


                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                returnUrl = Request.RawUrl;

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                returnUrl = Request.RawUrl;

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                    ExceptionManager.Manage(customEx, this);
                }
            }

            if (returnUrl.IsNotNullOrEmpty())
            {


                //  ScriptManager.RegisterStartupScript(Page, typeof(Page), "closeScript", "clientClose('');", true);

                ScriptManager.RegisterStartupScript(this, GetType(), "close", "Close();", true);

            }
        }



        private bool BusinessRuleValidation()
        {
            bool validate = false;
            DateTime existingDate = DateTime.MinValue;

            switch (CurrentConfig)
            {

                #region validation for Infra to BF for Effective date in same rule config

                case "InfraToBF":
                case "":

                    #region update
                    if (btnSave.Text.ToLower().Contains("update"))
                    {
                        var currentRuleInfratoBf = Facade.GetBusinessImpactInfratoBFById(CurrentEntity.Id);
                        //if (DateTime.Now.AddSeconds(-currentRuleInfratoBf.UpdateDate.Second) < currentRuleInfratoBf.UpdateDate.AddHours(1))
                        //{
                        //    validate = false;
                        //    ErrorMsg = "Please update rule after one hour.";
                        //}
                        //else
                        //{
                            var InfraToBFDetails = Facade.GetALLBusinessImpactInfratoBF();
                            if (InfraToBFDetails != null)
                            {
                                var InfraToBFFilter = from i in InfraToBFDetails
                                                      where
                                                      (i.InfraID == CurrentEntity.InfraID && i.BFId == CurrentEntity.BFId && i.InfraComponentType == CurrentEntity.InfraComponentType && i.InfraComponentId == CurrentEntity.InfraComponentId && i.jobname == CurrentEntity.jobname && i.ProcessName == CurrentEntity.ProcessName &&  i.Id != CurrentEntity.Id)
                                                      select i;
                                if (InfraToBFFilter != null)
                                {
                                    if (InfraToBFFilter.Count() == 0)
                                    {
                                        validate = true;
                                    }
                                    else
                                    {
                                        validate = false;
                                        ErrorMsg = "This rule is already configured.";
                                    }

                                }
                                else
                                {

                                    validate = true;


                                }
                            }
                            else
                            {
                                validate = true;
                            }
                        //}//End of else
                    }
                    #endregion Update

                    #region Save
                    else if (btnSave.Text.ToLower().Contains("save"))
                    {
                        var InfraToBFDetails = Facade.GetALLBusinessImpactInfratoBF();
                        if (InfraToBFDetails != null)
                        {
                            var InfraToBFFilter = from i in InfraToBFDetails
                                                  where
                                                  (i.InfraID == CurrentEntity.InfraID && i.BFId == CurrentEntity.BFId && i.InfraComponentType == CurrentEntity.InfraComponentType && i.InfraComponentId == CurrentEntity.InfraComponentId && i.jobname.ToLower() == CurrentEntity.jobname.ToLower() && i.ProcessName.ToLower() == CurrentEntity.ProcessName.ToLower())
                                                  select i;

                            if (InfraToBFFilter != null)
                            {
                                if (InfraToBFFilter.Count() == 0)
                                {
                                    validate = true;
                                }
                                else
                                {
                                    validate = false;
                                    ErrorMsg = "This rule is already configured.";
                                }

                            }
                            else
                            {

                                validate = true;


                            }
                        }
                        else
                        {

                            validate = true;


                        }
                    }
                    #endregion save

                    break;

                #endregion validation for Infra to BF for Effective date in same rule confi

                #region validation for BF to BF for Effective date in same rule config
                case "BFtoBF":


                    #region Update
                    if (btnSave.Text.ToLower().Contains("update"))
                    {
                        var currentRuleBftoBF = Facade.GetBusinessImpactBFtoBFById(CurrentEntity.Id);
                        //if (DateTime.Now.AddSeconds(-currentRuleBftoBF.UpdateDate.Second) < currentRuleBftoBF.UpdateDate.AddHours(1))
                        //{
                        //    validate = false;
                        //    ErrorMsg = "Please update rule after one hour.";
                        //}
                        //else
                        //{
                            var BFToBFDetails = Facade.GetALLBusinessImpactBFtoBF();
                            if (BFToBFDetails != null)
                            {
                                var BFToBFFilter = from b in BFToBFDetails
                                                   where
                                                   (b.ParentBFId == CurrentEntity.ParentBFId && b.ChildBFId == CurrentEntity.ChildBFId && b.Id != CurrentEntity.Id)
                                                   select b;
                                if (BFToBFFilter != null)
                                {
                                    if (BFToBFFilter.Count() == 0)
                                    {
                                        validate = true;
                                    }
                                    else
                                    {
                                        validate = false;
                                        ErrorMsg = "This rule is already configured.";
                                    }

                                }
                                else
                                {

                                    validate = true;


                                }
                            }
                            else
                            {
                                validate = true;


                            }
                        //}//End of Else
                    }
                    #endregion Update

                    #region Save
                    if (btnSave.Text.ToLower().Contains("save"))
                    {
                        var BFToBFDetails = Facade.GetALLBusinessImpactBFtoBF();
                        if (BFToBFDetails != null)
                        {
                            var BFToBFFilter = from b in BFToBFDetails
                                               where
                                               (b.ParentBFId == CurrentEntity.ParentBFId && b.ChildBFId == CurrentEntity.ChildBFId)
                                               select b;
                            if (BFToBFFilter != null)
                            {
                                if (BFToBFFilter.Count() == 0)
                                {
                                    validate = true;
                                }
                                else
                                {
                                    validate = false;
                                    ErrorMsg = "This rule is already configured.";
                                }

                            }
                            else
                            {

                                validate = true;


                            }
                        }
                        else
                        {
                            validate = true;
                        }
                    }
                    #endregion Save
                    break;



                #endregion validation for BF to BF for Effective date in same rule config

                #region validation for BF to BS for Effective date in same rule config
                case "BFtoBS":
                    #region Update
                    if (btnSave.Text.ToLower().Contains("update"))
                    {
                        var currentRuleBftoBs = Facade.GetBusinessImpactBFtoBSById(CurrentEntity.Id);
                        if (DateTime.Now < currentRuleBftoBs.UpdateDate.AddHours(1))
                        {
                            validate = false;
                            ErrorMsg = "Please update rule after one hour.";
                        }
                        else
                        {
                            var BFToBSDetails = Facade.GetALLBusinessImpactBFtoBS();
                            if (BFToBSDetails != null)
                            {
                                var BFToBSFilter = from bs in BFToBSDetails
                                                   where
                                                   (bs.BFId == CurrentEntity.BFId && bs.BFImpactID == CurrentEntity.BFImpactID && bs.BSId == CurrentEntity.BSId && bs.BSImpactID == CurrentEntity.BSImpactID && bs.EffectiveDateFrom == CurrentEntity.EffectiveDateFrom && bs.Id != CurrentEntity.Id)
                                                   select bs;
                                if (BFToBSFilter != null)
                                {
                                    if (BFToBSFilter.Count() == 0)
                                    {
                                        validate = true;
                                    }
                                    else
                                    {

                                        validate = false;
                                        ErrorMsg = "This rule is already configured.";
                                    }


                                }
                                else
                                {

                                    validate = true;


                                }
                            }
                            else
                                validate = true;

                        }
                    }
                    #endregion Update

                    #region Save
                    if (btnSave.Text.ToLower().Contains("save"))
                    {
                        var BFToBSDetails = Facade.GetALLBusinessImpactBFtoBS();
                        if (BFToBSDetails != null)
                        {
                            var BFToBSFilter = from bs in BFToBSDetails
                                               where
                                               (bs.BFId == CurrentEntity.BFId && bs.BFImpactID == CurrentEntity.BFImpactID && bs.BSId == CurrentEntity.BSId && bs.BSImpactID == CurrentEntity.BSImpactID && bs.EffectiveDateFrom == CurrentEntity.EffectiveDateFrom)
                                               select bs;
                            if (BFToBSFilter != null)
                            {
                                if (BFToBSFilter.Count() == 0)
                                {
                                    validate = true;
                                }
                                else
                                {

                                    validate = false;
                                    ErrorMsg = "This rule is already configured.";
                                }


                            }
                            else
                            {

                                validate = true;


                            }
                        }
                        else
                            validate = true;

                    }
                    #endregion Save
                    break;

                case "FromDigram":
                    #region update from Diagram
                    if (btnSave.Text.ToLower().Contains("update"))
                    {
                        var currentRuleInfratoBf = Facade.GetBusinessImpactInfratoBFById(CurrentEntity.Id);
                        if (DateTime.Now.AddSeconds(-currentRuleInfratoBf.UpdateDate.Second) < currentRuleInfratoBf.UpdateDate.AddHours(1))
                        {
                            validate = false;
                            ErrorMsg = "Please update rule after one hour.";
                        }
                        else
                        {
                            var InfraToBFDetails = Facade.GetALLBusinessImpactInfratoBF();
                            if (InfraToBFDetails != null)
                            {
                                var InfraToBFFilter = from i in InfraToBFDetails
                                                      where
                                                      (i.InfraID == CurrentEntity.InfraID && i.BFId == CurrentEntity.BFId && i.BFImpactID == CurrentEntity.BFImpactID && i.InfraComponentType == CurrentEntity.InfraComponentType && i.InfraComponentId == CurrentEntity.InfraComponentId && i.EffectiveDateFrom == CurrentEntity.EffectiveDateFrom && i.Id != CurrentEntity.Id)
                                                      select i;
                                if (InfraToBFFilter != null)
                                {
                                    if (InfraToBFFilter.Count() == 0)
                                    {
                                        validate = true;
                                    }
                                    else
                                    {
                                        validate = false;
                                        ErrorMsg = "This rule is already configured.";
                                    }

                                }
                                else
                                {

                                    validate = true;


                                }
                            }
                            else
                            {
                                validate = true;
                            }
                        }
                    }
                    #endregion Update from Diagram

                    #region Save from Diagram
                    else if (btnSave.Text.ToLower().Contains("save"))
                    {
                        var InfraToBFDetails = Facade.GetALLBusinessImpactInfratoBF();
                        if (InfraToBFDetails != null)
                        {
                            var InfraToBFFilter = from i in InfraToBFDetails
                                                  where
                                                  (i.InfraID == CurrentEntity.InfraID && i.BFId == CurrentEntity.BFId && i.BFImpactID == CurrentEntity.BFImpactID && i.InfraComponentType == CurrentEntity.InfraComponentType && i.InfraComponentId == CurrentEntity.InfraComponentId && i.EffectiveDateFrom == CurrentEntity.EffectiveDateFrom)
                                                  select i;

                            if (InfraToBFFilter != null)
                            {
                                if (InfraToBFFilter.Count() == 0)
                                {
                                    validate = true;
                                }
                                else
                                {
                                    validate = false;
                                    ErrorMsg = "This rule is already configured.";
                                }

                            }
                            else
                            {

                                validate = true;


                            }
                        }
                        else
                        {

                            validate = true;


                        }
                    }
                    #endregion save from Diagram
                    break;
                #endregion validation for BF to BS for Effective date in same rule config

                #region validation for BS to BS for Effective date in same rule config
                case "BStoBS":


                    #region Update
                    if (btnSave.Text.ToLower().Contains("update"))
                    {
                        var currentRuleBstoBs = Facade.GetBusinessImpactBStoBSById(CurrentEntity.Id);
                        //if (DateTime.Now.AddSeconds(-currentRuleBstoBs.UpdateDate.Second) < currentRuleBstoBs.UpdateDate.AddHours(1))
                        //{
                        //    validate = false;
                        //    ErrorMsg = "Please update rule after one hour.";
                        //}
                        //else
                        //{
                        var BSToBSDetails = Facade.GetALLBusinessImpactBStoBS();
                        if (BSToBSDetails != null)
                        {
                            var BSToBSFilter = from b in BSToBSDetails
                                               where
                                               (b.ParentBSId == CurrentEntity.ParentBSId && b.ChildBSId == CurrentEntity.ChildBSId && b.Id != CurrentEntity.Id && b.ParentBSImpactID == CurrentEntity.ParentBSImpactID)
                                               select b;
                            if (BSToBSFilter != null)
                            {
                                if (BSToBSFilter.Count() == 0)
                                {
                                    validate = true;
                                }
                                else
                                {
                                    validate = false;
                                    ErrorMsg = "This rule is already configured.";
                                }

                            }
                            else
                            {

                                validate = true;


                            }
                        }
                        else
                        {
                            validate = true;


                        }
                        //}//End of Else
                    }
                    #endregion Update

                    #region Save
                    if (btnSave.Text.ToLower().Contains("save"))
                    {
                        var BSToBSDetails = Facade.GetALLBusinessImpactBStoBS();
                        if (BSToBSDetails != null)
                        {
                            var BSToBSFilter = from b in BSToBSDetails
                                               where
                                               (b.ParentBSId == CurrentEntity.ParentBSId && b.ChildBSId == CurrentEntity.ChildBSId && b.ParentBSImpactID == CurrentEntity.ParentBSImpactID)
                                               select b;
                            if (BSToBSFilter != null)
                            {
                                if (BSToBSFilter.Count() == 0)
                                {
                                    validate = true;
                                }
                                else
                                {
                                    validate = false;
                                    ErrorMsg = "This rule is already configured.";
                                }

                            }
                            else
                            {

                                validate = true;


                            }
                        }
                        else
                        {
                            validate = true;
                        }
                    }
                    #endregion Save
                    break;
                case "FromBStoBsDigram":

                    #region Update from FromBStoBsDigram
                    if (btnSave.Text.ToLower().Contains("update"))
                    {
                        var currentRuleBstoBs = Facade.GetBusinessImpactBStoBSById(CurrentEntity.Id);
                        //if (DateTime.Now.AddSeconds(-currentRuleBstoBs.UpdateDate.Second) < currentRuleBstoBs.UpdateDate.AddHours(1))
                        //{
                        //    validate = false;
                        //    ErrorMsg = "Please update rule after one hour.";
                        //    UpdateError.Update();
                        //}
                        //else
                        //{
                        var BSToBSDetails = Facade.GetALLBusinessImpactBStoBS();
                        if (BSToBSDetails != null)
                        {
                            var BSToBSFilter = from b in BSToBSDetails
                                               where
                                               (b.ParentBSId == CurrentEntity.ParentBSId && b.ChildBSId == CurrentEntity.ChildBSId && b.Id != CurrentEntity.Id && b.ParentBSImpactID == CurrentEntity.ParentBSImpactID)
                                               select b;
                            if (BSToBSFilter != null)
                            {
                                if (BSToBSFilter.Count() == 0)
                                {
                                    validate = true;
                                }
                                else
                                {
                                    validate = false;
                                    ErrorMsg = "This rule is already configured.";
                                    //UpdateError.Update();
                                }

                            }
                            else
                            {

                                validate = true;


                            }
                        }
                        else
                        {
                            validate = true;


                        }
                        //}//End of Else
                    }
                    #endregion Update from FromBStoBsDigram

                    #region Save from FromBStoBsDigram
                    if (btnSave.Text.ToLower().Contains("save"))
                    {
                        var BSToBSDetails = Facade.GetALLBusinessImpactBStoBS();
                        if (BSToBSDetails != null)
                        {
                            var BSToBSFilter = from b in BSToBSDetails
                                               where
                                               (b.ParentBSId == CurrentEntity.ParentBSId && b.ChildBSId == CurrentEntity.ChildBSId && b.ParentBSImpactID == CurrentEntity.ParentBSImpactID)
                                               select b;
                            if (BSToBSFilter != null)
                            {
                                if (BSToBSFilter.Count() == 0)
                                {
                                    validate = true;
                                }
                                else
                                {
                                    validate = false;
                                    ErrorMsg = "This rule is already configured.";
                                    //UpdateError.Update();
                                }

                            }
                            else
                            {

                                validate = true;


                            }
                        }
                        else
                        {
                            validate = true;
                        }
                    }
                    #endregion Save FromBStoBsDigram
                    break;




                #endregion validation for BS to BS for Effective date in same rule config


            }
            return validate;
        }

        protected void ddlBusinessFunction_SelectedIndexChanged(object sender, EventArgs e)
        {

            int selectedBFid = Convert.ToInt32(ddlBusinessFunction.SelectedValue);
            List<BusinessService> bsList = new List<BusinessService>();

            //var AllBS = Facade.GetAllBusinessServices();
            if (selectedBFid > 0)
            {
                var BF = Facade.GetBusinessFunctionById(selectedBFid);
                BusinessService bs = null;
                if (BF.BusinessServiceId > 0)
                {
                    bs = Facade.GetBusinessServiceById(BF.BusinessServiceId);
                    bsList.Add(bs);
                }
            }
            ddlBS_BFtoBS.DataSource = bsList;
            ddlBS_BFtoBS.DataTextField = "Name";
            ddlBS_BFtoBS.DataValueField = "Id";
            ddlBS_BFtoBS.DataBind();
            DropDownListExtension.AddDefaultItem(ddlBS_BFtoBS, "-Select Business service-");
        }

        protected void ddlParentBusinessServiceBStoBS_SelectedIndexChanged(object sender, EventArgs e)
        {

            ddlChildBusinessServiceBStoBS.Items.Clear();
            var appGroupList = Facade.GetAllBusinessServices();

            var lstChildBS = appGroupList.Where(x => x.Name != ddlParentBusinessServiceBStoBS.SelectedItem.Text).ToList();

            ddlChildBusinessServiceBStoBS.DataSource = lstChildBS;
            ddlChildBusinessServiceBStoBS.DataTextField = "Name";
            ddlChildBusinessServiceBStoBS.DataValueField = "Id";
            ddlChildBusinessServiceBStoBS.DataBind();
            DropDownListExtension.AddDefaultItem(ddlChildBusinessServiceBStoBS, "-Select Business Service-");

        }

        protected void Chkprocess_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                ChkQueue.Checked = false;
                var isInfraCompType = ddlinfraObjectComponent.SelectedValue;
                int CharCap = isInfraCompType.Count(c => c.Equals('-'));
                if (CharCap > 0)
                {
                    isInfraCompType = isInfraCompType.Split('-')[1];
                }

                if (isInfraCompType.ToLower() == "server")
                {
                    if (Chkprocess.Checked)
                    {
                        lblProcess.Visible = true; ddlAppliactionProcess.Visible = true; lblrulesubtype.Visible = true; lblrulesubtype.InnerText = "IF Process/Service";
                        ddlbindProcessAndServiceByInfraid(Convert.ToInt32(ddlInfraobject.SelectedValue));
                    }
                    else
                    { lblProcess.Visible = false; ddlAppliactionProcess.Visible = false; lblrulesubtype.InnerText = string.Empty; }
                }
                else
                { lblProcess.Visible = false; }
            }
            catch (Exception ex)
            {

            }
        }

        protected void CheckBox1_CheckedChanged(object sender, EventArgs e)
        {
            Chkprocess.Checked = false;
            var isInfraCompType = ddlinfraObjectComponent.SelectedValue; int BusinessServiceid = 0;
            int CharCap = isInfraCompType.Count(c => c.Equals('-'));
            if (CharCap > 0)
            {
                isInfraCompType = isInfraCompType.Split('-')[1];
            }

            if (isInfraCompType.ToLower() == "server")
            {
                if (ChkQueue.Checked)
                {
                    lblProcess.Visible = true; ddlAppliactionProcess.Visible = true; lblrulesubtype.Visible = true; lblrulesubtype.InnerText = "IF Queue";

                    var infralist = Facade.GetInfraObjectById(Convert.ToInt32(ddlInfraobject.SelectedValue));
                    if (infralist != null)
                        BusinessServiceid = infralist.BusinessServiceId;

                    ddlbindQueueByInfraid(Convert.ToInt32(ddlInfraobject.SelectedValue), BusinessServiceid);
                }
                else
                { lblProcess.Visible = false; ddlAppliactionProcess.Visible = false; lblrulesubtype.Visible = false; }
            }
            else
            { lblProcess.Visible = false; }

        }

        protected void ddlinfraObjectComponent_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (Chkprocess.Checked)
                {
                    if (ddlInfraobject != null && ddlBusinessService != null)
                    {
                        if (ddlInfraobject.Items.Count > 0 && ddlBusinessService.Items.Count > 0)
                        {
                            ChkQueue.Checked = false;
                            var isInfraCompType = ddlinfraObjectComponent.SelectedValue;
                            int CharCap = isInfraCompType.Count(c => c.Equals('-'));
                            if (CharCap > 0)
                            {
                                isInfraCompType = isInfraCompType.Split('-')[1];
                            }

                            if (isInfraCompType.ToLower() == "server")
                            {
                                if (Chkprocess.Checked)
                                {
                                    lblProcess.Visible = true; ddlAppliactionProcess.Visible = true; lblrulesubtype.Visible = true; lblrulesubtype.InnerText = "IF Process/Service";
                                    ddlbindProcessAndServiceByInfraid(Convert.ToInt32(ddlInfraobject.SelectedValue));
                                }
                                else
                                { lblProcess.Visible = false; ddlAppliactionProcess.Visible = false; lblrulesubtype.InnerText = string.Empty; }
                            }
                            else
                            { lblProcess.Visible = false; }
                        }
                    }

                }
                else if (ChkQueue.Checked)
                {

                    if (ddlInfraobject != null && ddlBusinessService != null)
                    {
                        if (ddlInfraobject.Items.Count > 0 && ddlBusinessService.Items.Count > 0)
                        {
                            Chkprocess.Checked = false;
                            var isInfraCompType = ddlinfraObjectComponent.SelectedValue; int BusinessServiceid = 0;
                            int CharCap = isInfraCompType.Count(c => c.Equals('-'));
                            if (CharCap > 0)
                            {
                                isInfraCompType = isInfraCompType.Split('-')[1];
                            }

                            if (isInfraCompType.ToLower() == "server")
                            {
                                if (ChkQueue.Checked)
                                {
                                    lblProcess.Visible = true; ddlAppliactionProcess.Visible = true; lblrulesubtype.Visible = true; lblrulesubtype.InnerText = "IF Queue";

                                    var infralist = Facade.GetInfraObjectById(Convert.ToInt32(ddlInfraobject.SelectedValue));
                                    if (infralist != null)
                                        BusinessServiceid = infralist.BusinessServiceId;

                                    ddlbindQueueByInfraid(Convert.ToInt32(ddlInfraobject.SelectedValue), BusinessServiceid);
                                }
                                else
                                { lblProcess.Visible = false; ddlAppliactionProcess.Visible = false; lblrulesubtype.Visible = false; }
                            }
                            else
                            { lblProcess.Visible = false; }
                        }
                    }

                }


            }
            catch (Exception ex)
            {

            }
        }









    }

        #endregion Event
}
