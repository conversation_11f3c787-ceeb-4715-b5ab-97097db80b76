﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{C29AEA5F-B76F-43F7-BDEC-68740C58D51C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CP.DataAccess</RootNamespace>
    <AssemblyName>CP.DataAccess</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;MYSQL</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>DEBUG;ORACLE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>0</WarningLevel>
    <DebugSymbols>true</DebugSymbols>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Devart.Data">
      <HintPath>..\..\Thirdparty\Devart\Devart.Data.dll</HintPath>
    </Reference>
    <Reference Include="Devart.Data.Oracle">
      <HintPath>..\..\Thirdparty\Devart\Devart.Data.Oracle.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common">
      <HintPath>..\..\Thirdparty\Microsoft\Microsoft.Practices.EnterpriseLibrary.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Data">
      <HintPath>..\..\Thirdparty\Microsoft\Microsoft.Practices.EnterpriseLibrary.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.ObjectBuilder">
      <HintPath>..\..\Thirdparty\Microsoft\Microsoft.Practices.ObjectBuilder.dll</HintPath>
    </Reference>
    <Reference Include="npgsql, Version=2.2.5.0, Culture=neutral, PublicKeyToken=5d8b90d52f46fda7, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Postgress\npgsql.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.121.2.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\OracleManageData\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AccessManagerCustomRole\AccessManagerBuilder.cs" />
    <Compile Include="AccessManagerCustomRole\AccessManagerCustomDataAccess.cs" />
    <Compile Include="ActionAnalytics\ActionAnalyticBuilder.cs" />
    <Compile Include="ActionAnalytics\ActionAnalyticDataAccess.cs" />
    <Compile Include="ActivDirectoryMonitor\ActionHumanIntervention\ActionHumanInterventionBuilder.cs" />
    <Compile Include="ActivDirectoryMonitor\ActionHumanIntervention\ActionHumanInterventionDataAccess.cs" />
    <Compile Include="ActionSet\ActionSetBuilder.cs" />
    <Compile Include="ActionSet\ActionSetDataAccess.cs" />
    <Compile Include="ActivDirectoryMonitor\ActiveDirectoryMonitorBuilder.cs" />
    <Compile Include="ActivDirectoryMonitor\ActiveDirectoryMonitorDataAccess.cs" />
    <Compile Include="ActivDirectory\ActiveDirectoryBuilder.cs" />
    <Compile Include="ActivDirectory\ActiveDirectoryDataAccess.cs" />
    <Compile Include="ActiveODGMonitor\ActiveODGMonitorBuilder.cs" />
    <Compile Include="ActiveODGMonitor\ActiveODGMonitorDataAccess.cs" />
    <Compile Include="ActiveODGNonODG\ActiveODGReplicationNonOdgBuilder.cs" />
    <Compile Include="ActiveODGNonODG\ActiveODGReplicationNonOdgDataAccess.cs" />
    <Compile Include="AlertManager\AlertManagerBuilder.cs" />
    <Compile Include="AlertManager\AlertManagerDataAccess.cs" />
    <Compile Include="AlertNotification\AlertNotificationBuilder .cs" />
    <Compile Include="AlertNotification\AlertNotificationDataAccess.cs" />
    <Compile Include="AlertReceiver\AlertReceiverBuilder.cs" />
    <Compile Include="AlertReceiver\AlertReceiverDataAccess.cs" />
    <Compile Include="Alert\AlertBuilder.cs" />
    <Compile Include="Alert\AlertDataAccess.cs" />
    <Compile Include="Analytics\AnalyticsBuilder.cs" />
    <Compile Include="Analytics\AnalyticsDataAccess.cs" />
    <Compile Include="AodgRepliLogDetails\AodgRepliLogDetailsBuilder.cs" />
    <Compile Include="AodgRepliLogDetails\AodgRepliLogDetailsDataAccess.cs" />
    <Compile Include="AppDependencyMappingHosts\AppDepMappingHostsBuilder.cs" />
    <Compile Include="AppDependencyMappingHosts\AppDepMappingHostsDataAccess.cs" />
    <Compile Include="AppDependencyMapSettings\AppDepMapSettingsBuilder.cs" />
    <Compile Include="AppDependencyMapSettings\AppDepMapSettingsDataAccess.cs" />
    <Compile Include="AppDepGroupNode\AppDependencyGroupNodesBuilder.cs" />
    <Compile Include="AppDepGroupNode\AppDependencyGroupNodesDataAccess.cs" />
    <Compile Include="AppDepMapLinks\AppDepMappingLinksBuilder.cs" />
    <Compile Include="AppDepMapLinks\AppDepMappingLinksDataAccess.cs" />
    <Compile Include="AppDepMapProfileDetails\AppDepMappingProfileDetailsBuilder.cs" />
    <Compile Include="AppDepMapProfileDetails\AppDepMappingProfileDetailsDataAccess.cs" />
    <Compile Include="AppDiscovery\ApplicationDiscoveryBuilder.cs" />
    <Compile Include="AppDiscovery\ApplicationDiscoveryDataAccess.cs" />
    <Compile Include="AppDiscProfileDetails\ApplicationDiscoveryProfileDetailsBuilder.cs" />
    <Compile Include="AppDiscProfileDetails\ApplicationDiscoveryProfileDetailsDataAccess.cs" />
    <Compile Include="ApplicationDependency\ApplicationDependencyBuilder.cs" />
    <Compile Include="ApplicationDependency\ApplicationDependencyDataAccess.cs" />
    <Compile Include="ApplicationGroupInfo\ApplicationGroupInfoBuilder.cs" />
    <Compile Include="ApplicationGroupInfo\ApplicationGroupInfoDataAccess.cs" />
    <Compile Include="ApplicationGroup\ApplicationGroupBuilder.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="ApplicationGroup\ApplicationGroupDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="ApplicationMonitor\ApplicationMonitorBuilder.cs" />
    <Compile Include="ApplicationMonitor\ApplicationMonitorDataAccess.cs" />
    <Compile Include="ASMGrid\ASMGridBuilder.cs" />
    <Compile Include="ASMGrid\ASMGridDataAccess.cs" />
    <Compile Include="AzureMonitor\AzureMonitorBuilder.cs" />
    <Compile Include="AzureMonitor\AzureMonitorDataAccess.cs" />
    <Compile Include="AzureSiteRecoveryRep\AzureRecSiteBuilder.cs" />
    <Compile Include="AzureSiteRecoveryRep\AzureRecSiteDataAccess.cs" />
    <Compile Include="Base24Replication\Base24RepliBuilder.cs" />
    <Compile Include="Base24Replication\Base24RepliDataAccess.cs" />
    <Compile Include="Base24RepliMonitor\Base24RepliMonitorBuilder.cs" />
    <Compile Include="Base24RepliMonitor\Base24RepliMonitorDataAccess.cs" />
    <Compile Include="BFBIAMatrixDetails\BFBIAMatrixDetailsBuilder.cs" />
    <Compile Include="BFBIAMatrixDetails\BFBIAMatrixDetailsDataAccess.cs" />
    <Compile Include="BFBIAMatrix\BFBIAMatrixBuilder.cs" />
    <Compile Include="BFBIAMatrix\BFBIAMatrixDataAccess.cs" />
    <Compile Include="BIAActionCountCompletedOutOfRTOBySol\BIAActCntCompOutOfRTOBySolBuilder.cs" />
    <Compile Include="BIAActionCountCompletedOutOfRTOBySol\BIAActionCountCompletedOutOfRTOBySolDataAccess.cs" />
    <Compile Include="BIAActionEffiAutoMode\BIAActionEffiAutoModeBuilder.cs" />
    <Compile Include="BIAActionEffiAutoMode\BIAActionEffiAutoModeDataAccess.cs" />
    <Compile Include="BIAActionEfficiency\BIAActionEfficiencyBuilder.cs" />
    <Compile Include="BIAActionEfficiency\BIAActionEfficiencyDataAccess.cs" />
    <Compile Include="BIAActionFailureHumanInterventionTrend\BIAActionFailureHumanInterventionTrendBuilder.cs" />
    <Compile Include="BIAActionFailureHumanInterventionTrend\BIAActionFailureHumanInterventionTrendDataAccess.cs" />
    <Compile Include="BIAActiontrendComponent\BiaActiontrendComponentBuilder.cs" />
    <Compile Include="BIAActiontrendComponent\BiaActiontrendComponentDataAccess.cs" />
    <Compile Include="BIAAlertCountBusinessServiceWise\BIAAlertCountBusinessServiceWiseBuilder.cs" />
    <Compile Include="BIAAlertCountBusinessServiceWise\BIAAlertCountBusinessServiceWiseDataAccess.cs" />
    <Compile Include="BIAAlertsTrendBusinessService\BIAAlertsTrendBusinessServiceBuilder.cs" />
    <Compile Include="BIAAlertsTrendBusinessService\BIAAlertsTrendBusinessServiceDataAccess.cs" />
    <Compile Include="BIAFailAction\BIAFailedActionBuilder.cs" />
    <Compile Include="BIAFailAction\BIAFailedActionDataAccess.cs" />
    <Compile Include="BIAFailedWorkflowByWorkflowType\BIAFailedWorkflowByWorkflowTypeBuilder.cs" />
    <Compile Include="BIAFailedWorkflowByWorkflowType\BIAFailedWorkflowByWorkflowTypeDataAccess.cs" />
    <Compile Include="BIAFailedWorkflow\BIAFailedWorkflowBuilder.cs" />
    <Compile Include="BIAFailedWorkflow\BIAFailedWorkflowDataAccess.cs" />
    <Compile Include="BIAFailureActinBySolType\BIAFailureActinBySolTypeBuilder.cs" />
    <Compile Include="BIAFailureActinBySolType\BIAFailureActinBySolTypeDataAccess.cs" />
    <Compile Include="BIAFailureActinBySOSB\BIAFailureActinBySOSBBuilder.cs" />
    <Compile Include="BIAFailureActinBySOSB\BIAFailureActinBySOSBDataAccess.cs" />
    <Compile Include="BIAFailureActionByWorkflowType\BIAFailureActionByWorkflowTypeBuilder.cs" />
    <Compile Include="BIAFailureActionByWorkflowType\BIAFailureActionByWorkflowTypeDataAccess.cs" />
    <Compile Include="BIAGetAlertDetails\BIAGetAlertDetailsBuilder.cs" />
    <Compile Include="BIAGetAlertDetails\BIAGetAlertDetailsDataAccess.cs" />
    <Compile Include="BIAHuminterventionsAction\BIAHuminterventionsActionBuilder.cs" />
    <Compile Include="BIAHuminterventionsAction\BIAHuminterventionsActionDataAccess.cs" />
    <Compile Include="BIAImpactCount\BIAImpactCountBuilder.cs" />
    <Compile Include="BIAImpactCount\BIAImpactCountDataAccess.cs" />
    <Compile Include="BIAOverallAlertStatistics\BIAOverallAlertStatisticsBuilder.cs" />
    <Compile Include="BIAOverallAlertStatistics\BIAOverallAlertStatisticsDataAccess.cs" />
    <Compile Include="BIAOvervallWorkflowStatistics\BIAOvervallWorkflowStatisticsBuilder.cs" />
    <Compile Include="BIAOvervallWorkflowStatistics\BIAOvervallWorkflowStatisticsDatatAccess.cs" />
    <Compile Include="BIAProfileDetailsActionEfficiencyTrend\BIAProfileDetailsActionEfficiencyTrendBuilder.cs" />
    <Compile Include="BIAProfileDetailsActionEfficiencyTrend\BIAProfileDetailsActionEfficiencyTrendDataAccess.cs" />
    <Compile Include="BIAProfile\BusinessProfileBuilder.cs" />
    <Compile Include="BIAProfile\BusinessProfileDataAccess.cs" />
    <Compile Include="BIASuccessWFHI\BIASuccessWFvsHIBuilder.cs" />
    <Compile Include="BIASuccessWFHI\BIASuccessWFvsHIDataAccess.cs" />
    <Compile Include="BIATimeInterval\BusinessTimeIntervalBuilder.cs" />
    <Compile Include="BIATimeInterval\BusinessTimeIntervalDataAccess.cs" />
    <Compile Include="BIAWorkflowAnalyatic\BIAWorkflowAnalyaticBuilder.cs" />
    <Compile Include="BIAWorkflowAnalyatic\BIAWorkflowAnalyaticDataAccess.cs" />
    <Compile Include="BIAWorkflowCompletedWithinRTO\BIAWorkflowCompletedWithinRTOBuilder.cs" />
    <Compile Include="BIAWorkflowCompletedWithinRTO\BIAWorkflowCompletedWithinRTODataAccess.cs" />
    <Compile Include="BIAWorkflowefficiencyTrend\BIAWorkflowefficiencyTrendBuilder.cs" />
    <Compile Include="BIAWorkflowefficiencyTrend\BIAWorkflowefficiencyTrendDataAccess.cs" />
    <Compile Include="BIAWorkflowfailureprofileTrend\BIAWorkflowfailureprofileTrendBuilder.cs" />
    <Compile Include="BIAWorkflowfailureprofileTrend\BIAWorkflowfailureprofileTrendDataAccess.cs" />
    <Compile Include="BIAWorkflowFailureWorkflowType\BIAWorkflowFailureWorkflowTypeBuilder.cs" />
    <Compile Include="BIAWorkflowFailureWorkflowType\BIAWorkflowFailureWorkflowTypeDataAccess.cs" />
    <Compile Include="BIAWorkflowProfilesHumanInterventionsTrend\BIAWorkflowProfilesHumanInterventionsTrendBuilder.cs" />
    <Compile Include="BIAWorkflowProfilesHumanInterventionsTrend\BIAWorkflowProfilesHumanInterventionsTrendDataAccess.cs" />
    <Compile Include="BIAWorkflowTrendsAll\BIAWorkflowTrendsAllBuilder.cs" />
    <Compile Include="BIAWorkflowTrendsAll\BIAWorkflowTrendsAllDataAccess.cs" />
    <Compile Include="BSDRReadyDaily\BSDRReadyBuilder.cs" />
    <Compile Include="BSDRReadyDaily\BSDRReadyDailyDataAccess.cs" />
    <Compile Include="BusinessFunctionBIAActivity\BusinessFunctionBIAActivityBuilder.cs" />
    <Compile Include="BusinessFunctionBIAActivity\BusinessFunctionBIAActivityDataAccess.cs" />
    <Compile Include="BusinessFunctionBIADetails\BusinessFunctionBIADetailsBuilder.cs" />
    <Compile Include="BusinessFunctionBIADetails\BusinessFunctionBIADetailsDataAccess.cs" />
    <Compile Include="BusinessFunctionBIARelation\BusinessFunctionBIARelationBuilder.cs" />
    <Compile Include="BusinessFunctionBIARelation\BusinessFunctionBIARelationDataAccess.cs" />
    <Compile Include="BusinessFunctionBIASeverity\BusinessFunctionBIASeverityBuilder.cs" />
    <Compile Include="BusinessFunctionBIASeverity\BusinessFunctionBIASeverityDataAccess.cs" />
    <Compile Include="BusinessFunctionBIA\BusinessFunctionBIABuilder.cs" />
    <Compile Include="BusinessFunctionBIA\BusinessFunctionBIADataAccess.cs" />
    <Compile Include="BusinessFunctionRPO\BusinessFunctionRPOBuilders.cs" />
    <Compile Include="BusinessFunctionRPO\BusinessFunctionRPODataAccess.cs" />
    <Compile Include="BusinessFuntion\BusinessFunctionBuilder.cs" />
    <Compile Include="BusinessFuntion\BusinessFunctionDataAccess.cs" />
    <Compile Include="BusinessImpact\BusinessImpactBuilder.cs" />
    <Compile Include="BusinessImpact\BusinessImpactDataAccess.cs" />
    <Compile Include="BusinessProfileImpactType\BIAProfileImpactTypesBuilder.cs" />
    <Compile Include="BusinessProfileImpactType\BIAProfileImpactTypesDataAccess.cs" />
    <Compile Include="BusinessProfileTimeInterval\BIAProfileTimeIntervalsBuilder.cs" />
    <Compile Include="BusinessProfileTimeInterval\BIAProfileTimeIntervalsDataAccess.cs" />
    <Compile Include="BusinessServiceAvailability\BusinessServiceAvailabilityBuilder.cs" />
    <Compile Include="BusinessServiceAvailability\BusinessServiceAvailabilityDataAccess.cs" />
    <Compile Include="BusinessServiceRPO\BusinessServiceRPOInfoBuilder.cs" />
    <Compile Include="BusinessServiceRPO\BusinessServiceRPOInfoDataAccess.cs" />
    <Compile Include="BusinessServiceRTO\BusinessServiceRTOInfoBuilder.cs" />
    <Compile Include="BusinessServiceRTO\BusinessServiceRTOInfoDataAccess.cs" />
    <Compile Include="BusinessService\BusinessServiceBuilder.cs" />
    <Compile Include="BusinessService\BusinessServiceDataAccess.cs" />
    <Compile Include="ApplicationService\ApplicationServiceDataAccess.cs" />
    <Compile Include="ApplicationService\ApplicationServiceBuilder.cs" />
    <Compile Include="Archive\ArchiveBuilder.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Archive\ArchiveDataAccess.cs" />
    <Compile Include="Audit\AuditBuilder.cs" />
    <Compile Include="Audit\AuditDataAccess.cs" />
    <Compile Include="CGMonitoring\CGMonitoringBuilder.cs" />
    <Compile Include="CGMonitoring\CGMonitoringDataAccess.cs" />
    <Compile Include="CGVolumeMonitoring\CGVolumeMonitoringBuilder.cs" />
    <Compile Include="CGVolumeMonitoring\CGVolumeMonitoringDataAccess.cs" />
    <Compile Include="ClodantDBReplication\CloudantDBReplicationBuilder.cs" />
    <Compile Include="ClodantDBReplication\CloudantDBReplicationDataAccess.cs" />
    <Compile Include="CloudantDBMonitoring\CloudantDBMonitorBuilder.cs" />
    <Compile Include="CloudantDBMonitoring\CloudantDBMonitorDataAccess.cs" />
    <Compile Include="CloudantDBReplicationJob\CloudantDBReplicationJobBuilder.cs" />
    <Compile Include="CloudantDBReplicationJob\CloudantDBReplicationJobDataAccess.cs" />
    <Compile Include="CloudantLBMonitoring\CloudantLBMonitorBuilder.cs" />
    <Compile Include="CloudantLBMonitoring\CloudantLBMonitorDataAccess.cs" />
    <Compile Include="CloudantReplicationMonitoring\CloudantRepliMonitorBuilder.cs" />
    <Compile Include="CloudantReplicationMonitoring\CloudantRepliMonitorDataAccess.cs" />
    <Compile Include="ClusterNodeMonitor\ClusterNodeBuilder.cs" />
    <Compile Include="ClusterNodeMonitor\ClusterNodeDataAccess.cs" />
    <Compile Include="ClusterSummaryMonitor\ClusterSummaryBuilder.cs" />
    <Compile Include="ClusterSummaryMonitor\ClusterSummaryDataAccess.cs" />
    <Compile Include="ClustorMonitoring\ClustorMonitoringBuilder.cs" />
    <Compile Include="ClustorMonitoring\ClustorMonitoringDataAccess.cs" />
    <Compile Include="ComponentFailureDaily\ComponentFailureDailyBuilder.cs" />
    <Compile Include="ComponentFailureDaily\ComponentFailureDailyDataAccess.cs" />
    <Compile Include="CPSLScheduled\CPSLScheduledBuilder.cs" />
    <Compile Include="CPSLScheduled\CPSLScheduledDataAccess.cs" />
    <Compile Include="CPSL\CPSLScriptBuilder.cs" />
    <Compile Include="CPSL\CPSLScriptDataAccess.cs" />
    <Compile Include="CustomSubRoleTypess\CustomSubRoleTypeBuilder.cs" />
    <Compile Include="CustomSubRoleTypess\CustomSubRoleTypeDataAccess.cs" />
    <Compile Include="DAGConfigSummary\DagSummaryBuilder.cs" />
    <Compile Include="DAGConfigSummary\DagSummaryDataAccess.cs" />
    <Compile Include="DataBaseCloudantNoSQL\DatabaseCloudantNoSQLBuilder.cs" />
    <Compile Include="DataBaseCloudantNoSQL\DatabaseCloudantNoSQlDataAccess.cs" />
    <Compile Include="DatabaseHanaDb\DatabaseHanaDbBuilder.cs" />
    <Compile Include="DatabaseHanaDb\DatabaseHanaDbDataAccess.cs" />
    <Compile Include="DatabaseMaria\DatabaseMariaBuilder.cs" />
    <Compile Include="DatabaseMaria\DatabaseMariaDataAccess.cs" />
    <Compile Include="DatabaseMaxDB\DatabaseMaxDBBuilder.cs" />
    <Compile Include="DatabaseMaxDB\DatabaseMaxDBDataAccess.cs" />
    <Compile Include="DatabaseMSSQLMonitor\DataBaseMSSqlMonitorBuilder.cs" />
    <Compile Include="DatabaseMSSQLMonitor\DataBaseMSSqlMonitorDataAccess.cs" />
    <Compile Include="DataBaseMSSqlNative\DataBaseMSSqlBuilder.cs" />
    <Compile Include="DataBaseMSSqlNative\DataBaseMSSqlDataAccess.cs" />
    <Compile Include="DatabaseOceanstorMssql\DatabaseOceanstorMssqlBuilder.cs" />
    <Compile Include="DatabaseOceanstorMssql\DatabaseOceanstorMssqlDataAccess.cs" />
    <Compile Include="DatabasePostgre9x\DatabasePostgre9xBuilder.cs" />
    <Compile Include="DatabasePostgre9x\DatabasePostgre9xDataAccess.cs" />
    <Compile Include="DatabasePostgreSql\DatabasePostgreSqlBuilder.cs" />
    <Compile Include="DatabasePostgreSql\DatabasepostgreSqlDataAccess.cs" />
    <Compile Include="DatabaseRole\DatabaseRoleBuilder.cs" />
    <Compile Include="DatabaseRole\DatabaseRoleDataAccess.cs" />
    <Compile Include="DatabaseSqlplus\DatabaseSqlPlusBuilder.cs" />
    <Compile Include="DatabaseSqlplus\DatabaseSqlPlusDataAccess.cs" />
    <Compile Include="DatabaseSubstituteAuthenticate\DatabaseSubstituteAuthenticateBuilder.cs" />
    <Compile Include="DatabaseSubstituteAuthenticate\DatabaseSubstituteAuthenticateDataAccess.cs" />
    <Compile Include="DataBaseSyBaseWithRsHadr\DataBaseSybaseWithRsHadrBuilder.cs" />
    <Compile Include="DataBaseSyBaseWithRsHadr\DataBaseSybaseWithRsHadrDBDataAccess.cs" />
    <Compile Include="DataBaseSyBaseWithSrs\DataBaseSybaseWithSrsBuilder.cs" />
    <Compile Include="DataBaseSyBaseWithSrs\DataBaseSybaseWithSrsDBDataAccess.cs" />
    <Compile Include="DatabaseSYBase\DataBaseSybaseBuilder.cs" />
    <Compile Include="DatabaseSYBase\DataBaseSyBaseDBDataAccess.cs" />
    <Compile Include="DefaultMoniService\DefaultMoniServiceBuilder.cs" />
    <Compile Include="DefaultMoniService\DefaultMoniServiceDataAccess.cs" />
    <Compile Include="DiscoverScan\DiscoverScanBuilder.cs" />
    <Compile Include="DiscoverScan\DiscoverScanDataAccess.cs" />
    <Compile Include="DiscoveryConfiguration\DiscoveryConfigurationBuilder.cs" />
    <Compile Include="DiscoveryConfiguration\DiscoveryConfigurationDataAccess.cs" />
    <Compile Include="DiscoveryHostLogs\DiscoveryHostLogsBuilder.cs" />
    <Compile Include="DiscoveryHostLogs\DiscoveryHostLogsDataAccess.cs" />
    <Compile Include="DropDown\DropDownAccess.cs" />
    <Compile Include="DropDown\DropDownBuilder.cs" />
    <Compile Include="eBDRProfileReplication\eBDRProfileRepliDataAccess.cs" />
    <Compile Include="eBDRProfileReplication\eBDRReplicationProfileBuilder.cs" />
    <Compile Include="eBDRProfileRepli\eBDRProfileRepliDetailsBuilder.cs" />
    <Compile Include="eBDRProfileRepli\eBDRProfileRepliNewDataAccess.cs" />
    <Compile Include="eBDR\eBDRProfileDetailsBuilder.cs" />
    <Compile Include="eBDR\EBDRReplicationDataAcess.cs" />
    <Compile Include="Ec2S3DataSyncMonitor\Ec2S3DataSyncMonitorBuilder.cs" />
    <Compile Include="Ec2S3DataSyncMonitor\Ec2S3DataSyncMonitorDataAccess.cs" />
    <Compile Include="Ec2S3DataSyncReplicationMonitor\Ec2S3DataSyncReplicationMonitorDataAccess.cs" />
    <Compile Include="Ec2S3DataSyncReplicationMonitor\Ec2S3DataSyncReplicationMonitorDataBuilder.cs" />
    <Compile Include="EC2S3DataSyncRep\EC2S3DataSyncReplicationBuilder.cs" />
    <Compile Include="EC2S3DataSyncRep\EC2S3DataSyncReplicationDataAccess.cs" />
    <Compile Include="EmailReport\EmailReportBuilder.cs" />
    <Compile Include="EmailReport\EmailReportDataAccess.cs" />
    <Compile Include="EMCISilonReplicationPolicy\EmcIsilonRepliPolicyBuilder.cs" />
    <Compile Include="EMCISilonReplicationPolicy\EmcIsilonRepliPolicyDataAccess.cs" />
    <Compile Include="EMCISilonReplication\EmcIsilonReplicationBuilder.cs" />
    <Compile Include="EMCISilonReplication\EmcIsilonReplicationDataAccess.cs" />
    <Compile Include="EmcISilonRepliMonitor\EmcISilonDataAccess.cs" />
    <Compile Include="EmcISilonRepliMonitor\EmcISilonMonitorBuilder.cs" />
    <Compile Include="EmcMirrorViewReplication\EmcMirrorViewRepliBuilder.cs" />
    <Compile Include="EmcMirrorViewReplication\EmcMirrorViewRepliDataAccess.cs" />
    <Compile Include="EmcMirrorViewRepliMonitoring\EmcMirrorViewRepliMonitorBuilder.cs" />
    <Compile Include="EmcMirrorViewRepliMonitoring\EmcMirrorViewRepliMonitorDataAccess.cs" />
    <Compile Include="EMCSRDFCGMonitor\EMCSRDFCGMonitorBuilder.cs" />
    <Compile Include="EMCSRDFCGMonitor\EMCSRDFCGMonitorDataAccess.cs" />
    <Compile Include="EMCSRDFCGRep\EMCSRDFCGBuilder.cs" />
    <Compile Include="EMCSRDFCGRep\EMCSRDFCGDataAccess.cs" />
    <Compile Include="EmcsrdfSGLogs\EmcsrdfSGLogsBuilder.cs" />
    <Compile Include="EmcsrdfSGLogs\EmcsrdfSGLogsDataAccess.cs" />
    <Compile Include="EMCSRDFSG\EMCSRDFSGBuilder.cs" />
    <Compile Include="EMCSRDFSG\EMCSRDFSGDataAccess.cs" />
    <Compile Include="EMCSRDFStarMonitor\EMCSRDFStarMonitorBuilder.cs" />
    <Compile Include="EMCSRDFStarMonitor\EMCSRDFStarMonitorDataAccess.cs" />
    <Compile Include="EMCSRDFStar\EMCSRDFStarBuilder.cs" />
    <Compile Include="EMCSRDFStar\EMCSRDFStarDataAccess.cs" />
    <Compile Include="EmcUnityReplication\EmcUnityRepliBuilder.cs" />
    <Compile Include="EmcUnityReplication\EmcUnityRepliDataAccess.cs" />
    <Compile Include="EmcUnityRepliMonitoring\EmcUnityRepliMonitorBuilder.cs" />
    <Compile Include="EmcUnityRepliMonitoring\EmcUnityRepliMonitorDataAccess.cs" />
    <Compile Include="Emc_MV_MirrorMonitoring\Emc_MV_MirrorMonitorBuilder.cs" />
    <Compile Include="Emc_MV_MirrorMonitoring\Emc_MV_MirrorMonitorDataAccess.cs" />
    <Compile Include="ExchangCompMontrStatus\ExchangMntrStatsDataAccess.cs" />
    <Compile Include="ExchangCompMontrStatus\ExchngMontrStatsBuilder.cs" />
    <Compile Include="ExChangeHeathSumryDataAccess\ExchangeReplHealthSummaryBuilder .cs" />
    <Compile Include="ExChangeHeathSumryDataAccess\ExchngHealtSumryDataAccess.cs" />
    <Compile Include="GoldenGateDBMonitors\GoldenGateDBMonitorBuilder.cs" />
    <Compile Include="GoldenGateDBMonitors\GoldenGateMonitorDataAccess.cs" />
    <Compile Include="GoldenGateGroupDetail\GoldenGateGroupDetBuilder.cs" />
    <Compile Include="GoldenGateGroupDetail\GoldenGateGroupDetDataAccess.cs" />
    <Compile Include="GoldenGateReplication\GoldenGateRepliBuilder.cs" />
    <Compile Include="GoldenGateReplication\GoldenGateRepliDataAccess.cs" />
    <Compile Include="GoldenGateRepliMonitor\GoldenGateRepliMonitorBuilder.cs" />
    <Compile Include="GoldenGateRepliMonitor\GoldenGateRepliMonitorDataAccess.cs" />
    <Compile Include="HACMPClusterDetailsMonitor\HACMPClusterDetailsMonitorBuilder.cs" />
    <Compile Include="HACMPClusterDetailsMonitor\HACMPClusterDetailsMonitorDataAccess.cs" />
    <Compile Include="HACMPCluster\HACMPClusterBuilder.cs" />
    <Compile Include="HACMPCluster\HACMPClusterDataAccess.cs" />
    <Compile Include="HACMPResourceGroupsMonitor\HACMPResourceGroupsMonitorBuilder.cs" />
    <Compile Include="HACMPResourceGroupsMonitor\HACMPResourceGroupsMonitorDataAccess.cs" />
    <Compile Include="HANADBMonitoring\HanaDbMonitorBuilder.cs" />
    <Compile Include="HANADBMonitoring\HanaDbMonitorDataAccess.cs" />
    <Compile Include="HanaDBReplicationMode\HanaDbReplicationModeBuilder.cs" />
    <Compile Include="HanaDBReplicationMode\HanaDbReplicationModeDataAccess.cs" />
    <Compile Include="HanaDBService\HanaDbDatabaseServiceBuilder.cs" />
    <Compile Include="HanaDBService\HanaDbDatabaseServiceDataAccess.cs" />
    <Compile Include="HanaDBSystemOperationMode\HanaDbSystemOperationModeBuilder.cs" />
    <Compile Include="HanaDBSystemOperationMode\HanaDbSystemOperationModeDataAccess.cs" />
    <Compile Include="HP3PARRepliMonitor\HP3PARMonitorBuilder.cs" />
    <Compile Include="HP3PARRepliMonitor\HP3PARMonitorDataAccess.cs" />
    <Compile Include="HP3PARStorage\HP3PARStorageBuilder.cs" />
    <Compile Include="HP3PARStorage\HP3PARStorageDataAccess.cs" />
    <Compile Include="HuaweiStorages\HuaweiStorageBuilder.cs" />
    <Compile Include="HuaweiStorages\HuaweiStorageDataAccess.cs" />
    <Compile Include="HuwaiStorageMonitor\HuwaiStorageMonitorStatslogsBuilder.cs" />
    <Compile Include="HuwaiStorageMonitor\HuwaiStorageMonitorStatslogsDataAccess.cs" />
    <Compile Include="HyperVMonitor\HyperVMonitorBuilder.cs" />
    <Compile Include="HyperVMonitor\HyperVMonitorDataAccess.cs" />
    <Compile Include="HyperVReplication\HyperVRepliBuilder.cs" />
    <Compile Include="HyperVReplication\HyperVRepliDataAccess.cs" />
    <Compile Include="IBMXIVReplication\IBMXIVMirrorBuilder.cs" />
    <Compile Include="IBMXIVReplication\IBMXIVMirrorDataAccess.cs" />
    <Compile Include="IBMXIVReplimonitor\IBMXIVReplimonitorBuilder.cs" />
    <Compile Include="IBMXIVReplimonitor\IBMXIVReplimonitorDataAccess.cs" />
    <Compile Include="ImportCMDB\ImportCMDBBuilder.cs" />
    <Compile Include="ImportCMDB\ImportCMDBDataAccess.cs" />
    <Compile Include="IncidentManagementBIASummary\IncidentManagementBIASummaryBuilder.cs" />
    <Compile Include="IncidentManagementBIASummary\IncidentManagementBIASummaryDataAccess.cs" />
    <Compile Include="IncidentManagementSummary\IncidentManagementSummaryBuilder.cs" />
    <Compile Include="IncidentManagementSummary\IncidentManagementSummaryDataAccess.cs" />
    <Compile Include="ImpactRelType\ImpactRelTypeBuilder.cs" />
    <Compile Include="ImpactRelType\ImpactRelTypeDataAccess.cs" />
    <Compile Include="IncidentManagementNew\IncidentManagementNewBuilder.cs" />
    <Compile Include="IncidentManagementNew\IncidentManagementNewDataAccess.cs" />
    <Compile Include="InfraobjectCGNameDataAccess\InfraobjectCGNameBuilder.cs" />
    <Compile Include="InfraobjectCGNameDataAccess\InfraobjectCGNameDataAccess.cs" />
    <Compile Include="InfraobjectDiskMonitor\InfraobjectDiskMonitorBuilder.cs" />
    <Compile Include="InfraobjectDiskMonitor\InfraobjectDiskMonitorDataAccess.cs" />
    <Compile Include="InfraobjectScheduledWF\InfraobjectScheduleWFBuilder.cs" />
    <Compile Include="InfraobjectScheduledWF\InfraobjectScheduleWFDataAccess.cs" />
    <Compile Include="InfraobjectScheduleStatelogs\InfraScheWFBuilder.cs" />
    <Compile Include="InfraobjectScheduleStatelogs\InfraScheWFDataAccess.cs" />
    <Compile Include="InfraobjectVolumeNameDetails\InfraobjectVolumeNameBuilder.cs" />
    <Compile Include="InfraobjectVolumeNameDetails\InfraobjectVolumeNameDataAccess.cs" />
    <Compile Include="InfraobjGlobalMirrorLunsdetails\InfraobjGlobalmirrorDetailsDataAccess.cs" />
    <Compile Include="InfraobjGlobalMirrorLunsdetails\InfraobjGlobalMirrorLunsDetailsBuilder.cs" />
    <Compile Include="InfraobjGlobalMirrorluns\InfraobjGlobalMirrorLunsBuilder.cs" />
    <Compile Include="InfraobjGlobalMirrorluns\InfraobjGlobalMirrorlunsDataAccess.cs" />
    <Compile Include="InfraSchedularLogs\InfrScheLogsBuilder.cs" />
    <Compile Include="InfraSchedularLogs\InfrScheLogsDataAccess.cs" />
    <Compile Include="InfraSchedularStatus\InfrScheStatusBuilder.cs" />
    <Compile Include="InfraSchedularStatus\InfrScheStatusDataAccess.cs" />
    <Compile Include="LoadMaster\CPLoadMasterBuilder.cs" />
    <Compile Include="LoadMaster\CPLoadMasterDataAccess.cs" />
    <Compile Include="LogFileDetails\LogFileDetailsBuilder.cs" />
    <Compile Include="LogFileDetails\LogFileDetailsDataAccess.cs" />
    <Compile Include="LogViewer\LogViewerBuilder.cs" />
    <Compile Include="LogViewer\LogViewerDataAccess.cs" />
    <Compile Include="MariaDBMonitor\MariaDBMonitorBuilder.cs" />
    <Compile Include="MariaDBMonitor\MariaDBMonitorDataAccess.cs" />
    <Compile Include="MaxDBMonitor\MaxDBMonitorBuilder.cs" />
    <Compile Include="MaxDBMonitor\MaxDBMonitorDataAccess.cs" />
    <Compile Include="MaxDBReplication\MaxDBReplicationBuilder.cs" />
    <Compile Include="MaxDBReplication\MaxDBReplicationDataAccess.cs" />
    <Compile Include="MaxFullDBEmcSrdfMonitor\MaxFullDBEmcSrdfMonitorBuilder.cs" />
    <Compile Include="MaxFullDBEmcSrdfMonitor\MaxFullDBEmcSrdfMonitorDataAccess.cs" />
    <Compile Include="MimixAlertsMonitor\MimixAlertsBuilder.cs" />
    <Compile Include="MimixAlertsMonitor\MimixAlertsDataAccess.cs" />
    <Compile Include="MimixAvilabilityMonitor\MimixAvilabilityBuilder.cs" />
    <Compile Include="MimixAvilabilityMonitor\MimixAvilabilityDataAccess.cs" />
    <Compile Include="MimixDatalagMonitor\MimixDatalagBuilder.cs" />
    <Compile Include="MimixDatalagMonitor\MimixDatalagDataAccess.cs" />
    <Compile Include="MimixHealthReplcation\MimixHealthBuilder.cs" />
    <Compile Include="MimixHealthReplcation\MimixHealthDataAccess.cs" />
    <Compile Include="MimixManagerMonitor\MimixManagerBuilder.cs" />
    <Compile Include="MimixManagerMonitor\MimixManagerDataAccess.cs" />
    <Compile Include="MimixReplication\MimixRepliBuilder.cs" />
    <Compile Include="MimixReplication\MimixRepliDataAccess.cs" />
    <Compile Include="MongoDBDMonitorStatus\MongoDBDMonitorStatusBuilder.cs" />
    <Compile Include="MongoDBDMonitorStatus\MongoDBDMonitorStatusDataAccess.cs" />
    <Compile Include="MongoDB\MongoDataBaseBuilder.cs" />
    <Compile Include="MongoDB\MongoDataBaseDataAccess.cs" />
    <Compile Include="MonitorQueue\MonitorQueueBuilder.cs" />
    <Compile Include="MonitorQueue\MonitorQueueDataAccess.cs" />
    <Compile Include="MonitorServiceStatusLogs\MonitorServiceStatusLogsBuilder.cs" />
    <Compile Include="MonitorServiceStatusLogs\MonitorServiceStatusLogsDataAccess.cs" />
    <Compile Include="DatabaseBase\DatabaseBaseBuilder.cs" />
    <Compile Include="DatabaseBase\DatabaseBaseDataAccess.cs" />
    <Compile Include="Base\BaseDataAccess.cs" />
    <Compile Include="Base\BaseEntityBuilder.cs" />
    <Compile Include="Base\DataAccessFactory.cs" />
    <Compile Include="BusinessInfo\BusinessInfoBuilder.cs" />
    <Compile Include="BusinessInfo\BusinessInfoDataAccess.cs" />
    <Compile Include="BusinessProcessAutomation\BPAutomationBuilder.cs" />
    <Compile Include="BusinessProcessAutomation\BPAutomationdDataAccess.cs" />
    <Compile Include="BusinessUserFunction\BusinessUserFunctionBuilder.cs" />
    <Compile Include="BusinessUserFunction\BusinessUserFunctionDataAccess.cs" />
    <Compile Include="CompanyInfo\CompanyInfoBuilder.cs" />
    <Compile Include="CompanyInfo\CompanyInfoDataAccess.cs" />
    <Compile Include="CompanyProfile\CompanyProfileBuilder.cs" />
    <Compile Include="CompanyProfile\CompanyProfileDataAccess.cs" />
    <Compile Include="CustomException\CustomExceptionBuilder.cs" />
    <Compile Include="CustomException\CustomExceptionDataAccess.cs" />
    <Compile Include="DatabaseBackupInfo\DatabaseBackupInfoBuilder.cs" />
    <Compile Include="DatabaseBackupInfo\DatabaseBackupInfoDataAccess.cs" />
    <Compile Include="DatabaseBackupOperation\DatabaseBackupOperationBuilder.cs" />
    <Compile Include="DatabaseBackupOperation\DatabaseBackupOperationDataAccess.cs" />
    <Compile Include="DatabaseDB2\DatabaseDB2Builder.cs" />
    <Compile Include="DatabaseDB2\DatabaseDb2DataAccess.cs" />
    <Compile Include="DatabaseExchangeDAG\DatabaseExchangeDAGBuilder.cs" />
    <Compile Include="DatabaseExchangeDAG\DatabaseExchangeDAGDataAccess.cs" />
    <Compile Include="DatabaseExchange\DatabaseExchangeBuilder.cs" />
    <Compile Include="DatabaseExchange\DatabaseExchangeDataAccess.cs" />
    <Compile Include="DataBaseMySql\DatabaseMySqlBuilder.cs" />
    <Compile Include="DataBaseMySql\DatabaseMySqlDataAccess.cs.cs" />
    <Compile Include="DatabaseNodes\DatabaseNodesBuilder.cs" />
    <Compile Include="DatabaseNodes\DatabaseNodesDataAccess.cs" />
    <Compile Include="DatabaseOracle\DatabaseOracleBuilder.cs" />
    <Compile Include="DatabaseOracle\DatabaseOracleDataAccess.cs" />
    <Compile Include="DatabaseOracleRac\DatabaseOracleRacBuilder.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DatabaseOracleRac\DatabaseRacDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DatabaseSql\DatabaseSqlBuilder.cs" />
    <Compile Include="DatabaseSql\DatabaseSqlDataAccess.cs" />
    <Compile Include="DatabaseVersion\DatabaseVersionBuilder.cs" />
    <Compile Include="DatabaseVersion\DatabaseVersionDataAccess.cs" />
    <Compile Include="DataSyncProperties\DataSyncPropertiesBuilder.cs" />
    <Compile Include="DataSyncProperties\DataSyncPropertiesDataAccess.cs" />
    <Compile Include="DB2DataSyncMonitor\DB2DataSyncMonitorBuilder.cs" />
    <Compile Include="DB2DataSyncMonitor\DB2DataSyncMonitorDataAccess.cs" />
    <Compile Include="DnsServerDetails\DnsServerDetailsBuilder.cs" />
    <Compile Include="DnsServerDetails\DnsServerDetailsDataAccess.cs" />
    <Compile Include="DomainDetails\DomainDetailsBuilder.cs" />
    <Compile Include="DomainDetails\DomainDetailsDataAccess.cs" />
    <Compile Include="DROperationResult\DROperationResultBuilder.cs" />
    <Compile Include="DROperationResult\DROperationResultDataAccess.cs.cs" />
    <Compile Include="DROperation\DROperationBuilder.cs" />
    <Compile Include="DROperation\DROperationDataAccess.cs" />
    <Compile Include="EMCDeviceDetails\EMCDeviceDetailsDataAccess.cs" />
    <Compile Include="EMCDeviceDetails\EMCDeviceDetailsBuilder.cs" />
    <Compile Include="EMCSRDF\EMCSRDFBuilder.cs" />
    <Compile Include="EMCSRDF\EMCSRDFDataAccess.cs" />
    <Compile Include="EventManagementList\EventManagementListBuilder.cs" />
    <Compile Include="EventManagementList\EventManagementListDataAccess.cs" />
    <Compile Include="EventManagement\EventManagementBuilder.cs" />
    <Compile Include="EventManagement\EventManagementDataAccess.cs" />
    <Compile Include="ExchangeDAGCompMonitor\ExchangeDAGCompMonitorBuilder.cs" />
    <Compile Include="ExchangeDAGCompMonitor\ExchangeDAGCompMonitorDataAccess.cs" />
    <Compile Include="ExchangeDAGMonitoring\ExchangeDAGMonitoringBuilder.cs" />
    <Compile Include="ExchangeDAGMonitoring\ExchangeDAGMonitoringDataAccess.cs" />
    <Compile Include="ExchangeDAGReplHealthStatus\ExchangeDAGReplHealthStatusBuilder.cs" />
    <Compile Include="ExchangeDAGReplHealthStatus\ExchangeDAGReplHealthStatusDataAccess.cs" />
    <Compile Include="ExchangeDAGRep\ExchageDAGReplicationBuilder.cs" />
    <Compile Include="ExchangeDAGRep\ExchangeDAGReplicationDataAccess.cs" />
    <Compile Include="ExchangeDAGServiceMonitoring\ExchangeDAGServiceMonitoringBuilder.cs" />
    <Compile Include="ExchangeDAGServiceMonitoring\ExchangeDAGServiceMonitoringDataAccess.cs" />
    <Compile Include="ExchangeHealth\ExchangeHealthBuilder.cs" />
    <Compile Include="ExchangeHealth\ExchangeHealthDataAccess.cs" />
    <Compile Include="ExchangeSCRStatus\ExchangeSCRStatusBuilder.cs" />
    <Compile Include="ExchangeSCRStatus\ExchangeSCRStatusDataAccess.cs" />
    <Compile Include="ExchangeService\ExchangeServiceBuilder.cs" />
    <Compile Include="ExchangeService\ExchangeServiceDataAccess.cs" />
    <Compile Include="FastCopyJob\FastCopyJobBuilder.cs" />
    <Compile Include="FastCopyJob\FastCopyJobDataAccess.cs" />
    <Compile Include="FastCopyMonitor\FastCopyMonitorBuilder.cs" />
    <Compile Include="FastCopyMonitor\FastCopyMonitorDataAccess.cs" />
    <Compile Include="FastCopy\FastCopyBuilder.cs" />
    <Compile Include="FastCopy\FastCopyDataAccess.cs" />
    <Compile Include="GlobalMirrorLuns\GlobalMirrorLunsBuilder.cs" />
    <Compile Include="GlobalMirrorLuns\GlobalMirrorLunsDataAccess.cs" />
    <Compile Include="GlobalMirrorMonitor\GlobalMirrorMonitorBuilder.cs" />
    <Compile Include="GlobalMirrorMonitor\GlobalMirrorMonitorDataAccess.cs" />
    <Compile Include="GlobalMirror\GlobalMirrorBuilder.cs" />
    <Compile Include="GlobalMirror\GlobalMirrorDataAccess.cs" />
    <Compile Include="GroupDatabaseNodes\GroupDatabaseNodesBuilder.cs" />
    <Compile Include="GroupDatabaseNodes\GroupDatabaseNodesDataAccess.cs" />
    <Compile Include="ImpactMaster\ImpactMasterBuilder.cs" />
    <Compile Include="ImpactMaster\ImpactMasterDataAccess.cs" />
    <Compile Include="ImpactTypeMaster\ImpactTypeMasterBuilder.cs" />
    <Compile Include="ImpactTypeMaster\ImpactTypeMasterDataAccess.cs" />
    <Compile Include="InfraObjectJobs\InfraObjectJobBuilder.cs" />
    <Compile Include="InfraObjectJobs\InfraObjectJobDataAccess.cs" />
    <Compile Include="GroupLuns\GroupLunsBuilder.cs" />
    <Compile Include="GroupLuns\GroupLunsDataAccess.cs" />
    <Compile Include="GroupWorkflow\GroupWorkflowBuilder.cs" />
    <Compile Include="GroupWorkflow\GroupWorkflowDataAccess.cs" />
    <Compile Include="Group\GroupBuilder.cs" />
    <Compile Include="Group\GroupDataAccess.cs" />
    <Compile Include="HADRReplication\HADRReplicationBuilder.cs" />
    <Compile Include="HADRReplication\HADRReplicationDataAccess.cs" />
    <Compile Include="HADR\HADRBuilder.cs" />
    <Compile Include="HADR\HADRDataAccess.cs" />
    <Compile Include="Heatmap\HeatmapBuilder.cs" />
    <Compile Include="Heatmap\HeatmapDataAccess.cs" />
    <Compile Include="HitachiURDeviceMonitoringStatus\HitachiURDeviceMonitoringStatusBuilder.cs" />
    <Compile Include="HitachiURDeviceMonitoringStatus\HitachiURDeviceMonitoringStatusDataAccess.cs" />
    <Compile Include="HitachiUrLuns\HitachiUrLunsBuilder.cs" />
    <Compile Include="HitachiUrLuns\HitachiUrLunsDataAccess.cs" />
    <Compile Include="HitachiURMonitoringStatus\HitachiURMonitoringStatusBuilder.cs" />
    <Compile Include="HitachiURMonitoringStatus\HitachiURMonitoringStatusDataAccess.cs" />
    <Compile Include="HitachiUr\HitachiUrBuilder.cs" />
    <Compile Include="HitachiUr\HitachiUrDataAccess.cs" />
    <Compile Include="ImpactAnalysis\ImpactAnalysisBuilder.cs" />
    <Compile Include="ImpactAnalysis\ImpactAnalysisDataAccess.cs" />
    <Compile Include="IncidentManagment\IncidentManagmentBuilder.cs" />
    <Compile Include="IncidentManagment\IncidentManagmentDataAccess.cs" />
    <Compile Include="Incident\IncidentBuilder.cs" />
    <Compile Include="Incident\IncidentDataAccess.cs" />
    <Compile Include="InfraObjectsLuns\InfraObjectsLunsBuilder.cs" />
    <Compile Include="InfraObjectsLuns\InfraObjectsLunsDataAccess.cs" />
    <Compile Include="InfraObject\InfraObjectBuilder.cs" />
    <Compile Include="InfraObject\InfraObjectDataAccess.cs" />
    <Compile Include="Infrastructure\InfrastructureBuilder.cs" />
    <Compile Include="Infrastructure\InfrastructureDataAccess.cs" />
    <Compile Include="JobName\JobBuilder.cs" />
    <Compile Include="JobName\JobDataAccess.cs" />
    <Compile Include="JobTypeRepType\JobTypeRepTypeBuilder.cs" />
    <Compile Include="JobTypeRepType\JobTypeRepTypeDataAccess.cs" />
    <Compile Include="Licencekey\LicencekeyBuilder.cs" />
    <Compile Include="Licencekey\LicencekeyDataAccess.cs" />
    <Compile Include="LogVolume\LogVolumeBuilder.cs" />
    <Compile Include="LogVolume\LogVolumeDataAccess.cs" />
    <Compile Include="Maintenance\MaintenanceBuilder.cs" />
    <Compile Include="Maintenance\MaintenanceDataAccess.cs" />
    <Compile Include="MonitorServices\MonitorServicesBuilder.cs" />
    <Compile Include="MonitorServices\MonitorServicesDataAccess.cs" />
    <Compile Include="MountPoint\MountPointBuilder.cs" />
    <Compile Include="MountPoint\MountPointDataAccess.cs" />
    <Compile Include="MSSQL2014ServerMonitor\MSSQL2014ServerBuilder.cs" />
    <Compile Include="MSSQL2014ServerMonitor\MSSQLServer2014MonitorDataAccess.cs" />
    <Compile Include="MSSQLAlwaysOnRepli\MSSQLAlwaysOnDataAccess.cs" />
    <Compile Include="MSSQLAlwaysOnRepli\MSSQLAlwaysOnReplicationBuilder.cs" />
    <Compile Include="MSSQLDataBaseMirrorMonitor\DatabaseMirrorBuilder.cs" />
    <Compile Include="MSSQLDataBaseMirrorMonitor\DataBaseMirrorMonitorDataAccess.cs" />
    <Compile Include="MSSQLDBMirrorRepliMonitor\MSSQLDBMirrorRepliMoniBuilder.cs" />
    <Compile Include="MSSQLDBMirrorRepliMonitor\MSSQLDBMirrorReplMonitorDataAccess.cs" />
    <Compile Include="MSSQLDBMirrorsReplication\MsSqlDBMirrorReplicationBuilder.cs" />
    <Compile Include="MSSQLDBMirrorsReplication\MSSqlDBMirrorReplicationDataAccess.cs" />
    <Compile Include="MssqlDMXEmcSrdfMonitor\MssqlDMXEmcSrdfMonitorBuilder.cs" />
    <Compile Include="MssqlDMXEmcSrdfMonitor\MssqlDMXEmcSrdfMonitorDataAccess.cs" />
    <Compile Include="MSSQLDoubleTakeRepli\MSSqlDoubleTakeRepliBuilder.cs" />
    <Compile Include="MSSQLDoubleTakeRepli\MSSqlDoubleTakeRepliDataAccess.cs" />
    <Compile Include="MSSqlDoubletekRepliMonitor\MSSqlDoubletekRepliMonitorBuilder.cs" />
    <Compile Include="MSSqlDoubletekRepliMonitor\MSSqlDoubletekRepliMonitorDataAccess.cs" />
    <Compile Include="MssqlEmcSrdfMonitor\MssqlEmcSrdfMonitorBuilder.cs" />
    <Compile Include="MssqlEmcSrdfMonitor\MssqlEmcSrdfMonitorDataAccess.cs" />
    <Compile Include="MssqlVMAXEmcSrdfMonitor\MssqlVMAXEmcSrdfMonitorBuilder.cs" />
    <Compile Include="MssqlVMAXEmcSrdfMonitor\MssqlVMAXEmcSrdfMonitorDataAccess.cs" />
    <Compile Include="MySqlFullDBEmcsrdf\MysqlFullDBBuilder.cs" />
    <Compile Include="MySqlFullDBEmcsrdf\MysqlFullDBDataAccess.cs" />
    <Compile Include="MySqlGlobalMirrorMonitor\MySqlGlobalMirrorMonitorBuilder.cs" />
    <Compile Include="MySqlGlobalMirrorMonitor\MySqlGlobalMirrorMonitorDataAccess.cs" />
    <Compile Include="MySqlMonitor\MySqlMonitorBuilder.cs" />
    <Compile Include="MySqlMonitor\MySqlMonitorDataAccess.cs" />
    <Compile Include="MySqlNativeMonitor\MySqlNativeMonitorBuilder.cs" />
    <Compile Include="MySqlNativeMonitor\MySqlNativeMonitorDataAccess.cs" />
    <Compile Include="MySqlRepli\MySqlRepliBuilder.cs" />
    <Compile Include="MySqlRepli\MySqlRepliDataAccess.cs" />
    <Compile Include="NodeMaster\CPNodeMasterBuilder.cs" />
    <Compile Include="NodeMaster\CPNodeMasterDataAccess.cs" />
    <Compile Include="NodeSubstituteAuthenticate\NodeSubstituteAuthenticateBuilder.cs" />
    <Compile Include="NodeSubstituteAuthenticate\NodeSubstituteAuthenticateDataAccess.cs" />
    <Compile Include="Nodes\NodesBuilder.cs" />
    <Compile Include="Nodes\NodesDataAccess.cs" />
    <Compile Include="NutanixClusterDetail\NutanixClusterDetailBuilder.cs" />
    <Compile Include="NutanixClusterDetail\NutanixClusterDetailDataAccess.cs" />
    <Compile Include="NutanixLeapReplication\NutanixLeapReplicationBuilder.cs" />
    <Compile Include="NutanixLeapReplication\NutanixLeapReplicationDataAccess.cs" />
    <Compile Include="NutanixProtectionDomainReplication\NutanixProtectionDomainRepliBuilder.cs" />
    <Compile Include="NutanixProtectionDomainReplication\NutanixProtectionDomainRepliDataAccess.cs" />
    <Compile Include="NutanixProtectionDomain\NutanixProtectionDomainBuilder.cs" />
    <Compile Include="NutanixProtectionDomain\NutanixProtectionDomainDataAccess.cs" />
    <Compile Include="NutanixRepli\NutanixRepliBuilder.cs" />
    <Compile Include="NutanixRepli\NutanixRepliDataAccess.cs" />
    <Compile Include="NutLeapRcblEntReplMonitor\NutLeapRcblEntReplMonitorBuilder.cs" />
    <Compile Include="NutLeapRcblEntReplMonitor\NutLeapRcblEntReplMonitorDataAccess.cs" />
    <Compile Include="NutLeapRPReplMonitor\NutLeapRPReplMonitorBuilder.cs" />
    <Compile Include="NutLeapRPReplMonitor\NutLeapRPReplMonitorDataAccess.cs" />
    <Compile Include="OracleEmcSrdfDMXMonitor\OracleEmcSrdfDMXBuilder.cs" />
    <Compile Include="OracleEmcSrdfDMXMonitor\OracleEmcSrdfFullDMXDataAccess.cs" />
    <Compile Include="OracleEmcSrdfVMAXMonitor\OracleEmcSrdfFullVMAXDataAccess.cs" />
    <Compile Include="OracleEmcSrdfVMAXMonitor\OracleEmcSrdfVMAXBuilder.cs" />
    <Compile Include="ParallelProfile\ParallelProfileBuilder.cs" />
    <Compile Include="ParallelProfile\ParallelProfileDataAccess.cs" />
    <Compile Include="ParallelWorkflowProfile\ParallelWorkflowProfileBuilder.cs" />
    <Compile Include="ParallelWorkflowProfile\ParallelWorkflowProfileDataAccess.cs" />
    <Compile Include="Postgre9xComponentMonitor\Postgre9xComponentMonitorBuilder.cs" />
    <Compile Include="Postgre9xComponentMonitor\Postgre9xComponentMonitorDataAccess.cs" />
    <Compile Include="Postgre9xMonitorStatus\Postgre9xMonitorStatusBuilder.cs" />
    <Compile Include="Postgre9xMonitorStatus\Postgre9xMonitorStatusDataAccess.cs" />
    <Compile Include="PostgredbMonitor\PostgredbMonitoringBuilder.cs" />
    <Compile Include="PostgredbMonitor\PostgredbMonitoringDataAccess.cs" />
    <Compile Include="PostgredbReplication\PostgreReplicationBuilder.cs" />
    <Compile Include="PostgredbReplication\PostgreReplicationDataAccess.cs" />
    <Compile Include="PostgreSqlClusterMonitor\PGSQLClusterMonitorBuilder.cs" />
    <Compile Include="PostgreSqlClusterMonitor\PGSqlClusterMonitorDataAccess.cs" />
    <Compile Include="QueueMoniter\QueueMoniterBuilder.cs" />
    <Compile Include="QueueMoniter\QueueMoniterDataAccess.cs" />
    <Compile Include="RecoverPStateMonitor\RecoveryPStateMonitorBuilder.cs" />
    <Compile Include="RecoverPStateMonitor\RecoveryPStateMonitorDataAccess.cs" />
    <Compile Include="RecoverPStatisticMonitor\RecoverPStatisticMonitorBuilder.cs" />
    <Compile Include="RecoverPStatisticMonitor\RecoverPStatisticMonitorDataAccess.cs" />
    <Compile Include="RecoveryPointMulti\RecoveryPointMultiBuilder.cs" />
    <Compile Include="RecoveryPointMulti\RecoveryPointMultiDataAccess.cs" />
    <Compile Include="RecoveryPoint\RecoveryPointBuilder.cs" />
    <Compile Include="RecoveryPoint\RecoveryPointDataAccess.cs" />
    <Compile Include="RedisCLIMode\RedisCLIModeBuilder.cs" />
    <Compile Include="RedisCLIMode\RedisCLIModeDataAccess.cs" />
    <Compile Include="ReplicatedGrpMonitor\ReplicatedGroupMonitorBuilder.cs" />
    <Compile Include="ReplicatedGrpMonitor\ReplicatedGroupMonitorDataAccess.cs" />
    <Compile Include="ReplicationBase\ReplicationBaseBuilder.cs" />
    <Compile Include="ReplicationBase\ReplicationBaseDataAccess.cs" />
    <Compile Include="ReportSchedule\ReportScheduleBuilder.cs" />
    <Compile Include="ReportSchedule\ReportScheduleDataAccess.cs" />
    <Compile Include="RLinkMonitorRepliPerformm\RLinkMonitorRepliPerBuilder.cs" />
    <Compile Include="RLinkMonitorRepliPerformm\RLinkMonitorRepliPerDataAccess.cs" />
    <Compile Include="RLinkMonitorSecandry\RLinkMonitorSecUpdateDataAccess.cs" />
    <Compile Include="RLinkMonitorSecandry\RLinkSecondaryUpdateBuilder.cs" />
    <Compile Include="RoboCopyJob\RoboCopyJobBuilder.cs" />
    <Compile Include="RoboCopyJob\RoboCopyJobDataAccess.cs" />
    <Compile Include="RoboCopyLogs\RoboCopyLogsBuilder.cs" />
    <Compile Include="RoboCopyLogs\RoboCopyLogsDataAccess.cs" />
    <Compile Include="RoboCopyOptions\RoboCopyOptionsBuilder.cs" />
    <Compile Include="RoboCopyOptions\RoboCopyOptionsDataAccess.cs" />
    <Compile Include="RoboCopy\RoboCopyBuilder.cs" />
    <Compile Include="RoboCopy\RoboCopyDataAccess.cs" />
    <Compile Include="RSyncJobs\RSyncJobBuilder.cs" />
    <Compile Include="RSyncJobs\RSyncJobDataAccess.cs" />
    <Compile Include="RSyncMonitoring\RSyncMonitorBuilder.cs" />
    <Compile Include="RSyncMonitoring\RSyncMonitorDataAccess.cs" />
    <Compile Include="RSyncOption\RSyncOptionsBuilder.cs" />
    <Compile Include="RSyncOption\RSyncOptionsDataAccess.cs" />
    <Compile Include="RSync\RSyncBuilder.cs" />
    <Compile Include="RSync\RSyncDataAccess.cs" />
    <Compile Include="RTOMTRConfiguration\RTOMTRConfigurationBuilder.cs" />
    <Compile Include="RTOMTRConfiguration\RTOMTRConfigurationDataAccess.cs" />
    <Compile Include="ScheDiscProfileDetails\ScheduleDiscoveryProfDetailsBuilder.cs" />
    <Compile Include="ScheDiscProfileDetails\ScheduleDiscoveryProfDetailsDataAccess.cs" />
    <Compile Include="ScheduleWorkflow\ScheduleWorkflowBuilder.cs" />
    <Compile Include="ScheduleWorkflow\ScheduleWorkflowDataAccess.cs" />
    <Compile Include="SCR\SCRBuilder.cs" />
    <Compile Include="SCR\SCRDataAccess.cs" />
    <Compile Include="ServiceDiagramDetails\ServiceDiagramBuilder.cs" />
    <Compile Include="ServiceDiagramDetails\ServiceDiagramDataAccess.cs" />
    <Compile Include="ServiceDRProtection\ServiceDRProtectionBuilder.cs" />
    <Compile Include="ServiceDRProtection\ServiceDRProtectionDataAccess.cs" />
    <Compile Include="ServiceProfiles\ServiceProfileBuilder.cs" />
    <Compile Include="ServiceProfiles\ServiceProfileDataAccess.cs" />
    <Compile Include="ServiceRTODaily\ServiceRTODailyBuilder.cs" />
    <Compile Include="ServiceRTODaily\ServiceRTODailyDataAccess.cs" />
    <Compile Include="Setting\SettingBuilder.cs" />
    <Compile Include="Setting\SettingDataAccess.cs" />
    <Compile Include="Setting\VirtualMonitoring\VirtualMonitoringBuilder.cs" />
    <Compile Include="Setting\VirtualMonitoring\VirtualMonitoringDataAccess.cs" />
    <Compile Include="SingleSignOn\SingleSignOnBuilder.cs" />
    <Compile Include="SingleSignOn\SingleSignOnDataAccess.cs" />
    <Compile Include="SMSConfiguration\SMSConfigurationBuilder.cs" />
    <Compile Include="SMSConfiguration\SMSConfigurationDataAccess.cs" />
    <Compile Include="SMSReport\SMSReportBuilder.cs" />
    <Compile Include="SMSReport\SMSReportDataAccess.cs" />
    <Compile Include="Sql2000Health\Sql2000HealthBuilder.cs" />
    <Compile Include="Sql2000Health\Sql2000HealthDataAccess.cs" />
    <Compile Include="Sql2000Log\Sql2000LogBuilder.cs" />
    <Compile Include="Sql2000Log\Sql2000LogDataAccess.cs" />
    <Compile Include="Sql2000Service\Sql2000ServiceBuilder.cs" />
    <Compile Include="Sql2000Service\Sql2000ServiceDataAccess.cs" />
    <Compile Include="SQLNative2008Monitors\SQLNative2008MonitorBuilder.cs" />
    <Compile Include="SQLNative2008Monitors\SQLNative2008MonitorDataAccess.cs" />
    <Compile Include="SQLNative2008\SQLNative2008DataAccess.cs" />
    <Compile Include="SQLNative2008\SQLNative2008ReplicationBuilder.cs" />
    <Compile Include="SqlNativeHealthPara\SqlNativeHealthParaBuilder.cs" />
    <Compile Include="SqlNativeHealthPara\SqlNativeHealthParaDataAccess.cs" />
    <Compile Include="SqlNativeHealth\SqlNativeHealthBuilder.cs" />
    <Compile Include="SqlNativeHealth\SqlNativeHealthDataAccess.cs" />
    <Compile Include="SqlNative\SqlNativeBuilder.cs" />
    <Compile Include="SqlNative\SqlNativeDataAccess.cs" />
    <Compile Include="NetworkIP\NetworkIPBuilder.cs" />
    <Compile Include="NetworkIP\NetworkIPDataAccess.cs" />
    <Compile Include="DataGuardMonitor\DataGuardMonitorBuilder.cs" />
    <Compile Include="DataGuardMonitor\DataGuardMonitorDataAccess.cs" />
    <Compile Include="DataGuard\DataGuardBuilder.cs" />
    <Compile Include="DataGuard\DataGuardDataAccess.cs" />
    <Compile Include="ParallelDROperation\ParallelDROperationBuilder.cs" />
    <Compile Include="ParallelDROperation\ParallelDROperationDataAccess.cs" />
    <Compile Include="ParallelGroupWorkflow\ParallelGroupWorkflowBuilder.cs" />
    <Compile Include="ParallelGroupWorkflow\ParallelGroupWorkflowDataAccess.cs" />
    <Compile Include="ParallelServers\ParallelServerBuilder.cs" />
    <Compile Include="ParallelServers\ParallelServerDataAccess.cs" />
    <Compile Include="ParallelWorkflowActionResult\ParallelWorkflowActionResultBuilder.cs" />
    <Compile Include="ParallelWorkflowActionResult\ParallelWorkflowActionResultDataAccess.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="OracleLog\OracleLogBuilder.cs" />
    <Compile Include="OracleLog\OracleLogDataAccess.cs" />
    <Compile Include="Server\ServerBuilder.cs" />
    <Compile Include="Server\ServerDataAccess.cs" />
    <Compile Include="SiteInchargeInfo\SiteInchargeInfoBuilder.cs" />
    <Compile Include="SiteInchargeInfo\SiteInchargeInfoDataAccess.cs" />
    <Compile Include="Site\SiteBuilder.cs" />
    <Compile Include="Site\SiteDataAccess.cs" />
    <Compile Include="SmtpConfiguration\SmtpConfigurationBuilder.cs" />
    <Compile Include="SmtpConfiguration\SmtpConfigurationDataAccess.cs" />
    <Compile Include="SnapMirrorMonitor\SnapMirrorMonitorBuilder.cs" />
    <Compile Include="SnapMirrorMonitor\SnapMirrorMonitorDataAccess.cs" />
    <Compile Include="SnapMirror\SnapMirrorBuilder.cs" />
    <Compile Include="SnapMirror\SnapMirrorDataAccess.cs" />
    <Compile Include="SqlNativeMonitor\SqlNativeMonitorBuilder.cs" />
    <Compile Include="SqlNativeMonitor\SqlNativeMonitorDataAccess.cs" />
    <Compile Include="SqlNativeServices\SqlNativeServicesBuilder.cs" />
    <Compile Include="SqlNativeServices\SqlNativeServicesDataAccess.cs" />
    <Compile Include="SRMVMware\SRMVmwareMonitorBuilder.cs" />
    <Compile Include="SRMVMware\SRMVmwareMonitorDataAccess.cs" />
    <Compile Include="SubstituteAuthenticate\SubstituteAuthenticateBuilder.cs" />
    <Compile Include="SubstituteAuthenticate\SubstituteAuthenticateDataAccess.cs" />
    <Compile Include="SVCcontrollerMonitor\SVCcontrollerMonitorBuilder.cs" />
    <Compile Include="SVCcontrollerMonitor\SVCcontrollerMonitorDataAccess.cs" />
    <Compile Include="SVCGlobalMirrorMonitor\SVCGlobalMirrorMonitorBuilder.cs" />
    <Compile Include="SVCGlobalMirrorMonitor\SVCGlobalMirrorMonitorDataAccess.cs" />
    <Compile Include="SVCGlobalMirrorORMetroConfig\SVCGlobalMirrorORMetroConfigBuilder.cs" />
    <Compile Include="SVCGlobalMirrorORMetroConfig\SVCGlobalMirrorORMetroConfigDataAccess.cs" />
    <Compile Include="SVCGMReplication\SVCGMReplicationMBuilder.cs" />
    <Compile Include="SVCGMReplication\SVCGMReplicationMsDataAccess.cs" />
    <Compile Include="SVCNodeDetailedMonitor\SVCNodeDetailedBuilder.cs" />
    <Compile Include="SVCNodeDetailedMonitor\SVCNodeDetailedDataAccess.cs" />
    <Compile Include="SyBasesMonitor\SyBaseBuilder.cs" />
    <Compile Include="SyBasesMonitor\SyBaseMonitorDataAccess.cs" />
    <Compile Include="SybaseWithRSHADR\SybaseWithRSHADRDataAccess.cs" />
    <Compile Include="SybaseWithRSHADR\SysbaseWithRSHADRBuilder.cs" />
    <Compile Include="SybaseWithRSHADR_DBMonitoring\SybaseWithRSHadrDBBuilder.cs" />
    <Compile Include="SybaseWithRSHADR_DBMonitoring\SybaseWithRSHadrDBMonitorDataAccess.cs" />
    <Compile Include="SybaseWithRSHADR_Repli_MonitoringNew\SybaseWithRSHadrRepliBuilderNew.cs" />
    <Compile Include="SybaseWithRSHADR_Repli_MonitoringNew\SybaseWithRSHadrRepliMonitorDataAccessNew.cs" />
    <Compile Include="SybaseWithRSHADR_Repli_Monitoring\SybaseWithRSHadrRepliBuilder.cs" />
    <Compile Include="SybaseWithRSHADR_Repli_Monitoring\SybaseWithRSHadrRepliMonitorDataAccess.cs" />
    <Compile Include="SybaseWithSRSMonitoring\SybaseWithSRSBuilder.cs" />
    <Compile Include="SybaseWithSRSMonitoring\SybaseWithSRSMonitorDataAccess.cs" />
    <Compile Include="SybaseWithSRS\SybaseWithSRSDataAccess.cs" />
    <Compile Include="SybaseWithSRS\SysbaseWithSRSBuilder.cs" />
    <Compile Include="TagEntity\TagEntityDetailsBuilder.cs" />
    <Compile Include="TagEntity\TagEntityDetailsDataAccess.cs" />
    <Compile Include="TPRCMonitoring\TPRCMonitorDataAccess.cs" />
    <Compile Include="TPRCMonitoring\TPRCMonitoringBuilder.cs" />
    <Compile Include="TPRCRepli\TPRCReplicationBuilder.cs" />
    <Compile Include="TPRCRepli\TPRCReplicationDataAccess.cs" />
    <Compile Include="UserActivity\UserActivityBuilder.cs" />
    <Compile Include="UserActivity\UserActivityDataAccess.cs" />
    <Compile Include="UserGroup\UserGroupBuilder.cs" />
    <Compile Include="UserGroup\UserGroupDataAccess.cs" />
    <Compile Include="UserInfo\UserInfoBuilder.cs" />
    <Compile Include="UserInfo\UserInfoDataAccess.cs" />
    <Compile Include="UserInfraObject\UserInfraObjectBuilder.cs" />
    <Compile Include="UserInfraObject\UserInfraObjectDataAccess.cs" />
    <Compile Include="UserServicesProfile\UserServiceProfileBuilder.cs" />
    <Compile Include="UserServicesProfile\UserServiceProfileDataAccess.cs" />
    <Compile Include="User\UserBuilder.cs" />
    <Compile Include="User\UserDataAccess.cs" />
    <Compile Include="VCenterMonitorProfile\VCenterMonitorProfileBuilder.cs" />
    <Compile Include="VCenterMonitorProfile\VCenterMonitorProfileDataAccess.cs" />
    <Compile Include="VeeamMonitor\VeeamMonitorBuilder.cs" />
    <Compile Include="VeeamMonitor\VeeamMonitorDataAccess.cs" />
    <Compile Include="VeeamRepli\VeeamRepliBuilder.cs" />
    <Compile Include="VeeamRepli\VeeamRepliDataAccess.cs" />
    <Compile Include="VeeamVMJobs\VeeamVMJobBuilder.cs" />
    <Compile Include="VeeamVMJobs\VeeamVMJobDataAccess.cs" />
    <Compile Include="VeritasCluster\VeritasClusterBuilder.cs" />
    <Compile Include="VeritasCluster\VeritasClusterDataAccess.cs" />
    <Compile Include="Veritas_Cluster_Monitoring\VeritasClusterMonitorDataAccess.cs" />
    <Compile Include="VmwareMonitor\VmwareMonitorBuilder.cs" />
    <Compile Include="VmwareMonitor\VmwareMonitorDataAccess.cs" />
    <Compile Include="VMWareNetsnapMirrorMonitor\VMWareNetSnapMirrorMonitorBuilder.cs" />
    <Compile Include="VMWareNetsnapMirrorMonitor\VMWareNetsnapMirrorMonitorDataAccess.cs" />
    <Compile Include="VmWarepathDetail\VmWarePathDetailBuilder.cs" />
    <Compile Include="VmWarepathDetail\VmWarePathDetailDataAccess.cs" />
    <Compile Include="VmwareVsphereMonitoring\VmwareVsphereMonitorBuilder.cs" />
    <Compile Include="VmwareVsphereMonitoring\VmwareVsphereMonitorDataAccess.cs" />
    <Compile Include="VmwareVsphereReplication\VmwareVsphereRepliBuilder.cs" />
    <Compile Include="VmwareVsphereReplication\VmwareVsphereRepliDataAccess.cs" />
    <Compile Include="VVRReplication\VVRReplicationBuilder.cs" />
    <Compile Include="VVRReplication\VVRReplicationDataAccess.cs" />
    <Compile Include="WorkflowAction\WorkflowActionBuilder.cs" />
    <Compile Include="WorkflowAction\WorkflowActionDataAccess.cs" />
    <Compile Include="Workflow\WorkflowBuilder.cs" />
    <Compile Include="Workflow\WorkflowDataAccess.cs" />
    <Compile Include="XIVMirrorStatistics\XIVMirrorStatisticsBuilder.cs" />
    <Compile Include="XIVMirrorStatistics\XIVMirrorStatisticsDataAccess.cs" />
    <Compile Include="XIVMirror\XIVMirrorBuilder.cs" />
    <Compile Include="XIVMirror\XIVMirrorDataAccess.cs" />
    <Compile Include="ZertoMonitor\ZertoBuilder.cs" />
    <Compile Include="ZertoMonitor\ZertoMonitorDataaccess.cs" />
    <Compile Include="ZertoSiteRecovery\ZertoSiteBuilder.cs" />
    <Compile Include="ZertoSiteRecovery\ZertoSiteDataAccess.cs" />
    <Compile Include="ZFSReplicationMonitorLogs\ZFSReplicationMonitorLogsBuilder.cs" />
    <Compile Include="ZFSReplicationMonitorLogs\ZFSReplicationMonitorLogsDataAccess.cs" />
    <Compile Include="ZFSStorageReplication\ZFSStorageReplicationBuilder.cs" />
    <Compile Include="ZFSStorageReplication\ZFSStorageReplicationDataAccess.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="ApplicationDependency\BCMS.Common.pfx" />
    <None Include="DataBaseMySql\.svn\all-wcprops" />
    <None Include="DataBaseMySql\.svn\entries" />
    <None Include="DataBaseMySql\.svn\text-base\DatabaseMySqlBuilder.cs.svn-base" />
    <None Include="DataBaseMySql\.svn\text-base\DatabaseMySqlDataAccess.cs.cs.svn-base" />
    <None Include="DB2DataSyncMonitor\.svn\all-wcprops" />
    <None Include="DB2DataSyncMonitor\.svn\entries" />
    <None Include="DB2DataSyncMonitor\.svn\text-base\DB2DataSyncMonitorBuilder.cs.svn-base" />
    <None Include="DB2DataSyncMonitor\.svn\text-base\DB2DataSyncMonitorDataAccess.cs.svn-base" />
    <None Include="MySqlGlobalMirrorMonitor\.svn\all-wcprops" />
    <None Include="MySqlGlobalMirrorMonitor\.svn\entries" />
    <None Include="MySqlGlobalMirrorMonitor\.svn\text-base\MySqlGlobalMirrorMonitorBuilder.cs.svn-base" />
    <None Include="MySqlGlobalMirrorMonitor\.svn\text-base\MySqlGlobalMirrorMonitorDataAccess.cs.svn-base" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="bin\Release\BCMS.AlertController.dll" />
    <Content Include="bin\Release\BCMS.AlertController.pdb" />
    <Content Include="bin\Release\Microsoft.Practices.ServiceLocation.dll" />
    <Content Include="bin\Release\Microsoft.Practices.Unity.dll" />
    <Content Include="bin\Release\Microsoft.Practices.Unity.Interception.dll" />
    <EmbeddedResource Include="cp.dataaccess.dll.licenses" />
    <Content Include="release.licenses" />
    <EmbeddedResource Include="licenses.licx" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="DataBaseMySql\.svn\prop-base\" />
    <Folder Include="DataBaseMySql\.svn\props\" />
    <Folder Include="DataBaseMySql\.svn\tmp\prop-base\" />
    <Folder Include="DataBaseMySql\.svn\tmp\props\" />
    <Folder Include="DataBaseMySql\.svn\tmp\text-base\" />
    <Folder Include="DB2DataSyncMonitor\.svn\prop-base\" />
    <Folder Include="DB2DataSyncMonitor\.svn\props\" />
    <Folder Include="DB2DataSyncMonitor\.svn\tmp\prop-base\" />
    <Folder Include="DB2DataSyncMonitor\.svn\tmp\props\" />
    <Folder Include="DB2DataSyncMonitor\.svn\tmp\text-base\" />
    <Folder Include="MSSqlDoubletekThirdParty\" />
    <Folder Include="MySqlGlobalMirrorMonitor\.svn\prop-base\" />
    <Folder Include="MySqlGlobalMirrorMonitor\.svn\props\" />
    <Folder Include="MySqlGlobalMirrorMonitor\.svn\tmp\prop-base\" />
    <Folder Include="MySqlGlobalMirrorMonitor\.svn\tmp\props\" />
    <Folder Include="MySqlGlobalMirrorMonitor\.svn\tmp\text-base\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CP.CacheController\CP.CacheController.csproj">
      <Project>{f9782f67-e167-4436-bad5-0dfbeef5c199}</Project>
      <Name>CP.CacheController</Name>
    </ProjectReference>
    <ProjectReference Include="..\CP.Common\CP.Common.csproj">
      <Project>{58ce906f-92e6-4f08-bab4-480dc99c36cc}</Project>
      <Name>CP.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CP.ExceptionHandler\CP.ExceptionHandler.csproj">
      <Project>{1005e955-9cc2-413c-a817-b53c97525766}</Project>
      <Name>CP.ExceptionHandler</Name>
    </ProjectReference>
    <ProjectReference Include="..\CP.Helper\CP.Helper.csproj">
      <Project>{ced08c2f-93fd-4ba6-8d2c-e79e45fe6a4f}</Project>
      <Name>CP.Helper</Name>
    </ProjectReference>
    <ProjectReference Include="..\CP.IDataAccess\CP.IDataAccess.csproj">
      <Project>{4e5a6dcb-f48d-476b-b596-1f1094fe4a4e}</Project>
      <Name>CP.IDataAccess</Name>
    </ProjectReference>
    <ProjectReference Include="..\CP.IFacade\CP.IFacade.csproj">
      <Project>{e794a867-970d-4ec7-b9fe-ff6ac56ff7bf}</Project>
      <Name>CP.IFacade</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>