var IsUserNameEncrypted = false;
var IsPasswordEncrypted = false;
$(document).ready(function () {

    binddropdown();
    new MaskedPassword(document.getElementById("ctl00_cphBody_Password"), '\u25CF');
});

function binddropdown() {
    $.ajax({
        type: "POST",
        url: "Login.aspx/DiscoverDomains",
        data: "{}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function Success(data) {
            getBindValue(data.d);
        }
    });
}
// Function to get static GUID from server session
function getStaticGuidFromServer(callback) {
    $.ajax({
        type: "POST",
        url: "Login.aspx/GetStaticGuid",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function(response) {
            callback(response.d);
        },
        error: function() {
            console.error("Failed to get static GUID from server");
            callback("");
        }
    });
}

// Function to set encrypted password in server session
function setEncryptedPasswordInSession(encryptedPassword, callback) {
    $.ajax({
        type: "POST",
        url: "Login.aspx/SetEncryptedPassword",
        data: JSON.stringify({ encryptedPassword: encryptedPassword }),
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function(response) {
            callback(response.d);
        },
        error: function() {
            console.error("Failed to set encrypted password in server session");
            callback(false);
        }
    });
}

// Function to get encrypted password from server session
function getEncryptedPasswordFromSession(callback) {
    $.ajax({
        type: "POST",
        url: "Login.aspx/GetEncryptedPassword",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function(response) {
            callback(response.d);
        },
        error: function() {
            console.error("Failed to get encrypted password from server session");
            callback("");
        }
    });
}

function cleartext() {

    var input = document.createElement('input');
    input.value = $('[id$=UserEncrypt]').val();
    if (input.value != "" && input.value != null && $('[id$=ctl00_cphBody_UserName]').val() != "") {
        getStaticGuidFromServer(function(guid) {
            $('[id$=ctl00_cphBody_UserName]').val(getOrignalData(input, guid));
        });
    }
    $('[id$=UserEncrypt]').val("");
    $('[id$=txtcaptcha]').val("");
    $('[id$=ctl00_cphBody_Password]').val("");
    $('[id$=passHidden]').val("");

    // Clear encrypted password from session
    setEncryptedPasswordInSession("", function(success) {
        if (!success) {
            console.warn("Failed to clear encrypted password from session");
        }
    });
}


//function getBindValue(value) {

//    var data = value.split(":");
//    for (var i = 0; i < data.length; i++) {

//        var volume = data[i].split(",");
//        var text = volume[0];
//        //   var html = '<tr><td><input type="text" id="' + text + '" </td></tr>';

//        // jQuery('[id$=combobox1]').append("<option value='" + html + "</option>");
//        jQuery(function () {
//            jQuery('[id$=combobox1]').combobox([
//                    text
//            ]);
//            $('[id$=combobox1]').val(volume[0]);

//        });

//        //   $("[id$=ctl00_cphBody_combobox1] option:contains(" + text + ")").attr('selected', true);
//        //  $("[id$=combobox1]).combobox(text[0]);

//    }




//}

function getBindValue(value) {
    var domain = [];
    var data = value.split(",");
    for (var i = 0; i < data.length; i++) {

        var volume = data[i].split(",");
        var text = volume[0];
        domain.push(text)
        //   var html = '<tr><td><input type="text" id="' + text + '" </td></tr>';

        // jQuery('[id$=combobox1]').append("<option value='" + html + "</option>");
        //jQuery(function () {
        //    jQuery('[id$=combobox1]').combobox([
        //            text
        //    ]);
        //    $('[id$=combobox1]').val(volume[0]);

        //});
        jQuery(function () {
            jQuery('[id$=combobox1]').combobox(
               domain);
        });

        //   $("[id$=ctl00_cphBody_combobox1] option:contains(" + text + ")").attr('selected', true);
        //  $("[id$=combobox1]).combobox(text[0]);

    }




}



var base64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef" +
   "ghijklmnopqrstuvwxyz0123456789+/=";

function encode64(input) {
    var output = "";
    var ch1, ch2, ch3, enc1, enc2, enc3, enc4;
    var i = 0;

    do {
        ch1 = input.charCodeAt(i++);
        ch2 = input.charCodeAt(i++);
        ch3 = input.charCodeAt(i++);

        enc1 = ch1 >> 2;
        enc2 = ((ch1 & 3) << 4) | (ch2 >> 4);
        enc3 = ((ch2 & 15) << 2) | (ch3 >> 6);
        enc4 = ch3 & 63;

        if (isNaN(ch2)) {
            enc3 = enc4 = 64;
        } else if (isNaN(ch3)) {
            enc4 = 64;
        }

        output = output + base64.charAt(enc1) + base64.charAt(enc2) +
          base64.charAt(enc3) + base64.charAt(enc4);
        ch1 = ch2 = ch3 = "";
        enc1 = enc2 = enc3 = enc4 = "";
    } while (i < input.length);

    return output;
}



function generateCode() {
    var passCode = $('[id$=passHidden]').val();
    var UserCode = $('[id$=ctl00_cphBody_UserName]').val();
    var passencode = "";
    var Userencode = "";
    if (passCode.trim().length > 0 && IsPasswordEncrypted == false) {
        passencode = encode64(passCode);
        $('[id$=ctl00_cphBody_Password]').val("\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF");
        $('[id$=passHidden]').val(passencode);

        // Store encrypted password in session instead of hidden field
        setEncryptedPasswordInSession(passencode, function(success) {
            if (!success) {
                console.warn("Failed to store encrypted password in session");
            }
        });

        IsPasswordEncrypted = true;
    }
    if (UserCode.trim().length > 0 && IsUserNameEncrypted == false) {
        //Userencode = encode64(UserCode);
        Userencode = UserCode;
        $('[id$=UserEncrypt]').val(Userencode);
        $('[id$=ctl00_cphBody_UserName]').val(Userencode);
        IsUserNameEncrypted = true
    }
}

function clearControlData(control) {
    $('[id$=ctl00_cphBody_Password]').val("");
    $('[id$=passHidden]').val("");

    // Clear encrypted password from session
    setEncryptedPasswordInSession("", function(success) {
        if (!success) {
            console.warn("Failed to clear encrypted password from session");
        }
    });
}

function getUserNameHash(control, callback) {
    getStaticGuidFromServer(function(guid) {
        $('[id$=ctl00_cphBody_UserEncrypt]').val(genrateUserNameHash(control, guid));

        // Call callback if provided
        if (callback && typeof callback === 'function') {
            callback();
        }
    });
}

// Server-side equivalent Base64 encoding with character swapping
function serverSideBase64Encode(plainText) {
    try {
        // Convert to UTF-8 bytes then base64 (matches C# UTF8.GetBytes + Convert.ToBase64String)
        var base64;

        // Modern approach for UTF-8 encoding
        if (typeof TextEncoder !== 'undefined') {
            var utf8Bytes = new TextEncoder().encode(plainText);
            base64 = btoa(String.fromCharCode.apply(null, utf8Bytes));
        } else {
            // Fallback for older browsers
            base64 = btoa(unescape(encodeURIComponent(plainText)));
        }

        // Character swapping logic (matches server-side)
        var actual = "";
        for (var i = 0; i < base64.length; i += 2) {
            if (i + 1 < base64.length) {
                actual += base64[i + 1] + base64[i];
            } else {
                actual += base64[i];
            }
        }
        return actual;
    } catch (ex) {
        console.error("Error in serverSideBase64Encode:", ex);
        return "";
    }
}

// Server-side equivalent hash key generation (matches Utility.getHashKeyByString exactly)
function serverSideGetHashKeyByString(strPass, strGuid) {
    try {
        if (!strPass || strPass === "") {
            return "";
        }

        // Use server-side equivalent base64 encoding
        var encodedPass = serverSideBase64Encode(strPass);
        var strKeyArr = strGuid.split('-');
        var j = 0;
        var strResult = "";

        for (var i = 0; i < encodedPass.length; i++) {
            if (i % 5 === 0) {
                j = 0;
            }
            strKeyArr[j] += encodedPass[i];
            j++;
        }

        for (var k = 0; k < strKeyArr.length; k++) {
            strResult += strKeyArr[k] + "-";
        }

        // Remove the last dash
        if (strResult.length > 0) {
            strResult = strResult.substring(0, strResult.length - 1);
        }

        return strResult;
    } catch (ex) {
        console.error("Error in serverSideGetHashKeyByString:", ex);
        return "";
    }
}

function getPasswordHash(control, callback) {
    console.log('getPasswordHash called with control value:', control.value);

    // Get the actual password value directly
    var passwordValue = control.value;

    getStaticGuidFromServer(function(guid) {
        console.log('Got GUID from server:', guid);

        // Use the correct password hashing function that matches server-side
        var strData = serverSideGetHashKeyByString(passwordValue, guid);
        console.log('Generated password hash:', strData);

        // Send encrypted password to session instead of hidden field
        setEncryptedPasswordInSession(strData, function(success) {
            if (success) {
                console.log('Encrypted password stored in session successfully');

                // Update the password field with the hash for display
                $('[id$=ctl00_cphBody_Password]').val(strData);

                // Also update passHidden if it exists
                var passHiddenElement = document.getElementById('passHidden');
                if (passHiddenElement) {
                    passHiddenElement.value = strData;
                }

                console.log('Password hashing completed');

                // Call callback if provided (for form submission)
                if (callback && typeof callback === 'function') {
                    callback();
                }
            } else {
                console.error('Failed to store encrypted password in session');
                // Fallback: still proceed with callback
                if (callback && typeof callback === 'function') {
                    callback();
                }
            }
        });
    });
}

// Test function to verify password hashing works correctly
function testPasswordHashMatching(testPassword) {
    console.log('=== TESTING PASSWORD HASH MATCHING ===');
    console.log('Test password:', testPassword || 'test123');

    var password = testPassword || 'test123';

    getStaticGuidFromServer(function(guid) {
        console.log('Using GUID:', guid);

        // Generate hash using our JavaScript function
        var clientHash = serverSideGetHashKeyByString(password, guid);
        console.log('Client-generated hash:', clientHash);

        // Test against server-side generation (if available)
        if (typeof GeneratePasswordHashForTesting !== 'undefined') {
            $.ajax({
                type: "POST",
                url: "Login.aspx/GeneratePasswordHashForTesting",
                data: JSON.stringify({ password: password, guid: guid }),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(response) {
                    console.log('Server-generated hash:', response.d);
                    console.log('Hashes match:', clientHash === response.d);
                    if (clientHash === response.d) {
                        console.log('✅ PASSWORD HASHING IS WORKING CORRECTLY!');
                    } else {
                        console.log('❌ PASSWORD HASHING MISMATCH DETECTED!');
                    }
                },
                error: function() {
                    console.log('Server test method not available, but client hash generated successfully');
                }
            });
        } else {
            console.log('Client hash generated successfully. Server test method not available.');
        }

        console.log('=== TEST COMPLETE ===');
    });
}

function checkKey(event) {

    var isFirefox = typeof InstallTrigger !== 'undefined';
    if (isFirefox == true) {
        var x = event.which || event.keyCode;
        if (x != null && x !== undefined) {
            if (x == 13 && document.activeElement.id == "ctl00_cphBody_Password") {
                getPasswordHash($('[id$=ctl00_cphBody_Password]'));
            }
        }
    }
    else {
        if (window.event.keyCode == 13 && document.activeElement.id == "ctl00_cphBody_Password") {
            getPasswordHash($('[id$=ctl00_cphBody_Password]'));
        }
    }
}

// Function to ensure password is hashed before form submission
function ensurePasswordHashedBeforeSubmit(callback) {
    var passwordField = $('[id$=ctl00_cphBody_Password]');

    console.log('Password field value:', passwordField.val());

    // Check session for encrypted password
    getEncryptedPasswordFromSession(function(sessionPassword) {
        console.log('Session encrypted password:', sessionPassword);

        // Check if password field has value but session password is empty
        if (passwordField.val() && !sessionPassword) {
            console.log('Hashing password before submission...');
            // Hash the password and then proceed with submission
            getPasswordHash(passwordField[0], callback);
        } else {
            console.log('Password already hashed or no password entered, proceeding...');
            // Password already hashed or no password entered, proceed immediately
            callback();
        }
    });
}

// Override the default form submission to ensure password is hashed
function validateAndSubmitLogin() {
    // First run ASP.NET validation
    if (typeof Page_ClientValidate === 'function') {
        if (!Page_ClientValidate('LoginUser')) {
            return false; // Validation failed
        }
    }

    // First ensure username is hashed
    var userNameField = $('[id$=ctl00_cphBody_UserName]');
    var userEncryptField = $('[id$=ctl00_cphBody_UserEncrypt]');

    function proceedWithPasswordHashing() {
        // Then ensure password is hashed before submitting
        ensurePasswordHashedBeforeSubmit(function() {
            // Trigger the actual form submission
            __doPostBack('ctl00$cphBody$LoginButton', '');
        });
    }

    if (userNameField.val() && !userEncryptField.val()) {
        getUserNameHash(userNameField[0], proceedWithPasswordHashing);
    } else {
        proceedWithPasswordHashing();
    }

    return false; // Prevent default submission
}


