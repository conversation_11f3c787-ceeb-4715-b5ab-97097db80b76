﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
#if ORACLE 
using Oracle.ManagedDataAccess.Client;
using Oracle.ManagedDataAccess.Types;
#endif
using System.Configuration;

namespace CP.DataAccess
{
    internal sealed class WorkflowDataAccess : BaseDataAccess, IWorkflowDataAccess
    {
        #region Constructors

        public WorkflowDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<Workflow> CreateEntityBuilder<Workflow>()
        {
            return (new WorkflowBuilder()) as IEntityBuilder<Workflow>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="Workflow" /> Workflow table.
        /// </summary>
        /// <param name="workflow">Workflow</param>
        /// <returns>Workflow</returns>
        /// <author><PERSON></author>
        /// 

        public static Oracle.ManagedDataAccess.Client.OracleConnection getcon(string getconnectionstring)
        {
            string stringuser = "";
            string strinpassword = "";
            string stringdirect = "";
            string stringhost = "";
            string stringsid = "";
            string stringpost = "";

            string[] splitted = getconnectionstring.Split(';');
            string[] splitted1 = splitted[0].Split('=');
            string[] splitted2 = splitted[1].Split('=');
            string[] splitted3 = splitted[2].Split('=');
            string[] splitted4 = splitted[3].Split('=');
            string[] splitted5 = splitted[4].Split('=');
            string[] splitted6 = splitted[5].Split('=');
            if (splitted1 != null && splitted2 != null && splitted3 != null && splitted4 != null && splitted5 != null && splitted6 != null)
            {
                stringuser = splitted1[1].ToString();
                strinpassword = splitted2[1].ToString();
                stringdirect = splitted3[1].ToString();
                stringhost = splitted4[1].ToString();
                stringsid = splitted5[1].ToString();
                stringpost = splitted6[1].ToString();
            }

            #region test code
            //foreach (string s in splitted)
            //{
            //    string temp = s;
            //    if (temp != string.Empty)
            //    {

            //    }
            //    // int index = s.IndexOf("=");
            //    //    String Result = s.Substring(index + 1);
            //    // int i = s.IndexOf('\\');
            //    // if (i >= 0) str = str.SubString(i + 1);
            //    //string split = s.Substring(s.IndexOf('=') + 1);
            //    //split.Add(output);
            //    //string[] splittedd = s.Split('=');
            //    //string output = s.Split('.').Last();
            //    //int index = s.IndexOf('=');
            //    //string key = s.Substring(0, index);
            //    //string value = s.Substring(index + 1, s.Length - index);
            //}


            // string connextionstring = string.Format("Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=pin)(PORT=1521)))(CONNECT_DATA=(SERVER=DEDICATED)(SID=CPOLMYBL)));User Id=cp1;Password=********");
            //string connextionstring = string.Format("Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST="+stringhost)(PORT=1521)))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=CP)));User Id=cp1;Password=********)");

            #endregion test code
            string connextionstring = string.Format("Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=" + stringhost + ")(PORT=" + stringpost + ")))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=" + stringsid + ")));User Id=" + stringuser + ";Password=" + strinpassword);
            //  Logger.Info("connextionstring " + connextionstring);
            Oracle.ManagedDataAccess.Client.OracleConnection con = new Oracle.ManagedDataAccess.Client.OracleConnection(connextionstring);
            try
            {
                con.Open();
                //  Logger.Info("Database connected successfully ");
                //con.Dispose();             
            }

            catch (Oracle.ManagedDataAccess.Client.OracleException ox)
            {
                string error = ox.ToString();
                con.Dispose();


            }
            catch (Exception ex)
            {
                string error1 = ex.ToString();
                con.Dispose();
            }
            return con;
        }

        #region OldCOde
//        Workflow IWorkflowDataAccess.Add(Workflow workflow)
//        {
//            try
//            {
//                const string sp = "Workflow_Create";

//                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
//                {
//                    AddOutputParameter(cmd);
//                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, workflow.Name);
//                    Database.AddInParameter(cmd, Dbstring + "iXml", DbType.AnsiString, workflow.Xml);
//                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, workflow.CreatorId);
//                    Database.AddInParameter(cmd, Dbstring + "iActionIds", DbType.AnsiString, workflow.ActionIds);
//#if ORACLE
//                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
//#endif
//                    using (IDataReader reader = Database.ExecuteReader(cmd))
//                    {
//                        workflow = reader.Read() ? CreateEntityBuilder<Workflow>().BuildEntity(reader, workflow) : null;
//                    }

//                    if (workflow == null)
//                    {
//                        int returnCode = GetReturnCodeFromParameter(cmd);

//                        switch (returnCode)
//                        {
//                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
//                                {
//                                    throw new ArgumentException("Workflow already exists. Please specify another workflow.");
//                                }
//                            default:
//                                {
//                                    throw new SystemException(
//                                        "An unexpected error has occurred while creating this workflow.");
//                                }
//                        }
//                    }

//                    return workflow;
//                }
//            }
//            catch (Exception ex)
//            {
//                throw new CpException(CpExceptionType.DataAccessInsertOperation,
//                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
//                    "Error In DAL While inserting Workflow Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
//                    ex.Message, ex);
//            }
//        }
        #endregion

        Workflow IWorkflowDataAccess.Add(Workflow workflow)
        {
            try
            {
                #if ORACLE 
                string encrypt = ConfigurationManager.ConnectionStrings["CPConnectionString"].ConnectionString;
                string decryptconnectionstring = CryptographyHelper.Md5Decrypt(encrypt);

                const string sp1 = "Workflow_Create";

                Oracle.ManagedDataAccess.Client.OracleCommand cmd1 = new Oracle.ManagedDataAccess.Client.OracleCommand("Workflow_Create", getcon(decryptconnectionstring));

                cmd1.CommandType = CommandType.StoredProcedure;

                cmd1.Parameters.Add("ReturnCode", Oracle.ManagedDataAccess.Client.OracleDbType.Int32).Direction = ParameterDirection.Output;
                cmd1.Parameters.Add("iName", workflow.Name);
                cmd1.Parameters.Add("iXml", workflow.Xml);
                cmd1.Parameters.Add("iCreatorId", workflow.CreatorId);
                cmd1.Parameters.Add("iActionIds", workflow.ActionIds);
                cmd1.Parameters.Add("cur", Oracle.ManagedDataAccess.Client.OracleDbType.RefCursor).Direction = ParameterDirection.Output;

                using (Oracle.ManagedDataAccess.Client.OracleDataReader reader = cmd1.ExecuteReader())
                {

                    while (reader.Read())
                    {
                        var _workflow = new Workflow();

                        _workflow.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                        _workflow.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                        _workflow.Xml = Convert.IsDBNull(reader["Xml"]) ? string.Empty : Convert.ToString(reader["Xml"]);
                        _workflow.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                        _workflow.ActionIds = Convert.IsDBNull(reader["ActionIds"]) ? string.Empty : Convert.ToString(reader["ActionIds"]);

                        return _workflow;
                    }

                }
                return workflow;
#endif
                #region Old code Using Devart
                const string sp = "Workflow_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, workflow.Name);
                    Database.AddInParameter(cmd, Dbstring + "iXml", DbType.AnsiString, workflow.Xml);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, workflow.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iActionIds", DbType.AnsiString, workflow.ActionIds);
#if ORACLE
                                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        workflow = reader.Read() ? CreateEntityBuilder<Workflow>().BuildEntity(reader, workflow) : null;
                    }

                    if (workflow == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Workflow already exists. Please specify another workflow.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this workflow.");
                                }
                        }
                    }

                    return workflow;
                }
                #endregion
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting Workflow Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Update <see cref="Workflow" />into Workflow table.
        /// </summary>
        /// <param name="workflow">Workflow</param>
        /// <returns>Workflow</returns>
        /// <author>Kiran Ghadge</author>
        /// 
        #region Old Code
//        Workflow IWorkflowDataAccess.Update(Workflow workflow)
//        {
//            try
//            {
//                const string sp = "Workflow_Update";

//                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
//                {
//                    AddOutputParameter(cmd);
//                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, workflow.Id);
//                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, workflow.Name);
//                    Database.AddInParameter(cmd, Dbstring + "iXml", DbType.AnsiString, workflow.Xml);
//                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, workflow.UpdatorId);
//                    Database.AddInParameter(cmd, Dbstring + "iActionIds", DbType.AnsiString, workflow.ActionIds);
//                    Database.AddInParameter(cmd, Dbstring + "iVersion", DbType.Double, workflow.Version);
//#if ORACLE
//                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
//#endif
//                    using (IDataReader reader = Database.ExecuteReader(cmd))
//                    {
//                        workflow = reader.Read() ? CreateEntityBuilder<Workflow>().BuildEntity(reader, workflow) : null;
//                    }

//                    if (workflow == null)
//                    {
//                        int returnCode = GetReturnCodeFromParameter(cmd);

//                        switch (returnCode)
//                        {
//                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
//                                {
//                                    throw new ArgumentException("Workflow already exists. Please specify another workflow.");
//                                }
//                            default:
//                                {
//                                    throw new SystemException(
//                                        "An unexpected error has occurred while updating this workflow.");
//                                }
//                        }
//                    }

//                    return workflow;
//                }
//            }
//            catch (Exception ex)
//            {
//                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
//                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
//                    "Error In DAL While Updating Workflow Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
//                    ex.Message, ex);
//            }
//        }

        #endregion

        Workflow IWorkflowDataAccess.Update(Workflow workflow)
        {
            try
            {
                #if ORACLE 
                string encrypt = ConfigurationManager.ConnectionStrings["CPConnectionString"].ConnectionString;
                string decryptconnectionstring = CryptographyHelper.Md5Decrypt(encrypt);

                const string sp2 = "Workflow_Update";

                Oracle.ManagedDataAccess.Client.OracleCommand cmd1 = new Oracle.ManagedDataAccess.Client.OracleCommand("Workflow_Update", getcon(decryptconnectionstring));

                cmd1.CommandType = CommandType.StoredProcedure;

                cmd1.Parameters.Add("ReturnCode", Oracle.ManagedDataAccess.Client.OracleDbType.Int32).Direction = ParameterDirection.Output;
                cmd1.Parameters.Add("iId", workflow.Id);
                cmd1.Parameters.Add("iName", workflow.Name);
                cmd1.Parameters.Add("iXml", workflow.Xml);
                cmd1.Parameters.Add("iUpdatorId", workflow.UpdatorId);
                cmd1.Parameters.Add("iActionIds", workflow.ActionIds);
                cmd1.Parameters.Add("iVersion", workflow.Version);
                cmd1.Parameters.Add("cur", Oracle.ManagedDataAccess.Client.OracleDbType.RefCursor).Direction = ParameterDirection.Output;
                using (Oracle.ManagedDataAccess.Client.OracleDataReader reader = cmd1.ExecuteReader())
                {

                    while (reader.Read())
                    {
                        var _workflow = new Workflow();

                        _workflow.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                        _workflow.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                        _workflow.Xml = Convert.IsDBNull(reader["Xml"]) ? string.Empty : Convert.ToString(reader["Xml"]);
                        _workflow.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
                        _workflow.ActionIds = Convert.IsDBNull(reader["ActionIds"]) ? string.Empty : Convert.ToString(reader["ActionIds"]);
                        _workflow.Version = Convert.IsDBNull(reader["Version"]) ? 1.0 : Convert.ToDouble(reader["Version"]);
                        return _workflow;
                    }

                }
                return workflow;
#endif
                #region old code
                const string sp = "Workflow_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, workflow.Id);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, workflow.Name);
                    Database.AddInParameter(cmd, Dbstring + "iXml", DbType.AnsiString, workflow.Xml);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, workflow.UpdatorId);
                    Database.AddInParameter(cmd, Dbstring + "iActionIds", DbType.AnsiString, workflow.ActionIds);
                    Database.AddInParameter(cmd, Dbstring + "iVersion", DbType.Double, workflow.Version);
#if ORACLE
                                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        workflow = reader.Read() ? CreateEntityBuilder<Workflow>().BuildEntity(reader, workflow) : null;
                    }

                    if (workflow == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Workflow already exists. Please specify another workflow.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this workflow.");
                                }
                        }
                    }

                    return workflow;
                }
                #endregion old code
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Workflow Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }
        /// <summary>
        ///     Get <see cref="Workflow" />from Workflow table by Id.
        /// </summary>
        /// <param name="id">Id of the Workflow</param>
        /// <returns>Workflow</returns>
        /// <author>Kiran Ghadge</author>
        Workflow IWorkflowDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "Workflow_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<Workflow>()).BuildEntity(reader, new Workflow())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="Workflow" />from Workflow table by GroupId.
        /// </summary>
        /// <param name="id">GroupId of the Workflow</param>
        /// <returns>Workflow</returns>
        /// <author>Kiran Ghadge</author>
        Workflow IWorkflowDataAccess.GetByGroupId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "Workflow_GetByGroupId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<Workflow>()).BuildEntity(reader, new Workflow())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetByGroupId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<Workflow> IWorkflowDataAccess.GetWorkFlowsByInfraID(int id)
        {
            try
            {
                const string sp = "Workflow_GetAllByInfraObjectID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Workflow>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<Workflow> IWorkflowDataAccess.GetWrkflwcust(int creatorid)
        {
            const string sp = "GROUPWKFLOW_GETALLBYCustom";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                try
                {
                    Database.AddInParameter(cmd, Dbstring + "icreatorid", DbType.Int32, creatorid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Workflow>().BuildEntities(reader);
                    }
                }
                catch (Exception ex)
                {
                    throw new CpException(CpExceptionType.DataAccessFetchOperation,
                        ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                        "Error In DAL While Executing Function Signature IGroupWorkflowDataAccess.GetWrkflwcust(" +
                        creatorid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
                }
            }
        }

        /// <summary>
        ///     Get <see cref="Workflow" />from Workflow table by InfraObjectId.
        /// </summary>
        /// <param name="id">InfaObjectId and ActionType of the GroupWorkflow</param>
        /// <returns>Workflow</returns>
        /// <author>Satyam Kumar</author>
        IList<Workflow> IWorkflowDataAccess.GetByActionType(int infraobjectid, int actiontype)
        {
            try
            {
                if (infraobjectid < 1)
                {
                    throw new ArgumentNullException("infraobjectid");
                }

                const string sp = "Workflow_GetByInfraActionType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObject", DbType.Int32, infraobjectid);
                    Database.AddInParameter(cmd, Dbstring + "iactionType", DbType.Int32, actiontype);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Workflow>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetByGroupId(" + infraobjectid + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="Workflow" />from Workflow table.
        /// </summary>
        /// <returns>Workflow List</returns>
        /// <author>Kiran Ghadge</author>
        IList<Workflow> IWorkflowDataAccess.GetAll()
        {
            try
            {
                const string sp = "Workflow_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Workflow>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<WorkflowGet> IWorkflowDataAccess.GetByActionIdDetails(string actionId)
        {
            try
            {
                const string sp = "Workflow_ServerAttachedDetails";

                IList<WorkflowGet> wfGet = new List<WorkflowGet>();
                WorkflowGet wfGetItem = null;

                int serverId = Convert.ToInt32(actionId);

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    cmd.CommandTimeout = 180000;

                    //string strActiondIds = "%" + actionId.Trim() + ",%";
                    //Database.AddInParameter(cmd, Dbstring + "iactionId", DbType.String, strActiondIds);
                    Database.AddInParameter(cmd, Dbstring + "iactionId", DbType.Int32, serverId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            wfGetItem = new WorkflowGet();
                            wfGetItem.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);

                            wfGetItem.WorkflowId = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            wfGetItem.ActionIds = Convert.IsDBNull(reader["ActionIds"]) ? string.Empty : Convert.ToString(reader["ActionIds"]);

                            wfGet.Add(wfGetItem);
                        }
                    }
                }
                return wfGet;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="Workflow" />from Workflow table.
        /// </summary>
        /// <returns>Workflow List</returns>
        /// <author>Uma Mehavarnan</author>
        /// <date>14-Mar-2016</date>
        IList<Workflow> IWorkflowDataAccess.GetAllWorkflowByCompanyId(int companyId, bool isSuperAdmin)
        {
            try
            {
                const string sp = "WORKFLW_GETBYLOGGEDINCOMPID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.Int32, companyId);
                    Database.AddInParameter(cmd, Dbstring + "iIsSuperAdmin", DbType.Boolean, isSuperAdmin);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Workflow>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetAllWorkflowByCompanyId" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="Workflow" />from Workflow table by Id.
        /// </summary>
        /// <param name="id">Id of the Workflow</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IWorkflowDataAccess.DeleteById(int id)
        {
            try
            {
                const string sp = "Workflow_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (returnCode == -1)
                        return true;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting Workflow Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Check <see cref="Workflow" />from Workflow table by name.
        /// </summary>
        /// <param name="name">Name of the Workflow</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IWorkflowDataAccess.IsExistByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "Workflow_IsExistByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException("An unexpected error has occurred while deleting this Workflow.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.IsExistByName (" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<Workflow> IWorkflowDataAccess.GetWorkflowByuser(string userRole, int userId)
        {
            try
            {
                const string sp = "GetWorkflowByuserRole";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iuserRole", DbType.AnsiString, userRole);
                    Database.AddInParameter(cmd, Dbstring + "iuserId", DbType.Int32, userId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Workflow>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByuser" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        Workflow IWorkflowDataAccess.GetWorkflowByWrkName(string WrkflwName)
        {
            try
            {
                if (string.IsNullOrEmpty(WrkflwName))
                {
                    throw new ArgumentNullException("WrkflwName");
                }

                const string sp = "WORKFLOW_GETBYWRKFLWNAME";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iWrkflwName", DbType.AnsiString, WrkflwName);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<Workflow>()).BuildEntity(reader, new Workflow())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByWrkName(" + WrkflwName + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        Workflow IWorkflowDataAccess.lockwf(int id, int lockid)
        {
            try
            {
                const string sp = "lockWorkflow";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iIsLock", DbType.AnsiString, lockid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<Workflow>()).BuildEntity(reader, new Workflow())
                            : null;
                    }
                    //using (IDataReader reader = Database.ExecuteReader(cmd))
                    //{
                    //    workflow = reader.Read() ? CreateEntityBuilder<Workflow>().BuildEntity(reader, workflow) : null;
                    //}

                    //if (workflow == null)
                    //{
                    //    int returnCode = GetReturnCodeFromParameter(cmd);

                    //    switch (returnCode)
                    //    {
                    //        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                    //            {
                    //                throw new ArgumentException("Workflow already lock . Please specify another workflow.");
                    //            }
                    //        default:
                    //            {
                    //                throw new SystemException(
                    //                    "An unexpected error has occurred while lokcing this workflow.");
                    //            }
                    //    }
                    //}

                    // return workflow;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Workflow Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        Workflow IWorkflowDataAccess.updatexml(string xml, int id, string Actionid)
        {
            try
            {
                const string sp = "UpadateWorkflowXmlByid";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "ixml", DbType.AnsiString, xml);
                    Database.AddInParameter(cmd, Dbstring + "iActionId", DbType.AnsiString, Actionid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<Workflow>()).BuildEntity(reader, new Workflow())
                            : null;
                    }
                    //using (IDataReader reader = Database.ExecuteReader(cmd))
                    //{
                    //    workflow = reader.Read() ? CreateEntityBuilder<Workflow>().BuildEntity(reader, workflow) : null;
                    //}

                    //if (workflow == null)
                    //{
                    //    int returnCode = GetReturnCodeFromParameter(cmd);

                    //    switch (returnCode)
                    //    {
                    //        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                    //            {
                    //                throw new ArgumentException("Workflow already exists. Please specify another workflow.");
                    //            }
                    //        default:
                    //            {
                    //                throw new SystemException(
                    //                    "An unexpected error has occurred while updating this workflow.");
                    //            }
                    //    }
                    //}

                    //return workflow;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Workflow Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        IList<WorkflowManagementDetails> IWorkflowDataAccess.GetWorkflowDetails(string userrole, int userid)
        {
            try
            {
               // const string sp = " ";
                const string sp = "GETGROUPWFDETAILS";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "p_iuserRole", DbType.String, userrole);
                    Database.AddInParameter(cmd, Dbstring + "p_iuserId", DbType.Int32, userid);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    IList<WorkflowManagementDetails> items = new List<WorkflowManagementDetails>();
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            WorkflowManagementDetails item = new WorkflowManagementDetails();
                            item.id = Convert.IsDBNull(reader["id"]) ? 0 : Convert.ToInt32(reader["id"]);
                            item.WorkflowName = Convert.IsDBNull(reader["WorkflowName"]) ? string.Empty : Convert.ToString(reader["WorkflowName"]);
                            item.profilename = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]);
                            item.Infraobjectname = Convert.IsDBNull(reader["InfraobjectName"]) ? string.Empty : Convert.ToString(reader["InfraobjectName"]);
                            item.Username = Convert.IsDBNull(reader["Username"]) ? string.Empty : Convert.ToString(reader["Username"]);
                            item.IsLock = Convert.IsDBNull(reader["IsLock"]) ? string.Empty : Convert.ToString(reader["IsLock"]);
                            item.ActionType = Convert.IsDBNull(reader["ActionType"]) ? 0: Convert.ToInt32(reader["ActionType"]);
                            item.BusinessService = Convert.IsDBNull(reader["BusinessService"]) ? string.Empty : Convert.ToString(reader["BusinessService"]);
                            item.BusinessFunction = Convert.IsDBNull(reader["BusinessFunction"]) ? string.Empty : Convert.ToString(reader["BusinessFunction"]);

                            items.Add(item);
                        }

                    }
                    return items;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #region WorkflowHistory

        bool IWorkflowDataAccess.AddWorkflowHistory(Workflow workflow)
        {
            try
            {
                int returnCode = 0;

                const string sp = "WORKFLOWHISTORY_CREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, workflow.Name);
                    Database.AddInParameter(cmd, Dbstring + "iXml", DbType.AnsiString, workflow.Xml);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, workflow.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iCreateDate", DbType.DateTime, workflow.CreateDate);
                    Database.AddInParameter(cmd, Dbstring + "iActionIds", DbType.AnsiString, workflow.ActionIds);
                    Database.AddInParameter(cmd, Dbstring + "iVersion", DbType.Double, workflow.Version);
                    Database.AddInParameter(cmd, Dbstring + "iUser", DbType.AnsiString, workflow.User);
                    Database.AddInParameter(cmd, Dbstring + "iDescription", DbType.AnsiString, workflow.Description);
                    Database.AddInParameter(cmd, Dbstring + "iVersionType", DbType.AnsiString, workflow.VersionType);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowId", DbType.Int32, workflow.WorkflowId);
                    Database.AddInParameter(cmd, Dbstring + "iOldActionId", DbType.Int32, workflow.OldActionId);
                    Database.AddInParameter(cmd, Dbstring + "iActionVersion", DbType.Double, workflow.ActionVersion);
                    Database.AddInParameter(cmd, Dbstring + "iOldSaveActionIds", DbType.AnsiString, workflow.OldSaveActionIds);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting WorkflowHistory_Old Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        IList<Workflow> IWorkflowDataAccess.GetAllWorkflowHistoryById(int id)
        {
            try
            {
                const string sp = "WORKFLOWHISTORYBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        var workflows = new List<Workflow>();

                        while (reader.Read())
                        {
                            Workflow workflow = new Workflow();
                            workflow.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            workflow.WorkflowId = Convert.IsDBNull(reader["WorkflowId"]) ? 1 : Convert.ToInt32(reader["WorkflowId"]);
                            workflow.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            workflow.Xml = Convert.IsDBNull(reader["Xml"]) ? string.Empty : Convert.ToString(reader["Xml"]);
                            workflow.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
                            workflow.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            workflow.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"].ToString());
                            workflow.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
                            workflow.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"].ToString());
                            workflow.ActionIds = Convert.IsDBNull(reader["ACTIONIDS"]) ? null : Convert.ToString(reader["ACTIONIDS"]);
                            workflow.Version = Convert.IsDBNull(reader["Version"]) ? 1.0 : Convert.ToDouble(reader["Version"]);
                            workflow.User = Convert.IsDBNull(reader["USERNAME"]) ? string.Empty : Convert.ToString(reader["USERNAME"]);
                            workflow.Description = Convert.IsDBNull(reader["Description"]) ? string.Empty : Convert.ToString(reader["Description"]);
                            workflow.VersionType = Convert.IsDBNull(reader["VersionType"]) ? string.Empty : Convert.ToString(reader["VersionType"]);
                            workflow.OldActionId = Convert.IsDBNull(reader["OldActionId"]) ? 0 : Convert.ToInt32(reader["OldActionId"]);
                            workflow.ActionVersion = Convert.IsDBNull(reader["ActionVersion"]) ? 0 : Convert.ToDouble(reader["ActionVersion"]);
                            workflow.OldSaveActionIds = Convert.IsDBNull(reader["OldSaveActionIds"]) ? string.Empty : Convert.ToString(reader["OldSaveActionIds"]);
                            workflows.Add(workflow);
                        }

                        return (workflows.Count > 0) ? workflows : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetAllWorkflowOldHistoryById" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<WorkflowAction> IWorkflowDataAccess.GetActionNameByIds(string actionids)
        {
            try
            {
                const string sp = "GETWFACTIONNAMEBYIDS";
                string actionname = "";
                IList<WorkflowAction> ActionList = new List<WorkflowAction>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif
                    Database.AddInParameter(cmd, Dbstring + "iActionids", DbType.AnsiString, actionids);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            WorkflowAction WorkflowAction = new WorkflowAction();

                            WorkflowAction.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            WorkflowAction.Name = Convert.IsDBNull(reader["Name"])
                                ? string.Empty
                                : CryptographyHelper.Md5Decrypt(reader["Name"].ToString());
                            //actionname = actionname + CryptographyHelper.Md5Decrypt((Convert.IsDBNull(reader["name"]) ? string.Empty : Convert.ToString(reader["name"]))) + "~";
                            ActionList.Add(WorkflowAction);
                        }

                        // return actionname.Substring(0, actionname.Length - 1);

                    }
                    return ActionList;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetOldActionNameByIds" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
        
        Workflow IWorkflowDataAccess.GetWorkflowByHistoryId(int id)
        {
            try
            {
                const string sp = "GETWORKFLOWHISTORYBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        Workflow workflow = new Workflow();

                        while (reader.Read())
                        {
                            workflow.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            workflow.WorkflowId = Convert.IsDBNull(reader["WorkflowId"]) ? 0 : Convert.ToInt32(reader["WorkflowId"]);
                            workflow.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            workflow.Xml = Convert.IsDBNull(reader["Xml"]) ? string.Empty : Convert.ToString(reader["Xml"]);
                            workflow.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
                            workflow.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            workflow.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"].ToString());
                            workflow.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
                            workflow.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"].ToString());
                            workflow.ActionIds = Convert.IsDBNull(reader["ACTIONIDS"]) ? null : Convert.ToString(reader["ACTIONIDS"]);
                            workflow.Version = Convert.IsDBNull(reader["Version"]) ? 1.0 : Convert.ToDouble(reader["Version"]);
                            workflow.User = Convert.IsDBNull(reader["USERNAME"]) ? string.Empty : Convert.ToString(reader["USERNAME"]);
                            workflow.Description = Convert.IsDBNull(reader["Description"]) ? string.Empty : Convert.ToString(reader["Description"]);
                            workflow.VersionType = Convert.IsDBNull(reader["VersionType"]) ? string.Empty : Convert.ToString(reader["VersionType"]);
                            workflow.OldActionId = Convert.IsDBNull(reader["OldActionId"]) ? 0 : Convert.ToInt32(reader["OldActionId"]);
                            workflow.ActionVersion = Convert.IsDBNull(reader["ActionVersion"]) ? 0 : Convert.ToDouble(reader["ActionVersion"]);
                            workflow.OldSaveActionIds = Convert.IsDBNull(reader["OldSaveActionIds"]) ? string.Empty : Convert.ToString(reader["OldSaveActionIds"]);
                        }
                        return workflow;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetworkflowOldByhistoryId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<Workflow> IWorkflowDataAccess.GetWorkflowByActionId(string id)
        {
            try
            {

                const string sp = "GETWORKFLOWSBYACTIONID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.String, "%" + id + "%");
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Workflow>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowNEWDataAccess.GetworkflowOldByActionId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<WorkflowManagementDetails> IWorkflowDataAccess.GetWorkflowByuserId(int userId, bool isSuperAdmin)
        {
            try
            {
                const string sp = "GetWorkflowByuserId";

               
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iuserId", DbType.Int32, userId);
                    Database.AddInParameter(cmd, Dbstring + "iIsSuperAdmin", DbType.Boolean, isSuperAdmin);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    IList<WorkflowManagementDetails> items = new List<WorkflowManagementDetails>();
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            WorkflowManagementDetails item = new WorkflowManagementDetails();
                            item.id = Convert.IsDBNull(reader["id"]) ? 0 : Convert.ToInt32(reader["id"]);
                            item.WorkflowName = Convert.IsDBNull(reader["WorkflowName"]) ? string.Empty : Convert.ToString(reader["WorkflowName"]);
                            item.profilename = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]);
                            item.Infraobjectname = Convert.IsDBNull(reader["InfraobjectName"]) ? string.Empty : Convert.ToString(reader["InfraobjectName"]);
                            item.Username = Convert.IsDBNull(reader["Username"]) ? string.Empty : Convert.ToString(reader["Username"]);
                            item.IsLock = Convert.IsDBNull(reader["IsLock"]) ? string.Empty : Convert.ToString(reader["IsLock"]);

                            items.Add(item);
                        }

                    }
                    return items;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByuserId" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
        
        #endregion WorkflowHistory



//        IList<T> IWorkflowDataAccess.GetUserComponentByRole<T>(string type, int userId)
//        {
//            try
//            {
//                const string sp = "GetCompByCreatorNType";
//                List<T> list = new List<T>();
//                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
//                {
//                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, type);
//                    Database.AddInParameter(cmd, Dbstring + "iuserId", DbType.Int32, userId);
//#if ORACLE
//                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
//#endif
//                    using (IDataReader reader = Database.ExecuteReader(cmd))
//                    {
//                        T obj = default(T);
//                        while (reader.Read())
//                        {
//                            obj = Activator.CreateInstance<T>();
//                            obj.GetType().GetProperty("Id").SetValue(obj, Convert.ToInt32(reader["Id"]), null);
//                            obj.GetType().GetProperty("Name").SetValue(obj, Convert.ToString(reader["Name"]), null);

//                            list.Add(obj);
//                        }

//                    }
//                    return list;
//                }
//            }
//            catch (Exception ex)
//            {
//                throw new CpException(CpExceptionType.DataAccessFetchOperation,
//                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
//                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByuser" + Environment.NewLine +
//                    "SYSTEM MESSAGE : " + ex.Message, ex);
//            }
//        }

        #endregion Methods
    }
}