﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;
using System.Collections.Generic;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DatabaseBase", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class DatabaseBase : BaseEntity
    {
        #region Member Variables

        private IList<SubstituteAuthentication> _authentication = new List<SubstituteAuthentication>();
        private SubstituteAuthentication _authentication1 = new SubstituteAuthentication();
        private DatabaseDB2 _databaseDb2 = new DatabaseDB2();
        private DatabaseExchange _databaseExchange = new DatabaseExchange();
        private DataBaseExchangeDAG _databaseExchangeDAG = new DataBaseExchangeDAG();

        private DatabaseOracle _databaseOracle = new DatabaseOracle();

        private DatabaseOracleRac _databaseOracleRac = new DatabaseOracleRac();
        private DatabaseSql _databaseSql = new DatabaseSql();

        private DatabaseNodes _databasenode = new DatabaseNodes();

        private DatabaseMySql __databaseMySql = new DatabaseMySql();

        private PostgreSql __postgresql = new PostgreSql();

        private DatabasePostgre9x __postgre9x = new DatabasePostgre9x();

        private DatabaseSqlNative2008 _mssql = new DatabaseSqlNative2008();

        private DataBaseSyBase _sybase = new DataBaseSyBase();

        private DatabaseMaxDB _maxDB = new DatabaseMaxDB();

        private DatabaseSybaseWithSrs _sybaseWithSrs = new DatabaseSybaseWithSrs();

        private MongoDB _mongodb = new MongoDB();

        private DatabaseSybaseWithRsHadr _sybasewithrshadr = new DatabaseSybaseWithRsHadr();

        private DatabaseHANADB _databaseHanaDb = new DatabaseHANADB();

        private DatabaseCloudantNoSQL __databasecloudantnosql = new DatabaseCloudantNoSQL();

        private DatabaseOceanstorMssql _omssql = new DatabaseOceanstorMssql();

        private MariaDB _MariaDB = new MariaDB();
        //ITIT-7572
        private RedisCLIModeDB _RedisCLIMode = new RedisCLIModeDB();
       
        #endregion Member Variables

        #region Properties

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public DatabaseType DatabaseType { get; set; }

        [DataMember]
        public DatabaseType DatabaseName { get; set; }

        [DataMember]
        public string Version { get; set; }

        [DataMember]
        public string Type { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public int Racdbnode { get; set; }

        [DataMember]
        public bool IsPartofRac { get; set; }

        [DataMember]
        public DatabaseMode Mode { get; set; }

        [DataMember]
        public string DatabaseConnectivity { get; set; }

        [DataMember]
        public DatabaseExchange DatabaseExchange
        {
            get { return _databaseExchange; }
            set { _databaseExchange = value; }
        }

        [DataMember]
        public DatabaseOracle DatabaseOracle
        {
            get { return _databaseOracle; }
            set { _databaseOracle = value; }
        }

        [DataMember]
        public DatabaseSql DatabaseSql
        {
            get { return _databaseSql; }
            set { _databaseSql = value; }
        }

        [DataMember]
        public DatabaseOracleRac DatabaseOracleRac
        {
            get { return _databaseOracleRac; }
            set { _databaseOracleRac = value; }
        }

        [DataMember]
        public DatabaseDB2 DatabaseDb2
        {
            get { return _databaseDb2; }
            set { _databaseDb2 = value; }
        }

        [DataMember]
        public DataBaseExchangeDAG DatabaseExcahngeDAG
        {
            get { return _databaseExchangeDAG; }
            set { _databaseExchangeDAG = value; }
        }

        [DataMember]
        public DatabaseNodes DatabaseNode
        {
            get { return _databasenode; }
            set { _databasenode = value; }
        }

        [DataMember]
        public DatabaseMySql DatabaseMySql
        {
            get { return __databaseMySql; }
            set { __databaseMySql = value; }
        }

        [DataMember]
        public PostgreSql PostgreSql
        {
            get { return __postgresql; }
            set { __postgresql = value; }
        }

        [DataMember]
        public DatabasePostgre9x DatabasePostgre9x
        {
            get { return __postgre9x; }
            set { __postgre9x = value; }
        }

        [DataMember]
        public int DBServerTypeCount { get; set; }

        public DatabaseSqlNative2008 Databasemssql
        {
            get { return _mssql; }
            set { _mssql = value; }
        }

        public DataBaseSyBase Databasesybase
        {
             get { return _sybase; }
            set { _sybase = value; }
         }
        public DatabaseSybaseWithSrs DatabasesybaseWithSrs
        {
            get { return _sybaseWithSrs; }
            set { _sybaseWithSrs = value; }
        }
      
        public DatabaseMaxDB DatabasemaxDB
        {
            get { return _maxDB; }
            set { _maxDB = value; }
        }
        public MongoDB mongodb
        {
            get { return _mongodb; }
            set { _mongodb = value; }
        }

        public DatabaseSybaseWithRsHadr DatabaseSybaseWithRSHADR
        {
            get { return _sybasewithrshadr; }
            set { _sybasewithrshadr = value; }
        }

        [DataMember]
        public DatabaseHANADB DatabaseHanaDb
        {
            get { return _databaseHanaDb; }
            set { _databaseHanaDb = value; }
        }

        public SubstituteAuthentication SubstituteAuthentication1
        {
            get { return _authentication1; }
            set { _authentication1 = value; }
        }


        public IList<SubstituteAuthentication> SubstituteAuthentication
        {
            get
            {
                return _authentication;
            }

            set
            {
                _authentication = value;
            }
        }
        public DatabaseCloudantNoSQL DatabaseCloudantNoSQL
        {
            get { return __databasecloudantnosql; }
            set { __databasecloudantnosql = value; }
        }
        public DatabaseOceanstorMssql DatabaseMssqlfulldb
        {
            get { return _omssql; }
            set { _omssql = value; }
        }

        [DataMember]
        public MariaDB MariaDB
        {
            get { return _MariaDB; }
            set { _MariaDB = value; }
        }
        //ITIT-7572
        public RedisCLIModeDB RedisCLIMode
        {
            get { return _RedisCLIMode; }
            set { _RedisCLIMode = value; }
        }
        #endregion Properties
    }
}