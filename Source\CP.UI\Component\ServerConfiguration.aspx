﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="ServerConfiguration.aspx.cs" Inherits="CP.UI.ServerConfiguration"
    Title="Continuity Patrol :: Component-ServerConfiguration" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <style>
        .chosen-select + .chosen-container {
            width: 48% !important;AppNodeServer
            opacity: 1 !important;
        }

        select[id$=ddlAuthType] option, select[id$=ddlEditAuthType] option {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            padding: 20px 10px;
            background-color: white;
        }

        select[id$=ddlAuthType], select[id$=ddlEditAuthType] {
            text-align: left;
        }
    </style>
    <script src="../Script/ServerConfiguration.js"></script>
    <script src="../Script/EncryptDecrypt.js"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js"></script>
    <script>
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
        //function pageLoad() {
        //    $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        // }
    </script>
    <script type="text/javascript">
        $(document).ready(function () {
            var getipaddress = localStorage.getItem("ADMIP");
            if (getipaddress != null) {
                $("[id$=txtIPAddress]").val(getipaddress);
            }
            var txtBoxValue;


            $("input[id$=txtSSHKeyPassword]").focusout(function () {
                if ($("input[id$=txtSSHKeyPassword]").val() == '') {
                    $(this).val(txtBoxValue);
                }
            });
            $("input[id$=txtSSHPassword]").focusout(function () {
                if ($("input[id$=txtSSHPassword]").val() == '') {
                    $(this).val(txtBoxValue);
                }
            });
            $("input[id$=txtSudoUserPassword]").focusout(function () {
                if ($("input[id$=txtSudoUserPassword]").val() == '') {
                    $(this).val(txtBoxValue);
                }
            });
        });
        function CancelClick() {
            return false;
        }
    </script>


</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">

    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>


                <h3>
                    <img src="../Images/icons/server.png" />
                    Server Configuration</h3>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtHostName">
                                        Name <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtHostName" runat="server" class="form-control" AutoPostBack="True"
                                            OnTextChanged="TxtHostNameTextChanged" TabIndex="1" autocomplete="off"></asp:TextBox>
                                        <asp:Label ID="lblName" runat="server" ForeColor="Red" Text=""></asp:Label>
                                        <asp:RequiredFieldValidator ID="rfvHostName" runat="server" CssClass="error" ErrorMessage="Enter Server Name"
                                            ControlToValidate="txtHostName" Display="Dynamic"></asp:RequiredFieldValidator>

                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="ddlSite">
                                        Site<span class="inactive">*</span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlSite" runat="server" TabIndex="2" CssClass="chosen-select col-md-6"
                                            data-style="btn-default">
                                            <asp:ListItem Value="0">Select Site</asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvddlSite" runat="server" CssClass="error" ControlToValidate="ddlSite"
                                            Display="Dynamic" ErrorMessage="Select Site" InitialValue="0"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="ddlServerType">
                                        Role <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlServerRole" runat="server" TabIndex="3"
                                            CssClass="chosen-select col-md-6" data-style="btn-default">
                                            <asp:ListItem Value="0">Select Server Role</asp:ListItem>
                                            <asp:ListItem Value="1">Application</asp:ListItem>
                                            <asp:ListItem Value="2">Database</asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator5" runat="server" CssClass="error" ControlToValidate="ddlServerRole"
                                            Display="Dynamic" InitialValue="0" ErrorMessage="Select Server Role"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="ddlServerType">
                                        Type <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlServerType" runat="server" TabIndex="4" AutoPostBack="true"
                                            CssClass="chosen-select col-md-6" data-style="btn-default" OnSelectedIndexChanged="DdlServerTypeSelectedIndexChanged">
                                            <asp:ListItem Value="0">Select Server Type</asp:ListItem>
                                            <asp:ListItem Value="PRDBServer">PRDBServer</asp:ListItem>
                                            <asp:ListItem Value="DRDBServer">DRDBServer</asp:ListItem>
                                            <asp:ListItem Value="DSCLIServer">DSCLIServer</asp:ListItem>
                                            <asp:ListItem Value="HMCServer">HMCServer</asp:ListItem>
                                            <asp:ListItem Value="PRAppServer">PRAppServer</asp:ListItem>
                                            <asp:ListItem Value="DRAppServer">DRAppServer</asp:ListItem>
                                            <asp:ListItem Value="PRESXIServer">PRESXIServer</asp:ListItem>
                                            <asp:ListItem Value="DRESXIServer">DRESXIServer</asp:ListItem>
                                            <asp:ListItem Value="DNSServer">DNSServer</asp:ListItem>
                                            <asp:ListItem Value="SymCLIServer">SymCLIServer</asp:ListItem>
                                            <asp:ListItem Value="XCLIServer">XCLIServer</asp:ListItem>
                                            <asp:ListItem Value="SybRepServer">SybRepServer</asp:ListItem>
                                            <asp:ListItem Value="PRIsilonOneFS">PRIsilonOneFS</asp:ListItem>
                                            <asp:ListItem Value="DRIsilonOneFS">DRIsilonOneFS</asp:ListItem>
                                            <asp:ListItem Value="PRNaviseccliServer">PRNaviseccliServer</asp:ListItem>
                                            <asp:ListItem Value="DRNaviseccliServer">DRNaviseccliServer</asp:ListItem>
                                            <asp:ListItem Value="UEMCCLI">UEMCCLI</asp:ListItem>
                                            <asp:ListItem Value="NearDRDBServer">NearDRDBServer</asp:ListItem>
                                            <asp:ListItem Value="PRNutanixLeapServer">PRNutanixLeapServer</asp:ListItem>
                                            <asp:ListItem Value="DRNutanixLeapServer">DRNutanixLeapServer</asp:ListItem>
                                            <asp:ListItem Value="PRNutanixServer">PRNutanixServer</asp:ListItem>
                                            <asp:ListItem Value="DRNutanixServer">DRNutanixServer</asp:ListItem>
                                           <asp:ListItem Value="PRVsphereServer">PRVsphereServer</asp:ListItem>
                                            <asp:ListItem Value="DRVsphereServer">DRVsphereServer</asp:ListItem>
                                            <asp:ListItem Value="AppNodeServer">AppNodeServer</asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvddlServerType" runat="server" CssClass="error" ControlToValidate="ddlServerType"
                                            Display="Dynamic" InitialValue="0" ErrorMessage="Select Server Type"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtIPAddress" id="Label1" runat="server">
                                        This is a Part of Cluster</label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="chkCluster" runat="server" TabIndex="5"
                                            AutoPostBack="true" OnCheckedChanged="ChkClusterCheckedChanged" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtIPAddress" id="ipAddress" runat="server">
                                        IP Address <%--<span class="inactive">*</span>--%></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtIPAddress" runat="server" class="form-control" TabIndex="6" autocomplete="off"></asp:TextBox>
                                        <%--<asp:RequiredFieldValidator ID="rfvIPAddress" CssClass="error" runat="server" ControlToValidate="txtIPAddress"
                                            ErrorMessage="Enter IP Address" Display="Dynamic"></asp:RequiredFieldValidator>--%>
                                        <asp:RegularExpressionValidator ID="revIPAddress" runat="server" CssClass="error" ControlToValidate="txtIPAddress"
                                            ErrorMessage="Enter Valid IP format" ValidationExpression="^(([01]?\d\d?|2[0-4]\d|25[0-5])\.){3}([01]?\d\d?|25[0-5]|2[0-4]\d)$"
                                            Display="Dynamic"></asp:RegularExpressionValidator>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtHostName1" id="Label2" runat="server">
                                        Host Name <%--<span class="inactive">*</span>--%></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtHostName1" runat="server" class="form-control" TabIndex="7" autocomplete="off"></asp:TextBox>
                                        <asp:Label ID="lbltextid" runat="server" Text="Enter Either IP Address Or Host Name." Visible="false" CssClass="error"></asp:Label>
                                        <%--<asp:RequiredFieldValidator ID="RequiredFieldValidator3" CssClass="error" runat="server" ControlToValidate="txtHostName1"
                                            ErrorMessage="Enter Host Name" Display="Dynamic"></asp:RequiredFieldValidator>--%>
                                  <%--<asp:Label ID="lblIPHostError123" runat="server" Visible="false" CssClass="error" Text="Enter Either IP Address Or Host Name."> </asp:Label>--%>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtPort" id="lblPort" runat="server">
                                        Port <span class="inactive"></span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtPort" runat="server" class="form-control" TabIndex="8" autocomplete="off"></asp:TextBox>


                                        <asp:RegularExpressionValidator ID="revPort" runat="server" CssClass="error" ControlToValidate="txtPort"
                                            ErrorMessage="Enter Only Number" ValidationExpression="^[0-9]+$"
                                            Display="Dynamic"></asp:RegularExpressionValidator>
                                    </div>
                                </div>

                                <asp:Panel ID="pnlPowerShell" runat="server" Visible="false">
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="ddlWinRMPort">
                                            WinRM Port <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:DropDownList ID="ddlWinRMPort" runat="server" TabIndex="4"
                                                CssClass="selectpicker col-md-6" data-style="btn-default">
                                                <asp:ListItem Value="0">Select WinRM Port</asp:ListItem>
                                                <asp:ListItem Value="5985">http</asp:ListItem>
                                                <asp:ListItem Value="5986">https</asp:ListItem>
                                            </asp:DropDownList>
                                            <asp:RequiredFieldValidator ID="reqValidatorWinRMPort" runat="server" CssClass="error" ControlToValidate="ddlWinRMPort"
                                                Display="Dynamic" InitialValue="0" ErrorMessage="Select WinRM Port"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="ddlProxyAccess">
                                            Proxy Access Type <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:DropDownList ID="ddlProxyAccess" runat="server" TabIndex="3"
                                                CssClass="selectpicker col-md-6" data-style="btn-default">
                                                <asp:ListItem Value="0">Select Proxy Access Type</asp:ListItem>
                                                <asp:ListItem Value="IEConfig">IEConfig</asp:ListItem>
                                                <asp:ListItem Value="WinHttpConfig">WinHttpConfig</asp:ListItem>
                                                <asp:ListItem Value="AutoDetect">AutoDetect</asp:ListItem>
                                                <asp:ListItem Value="NoProxyServer">NoProxyServer</asp:ListItem>
                                                <asp:ListItem Value="None">None</asp:ListItem>
                                            </asp:DropDownList>
                                            <asp:RequiredFieldValidator ID="reqValidatorProxyAccess" runat="server" CssClass="error" ControlToValidate="ddlProxyAccess"
                                                Display="Dynamic" InitialValue="0" ErrorMessage="Select Proxy Access Type"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>

                                </asp:Panel>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="ddlOS">
                                        Operating System <span class="inactive">*</span></label>
                                    <div class="col-md-9">

                                        <asp:DropDownList ID="ddlOS" runat="server" TabIndex="9" AutoPostBack="true" OnSelectedIndexChanged="ddlOS_SelectedIndexChanged"
                                            CssClass="chosen-select col-md-6" data-style="btn-default">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvddlOS" runat="server" CssClass="error" ControlToValidate="ddlOS"
                                            Display="Dynamic" InitialValue="0" ErrorMessage="Select Operating System"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtIPAddress" id="Label4" runat="server">
                                        Virtual Guest OS</label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="chkVirtualGuestOS" runat="server" TabIndex="10" />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Authentication Type <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlAuthenticationType" runat="server" TabIndex="11" AutoPostBack="true"
                                            CssClass="selectpicker col-md-6" data-style="btn-default" OnSelectedIndexChanged="ddlAuthenticationType_SelectedIndexChanged">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" CssClass="error" ControlToValidate="ddlOS"
                                            Display="Dynamic" InitialValue="0" ErrorMessage="Select Authentication Type"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group" runat="server" id="dvUser" visible="false">
                                    <label class="col-md-3 control-label" for="lblSSHUser">
                                        <asp:Label ID="lblSSHUser" runat="server" Text=""></asp:Label>
                                        <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtSSHUser" runat="server" class="form-control" TabIndex="12" autocomplete="off" onblur="getHashData(this)" onfocus="clearText(this)"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvtxtSSHUser" runat="server" CssClass="error" ErrorMessage="Enter SSH User"
                                            ControlToValidate="txtSSHUser" Display="Dynamic"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="rfeSShUser" runat="server" CssClass="error" ControlToValidate="txtSSHUser"
                                            ErrorMessage="Enter Valid SSH User" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"
                                            Display="Dynamic"></asp:RegularExpressionValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtIPAddress" id="Label3" runat="server">
                                        SSO Enabled</label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="ChkSSOEnable" runat="server" TabIndex="13"
                                            AutoPostBack="True" OnCheckedChanged="ChkSSOEnableCheckedChanged" />
                                    </div>
                                </div>

                                <asp:Panel ID="pnlSSOType" runat="server" Visible="false">
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="ddlServerType">
                                            SignOn Type <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:DropDownList ID="ddlSignOnType" runat="server"
                                                CssClass="selectpicker col-md-6" data-style="btn-default" OnSelectedIndexChanged="ddlSignOnType_SelectedIndexChanged" AutoPostBack="true">
                                            </asp:DropDownList>
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" CssClass="error" ControlToValidate="ddlSignOnType"
                                                Display="Dynamic" InitialValue="0" ErrorMessage="Select SignOn Type"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </asp:Panel>

                                <asp:Panel ID="pnlSSoProfilrDrp" runat="server" Visible="false">
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="ddlServerType">
                                            Profile <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:DropDownList ID="ddlSSOProfile" runat="server" AutoPostBack="true" CssClass="selectpicker col-md-6"
                                                data-style="btn-default">
                                            </asp:DropDownList>
                                            <asp:RequiredFieldValidator ID="rfvssoprofile" runat="server" CssClass="error" ControlToValidate="ddlSSOProfile"
                                                Display="Dynamic" InitialValue="0" ErrorMessage="Select SSO Profile"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </asp:Panel>

                                <asp:Panel ID="pnlCyberark" runat="server" Visible="false">
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="ddlServerType">
                                            Safe <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtSafe" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                                            <asp:RequiredFieldValidator ID="rfvSafe" CssClass="error" runat="server" ControlToValidate="txtSafe"
                                                ErrorMessage="Enter safe" Display="Dynamic"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="ddlServerType">
                                            Object <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtObject" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                                            <asp:RequiredFieldValidator ID="rfvObject" CssClass="error" runat="server" ControlToValidate="txtObject"
                                                ErrorMessage="Enter safe" Display="Dynamic"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="ddlServerType">
                                            Folder <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtFolder" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                                            <asp:RequiredFieldValidator ID="rfvFolder" CssClass="error" runat="server" ControlToValidate="txtFolder"
                                                ErrorMessage="Enter safe" Display="Dynamic"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="ddlServerType">
                                            Reason <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtReason" runat="server" class="form-control" autocomplete="off" TextMode="MultiLine"></asp:TextBox>
                                            <asp:RequiredFieldValidator ID="rfvReason" CssClass="error" runat="server" ControlToValidate="txtReason"
                                                ErrorMessage="Enter safe" Display="Dynamic"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </asp:Panel>

                                <asp:Panel ID="pnlsshkeypath" runat="server">
                                    <div class="form-group" runat="server" visible="false" id="dSSHKeyPath">
                                        <label class="col-md-3 control-label" for="lblSSHKeyUser">
                                            <asp:Label ID="lblSSHKeyUser" runat="server" Text="SSH Key Path"></asp:Label>
                                            <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtSSHKeyPath" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                                            <asp:RequiredFieldValidator ID="rfvtxtSSHKeyPath" runat="server" CssClass="error" ErrorMessage="Enter SSH Key Path"
                                                ControlToValidate="txtSSHKeyPath"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                    <div class="form-group" runat="server" visible="false" id="dSSHKeyPassword">
                                        <label class="col-md-3 control-label" for="lblSSHUser">
                                            <asp:Label ID="lblSSHKeyPassword" runat="server" Text="SSH Key Password"></asp:Label>
                                            <span class="inactive"></span>
                                        </label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtSSHKeyPassword" runat="server" CssClass="form-control chk-caps" autocomplete="off" TextMode="Password" OnTextChanged="txtSSHKeyPasswordTextChanged" onblur="getHashData(this)" onfocus="clearText(this)"></asp:TextBox>

                                            <span class="caps-error">Caps Lock is ON.</span>
                                        </div>
                                    </div>
                                </asp:Panel>

                                <div class="form-group" id="dsSSHPassword" runat="server" visible="false">
                                    <label class="col-md-3 control-label" for="lblSSHPassword">
                                        <asp:Label ID="lblSSHPassword" runat="server" Text=" "></asp:Label>
                                        <asp:Label ID="lblSshStatus" runat="server" Text=" " class="inactive"></asp:Label></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtSSHPassword" CssClass="form-control chk-capsp" runat="server" TextMode="Password" Style="float: left"
                                            TabIndex="14" autocomplete="off" OnTextChanged="TxtSSHPasswordTextChanged" onblur="getHashData(this)" onfocus="clearText(this)"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvtxtSSHPassword" runat="server" CssClass="error" ErrorMessage="Enter SSH Password" Display="Dynamic"
                                            ControlToValidate="txtSSHPassword"></asp:RequiredFieldValidator>

                                        <span class="caps-error">Caps Lock is ON.</span>
                                        <a id="btnTestConnection" class="btn btn-primary hide" runat="server" title="Test Connection" style="float: left; margin-left: 10px;">Test Connection</a>
                                        <span id="spnRunning" class="running-icon" style="display: none; margin-left: 10px; margin-top: 10px;"></span><span id="spnValidateConnection"></span>
                                    </div>
                                </div>

                                <asp:UpdatePanel ID="UpnlConSubsAuthn" UpdateMode="Conditional" runat="server">
                                    <ContentTemplate>
                                        <div id="divAuthentication">
                                            <div class="form-group">
                                                <label class="col-md-3 control-label" for="ddlSudosu">
                                                    <asp:Label ID="lblAuthentication" runat="server" Text="Substitute Authentication(Sudo/Su)"></asp:Label>
                                                    <span class="inactive"></span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:CheckBox ID="chkSubsAuth" runat="server" TabIndex="12" AutoPostBack="true" OnCheckedChanged="chkSubsAuth_CheckedChanged" Checked="false" />
                                                    <%--<asp:DropDownList ID="ddlSudosu" runat="server" TabIndex="15" AutoPostBack="true"
                                                OnSelectedIndexChanged="ddlSudosu_SelectedIndexChanged" CssClass="selectpicker col-md-6"
                                                data-style="btn-default">
                                                <asp:ListItem Value="0">Select Sudo/Su </asp:ListItem>
                                                <asp:ListItem Value="1">Sudo</asp:ListItem>
                                                <asp:ListItem Value="2">Su</asp:ListItem>
                                            </asp:DropDownList>--%>
                                                </div>
                                            </div>
                                        </div>
                                        <asp:Panel ID="pnlSudo" runat="server" Visible="False">
                                            <div class="form-group">
                                                <label class="col-md-3 control-label" for="txtSudoUser">
                                                    <asp:Label ID="lblSudo" runat="server" Text="Sudo User"></asp:Label>
                                                    <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtSudoUser" runat="server" class="form-control" AutoPostBack="True"
                                                        CausesValidation="True" OnTextChanged="txtSudoUser_TextChanged" onblur="getHashData(this)" onfocus="clearText(this)"></asp:TextBox>
                                                    <asp:RegularExpressionValidator ID="rfeSudoUser" runat="server" CssClass="error" ControlToValidate="txtSudoUser"
                                                        ErrorMessage="Enter Valid Sudo User" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"
                                                        Display="Dynamic"></asp:RegularExpressionValidator>
                                                    <asp:Label ID="LblsudouserError" runat="server" Text="Enter UserName" CssClass="error"
                                                        Visible="false"></asp:Label>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label" for="txtSudoUserPassword">
                                                    <asp:Label ID="lblsupassword" runat="server" Text="Sudo User Password"></asp:Label>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtSudoUserPassword" runat="server" CssClass="form-control chk-capspc" TabIndex="13"
                                                        Text="" autocomplete="off" TextMode="Password" onblur="getHashData(this)" onfocus="clearText(this)"></asp:TextBox>

                                                    <span class="caps-error">Caps Lock is ON.</span>
                                                </div>
                                            </div>
                                        </asp:Panel>
                                        <asp:Panel ID="pnlConSubsAuthn" runat="server" class="form-group" Visible="false">
                                            <label class="col-md-3 control-label">Configure Substitute Authentication</label>
                                            <div class="col-md-6">
                                                <div class="widget">
                                                    <asp:ListView ID="lvsubaunth" runat="server" InsertItemPosition="LastItem" OnItemCreated="lvsubaunth_ItemCreated" OnItemDataBound="lvsubaunth_ItemDataBound"
                                                        OnItemInserting="lvsubaunth_ItemInserting" OnItemEditing="lvsubaunth_ItemEditing" OnItemCanceling="lvsubaunth_ItemCanceling" OnItemUpdating="lvsubaunth_ItemUpdating"
                                                        OnItemDeleting="lvsubaunth_ItemDeleting">
                                                        <LayoutTemplate>
                                                            <table class="table table-bordered margin-bottom-none">
                                                                <thead>
                                                                    <tr>

                                                                        <th style="width: 22%;">Authenticate Type
                                                                        </th>
                                                                        <th style="width: 22%;">Path
                                                                        </th>
                                                                        <th style="width: 22%;">User
                                                                        </th>
                                                                        <th style="width: 22%;">Password
                                                                        </th>
                                                                        <th>Edit/Delete
                                                                        </th>
                                                                    </tr>
                                                                </thead>
                                                            </table>
                                                            <div id="subanunth-content">
                                                                <%--style="overflow-x: initial;"--%>
                                                                <table class="table table-bordered margin-bottom-none">
                                                                    <tbody>
                                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </LayoutTemplate>
                                                        <ItemTemplate>
                                                            <tr>
                                                                <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                <td style="width: 22%;">
                                                                    <asp:DropDownList ID="ddlDispAuthType" runat="server" CssClass="selectpicker col-md-11 padding-none "
                                                                        data-style="btn-default" Enabled="false">
                                                                    </asp:DropDownList>
                                                                    <asp:TextBox ID="txtDispAuthType" Text='<%# Eval("Auth_Type") %>' CssClass="form-control" Enabled="False" runat="server" Visible="false"> </asp:TextBox>
                                                                </td>
                                                                <td style="width: 22%;">
                                                                    <asp:TextBox ID="txtDispPath" Text='<%# Eval("Path") %>' CssClass="form-control" Enabled="False" runat="server"> </asp:TextBox>
                                                                </td>
                                                                <td style="width: 22%;">
                                                                    <asp:TextBox ID="txtDispUser" Text='<%# Eval("UserName") %>' CssClass="form-control" Enabled="False" runat="server"> </asp:TextBox>
                                                                </td>
                                                                <td style="width: 22%;">
                                                                    <asp:Label ID="lblDispPass" runat="server" Text='<%# Eval("Password") %>' Visible="false" />
                                                                    <asp:TextBox ID="txtDispPass" Text='<%# Eval("Password") %>' autocomplete="off" TextMode="Password" CssClass="form-control" Enabled="False" runat="server"> </asp:TextBox>
                                                                </td>

                                                                <td>
                                                                    <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                        ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" CausesValidation="false" />
                                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                                        ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" CausesValidation="false" />
                                                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                                        ConfirmText='<%# "Are you sure want to delete ? " %>' OnClientCancel="CancelClick">
                                                                    </TK1:ConfirmButtonExtender>
                                                                </td>
                                                            </tr>
                                                        </ItemTemplate>
                                                        <EditItemTemplate>
                                                            <tr>
                                                                <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                <td style="width: 22%;">
                                                                    <asp:TextBox ID="txtEditAuthType" Text='<%# Eval("Auth_Type") %>' CssClass="selectpicker col-md-11 padding-none" runat="server" Visible="false"> </asp:TextBox>
                                                                    <asp:DropDownList ID="ddlEditAuthType" runat="server" CssClass="btn col-md-11 padding-none btn-default"
                                                                        data-style="btn-default">
                                                                    </asp:DropDownList>
                                                                    <asp:RequiredFieldValidator ID="rfvddlEditAuthType" runat="server" CssClass="error" ControlToValidate="ddlEditAuthType"
                                                                        Display="Dynamic" InitialValue="0" ErrorMessage="Select Type" ValidationGroup="SubsAuthn"></asp:RequiredFieldValidator>
                                                                </td>
                                                                <td style="width: 22%;">
                                                                    <asp:TextBox ID="txtEditPath" Text='<%# Eval("Path") %>' CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    <%--<asp:RequiredFieldValidator ID="rfvtxtEditPath" runat="server" CssClass="error" ControlToValidate="txtEditUser"
                                                                        Display="Dynamic" ErrorMessage="*" ValidationGroup="SubsAuthn"></asp:RequiredFieldValidator>--%>
                                                                </td>
                                                                <td style="width: 22%;">
                                                                    <asp:TextBox ID="txtEditUser" Text='<%# Eval("UserName") %>' CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    <%--<asp:RequiredFieldValidator ID="rfvtxtEditUser" runat="server" CssClass="error" ControlToValidate="txtEditUser"
                                                                        Display="Dynamic" ErrorMessage="*" ValidationGroup="SubsAuthn"></asp:RequiredFieldValidator>--%>
                                                                </td>
                                                                <td style="width: 22%;">
                                                                    <asp:Label ID="lblEditPass" runat="server" Text='<%# Eval("Password") %>' Visible="false" />
                                                                    <asp:TextBox ID="txtEditPass" Text='<%# Eval("Password") %>' CssClass="form-control" runat="server" autocomplete="off" TextMode="Password"> </asp:TextBox>
                                                                    <%-- <asp:RequiredFieldValidator ID="rfvtxtEditPass" runat="server" CssClass="error" ControlToValidate="txtEditPass"
                                                                        Display="Dynamic" ErrorMessage="*" ValidationGroup="SubsAuthn"></asp:RequiredFieldValidator>--%>
                                                                </td>

                                                                <td>
                                                                    <asp:LinkButton ID="imgUpdate" runat="server" CommandName="Update" AlternateText="Update"
                                                                        ToolTip="Update" CssClass="health-up" CausesValidation="false" />
                                                                    <asp:LinkButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                                        ToolTip="Cancel" CssClass="InActive" CausesValidation="false" />

                                                                </td>
                                                            </tr>
                                                        </EditItemTemplate>
                                                        <InsertItemTemplate>
                                                            <tr>
                                                                <td style="width: 22%;">
                                                                    <asp:DropDownList ID="ddlAuthType" runat="server" CssClass="btn col-md-11 padding-none btn-default"
                                                                        data-style="btn-default">
                                                                    </asp:DropDownList>
                                                                    <asp:RequiredFieldValidator ID="rfvddlAuthType" runat="server" CssClass="error" ControlToValidate="ddlAuthType"
                                                                        Display="Dynamic" InitialValue="0" ErrorMessage="*" ValidationGroup="SubsAuthn"></asp:RequiredFieldValidator>
                                                                </td>
                                                                <td style="width: 22%;">
                                                                    <asp:TextBox ID="txtPath" Text="" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    <%--<asp:RequiredFieldValidator ID="rfvtxtPath" runat="server" CssClass="error" ControlToValidate="txtPath"
                                                                        Display="Dynamic" ErrorMessage="*" ValidationGroup="SubsAuthn"></asp:RequiredFieldValidator>--%>
                                                                </td>
                                                                <td style="width: 22%;">
                                                                    <asp:TextBox ID="txtUser" Text="" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    <%--<asp:RequiredFieldValidator ID="rfvtxtUser" runat="server" CssClass="error" ControlToValidate="txtUser"
                                                                        Display="Dynamic" ErrorMessage="*" ValidationGroup="SubsAuthn"></asp:RequiredFieldValidator>--%>
                                                                </td>
                                                                <td style="width: 22%;">
                                                                    <asp:TextBox ID="txtPass" Text="" autocomplete="off" TextMode="Password" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    <%--<asp:RequiredFieldValidator ID="rfvtxtPass" runat="server" CssClass="error" ControlToValidate="txtPass"
                                                                        Display="Dynamic" ErrorMessage="*" ValidationGroup="SubsAuthn"></asp:RequiredFieldValidator>--%>
                                                                </td>

                                                                <td>
                                                                    <asp:LinkButton ID="imgInsert" runat="server" CommandName="Insert" AlternateText="Insert"
                                                                        ToolTip="Insert" CssClass="plus" ValidationGroup="SubsAuthn" />

                                                                </td>
                                                            </tr>

                                                        </InsertItemTemplate>
                                                    </asp:ListView>
                                                </div>
                                            </div>
                                        </asp:Panel>

                                    </ContentTemplate>
                                </asp:UpdatePanel>

                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtShellPrompt">
                                        <asp:Label ID="lblShellPrompt" runat="server" Text="Shell Prompt"></asp:Label>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtShellPrompt" runat="server" class="form-control" TabIndex="16"></asp:TextBox>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtLicenceKey">
                                        <asp:Label ID="lblLicenceKey" runat="server" Text="Licence Key"></asp:Label>
                                        <span class="inactive">*</span></label></label>

                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtLicenceKey" runat="server" AutoPostBack="true" OnTextChanged="TxtLicenseTextChanged" class="form-control" TabIndex="17"></asp:TextBox>
                                            <asp:Label ID="lblErr" runat="server" Visible="false" CssClass="error"> </asp:Label>
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" CssClass="error" ErrorMessage="Enter Licence Key"
                                                ControlToValidate="txtLicenceKey"></asp:RequiredFieldValidator>
                                        </div>
                                </div>
                                <asp:UpdatePanel ID="updateISASM" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <div class="form-group">
                                            <asp:Label ID="lblIsASM" runat="server" class="col-md-3 control-label">
                        isASM Grid<span class="inactive"></span></asp:Label>

                                            <div class="col-md-9">
                                                <asp:CheckBox ID="chkIsASM" runat="server" TabIndex="5" AutoPostBack="true" Checked="false" OnCheckedChanged="chkIsASM_CheckedChanged" />

                                            </div>

                                        </div>

                                        <div class="form-group" id="divASMInstance" runat="server" visible="false">
                                            <label class="col-md-3 control-label" for="ddlASMInstance">
                                                ASM Instance</label>
                                            <div class="col-md-9">
                                                <asp:DropDownList ID="ddlASMInstance" runat="server" TabIndex="9" AutoPostBack="true" OnSelectedIndexChanged="ddlASMInstance_SelectedIndexChanged"
                                                    CssClass="selectpicker col-md-6" data-style="btn-default">
                                                </asp:DropDownList>
                                            </div>
                                        </div>

                                    </ContentTemplate>
                                </asp:UpdatePanel>
                                <asp:Panel ID="panelVmDetails" runat="server" Visible="False">
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="txtDataStoreName">
                                            <asp:Label ID="lblDataStoreName" runat="server" Text="Datastore Name"></asp:Label>
                                        </label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtDataStoreName" runat="server" class="form-control"></asp:TextBox>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="txtVmPath">
                                            <asp:Label ID="lblVmpath" runat="server" Text="VM Path"></asp:Label>
                                        </label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtVmPath" runat="server" class="form-control"></asp:TextBox>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="txtDisks">
                                            <asp:Label ID="lblDisks" runat="server" Text="Disks"></asp:Label>
                                        </label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtDisks" runat="server" class="form-control"></asp:TextBox>
                                        </div>
                                    </div>
                                </asp:Panel>

                                <hr class="separator" />
                                <div class="form-actions row">
                                    <div class="col-lg-5">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
                                    </div>
                                    <div class="col-lg-7">
                                        <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="15%" runat="server" Text="Save" Style="margin-left: 10px;"
                                            OnClick="BtnSaveClick" TabIndex="18" />
                                        <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="15%" runat="server"
                                            Text="Cancel" OnClick="BtnCancelClick" CausesValidation="False" TabIndex="19" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
    <script src="../Script/jquery.nicescroll.min.js"></script>


</asp:Content>
