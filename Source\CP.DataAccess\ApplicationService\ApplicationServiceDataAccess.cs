﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class ApplicationServiceDataAccess : BaseDataAccess, IApplicationServiceDataAccess
    {
        #region Constructors

        public ApplicationServiceDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ApplicationService> CreateEntityBuilder<ApplicationService>()
        {
            return (new ApplicationServiceBuilder()) as IEntityBuilder<ApplicationService>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        /// Get <see cref="ApplicationService" /> from bcms_application_service_logs table by InfraObjectId.
        /// </summary>
        /// <param name="applicationId">applicationId</param>
        /// <returns>ApplicationService List</returns>
        /// <author><PERSON>vraj <PERSON></author>
        /// <Modified> <PERSON><PERSON>h Thakker - 30-04-2014 </Modified>
        IList<ApplicationService> IApplicationServiceDataAccess.GetByInfraObjectId(int InfraObjectId)
        {
            try
            {
                const string sp = "AppService_GetByInfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, InfraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ApplicationService>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IApplicationServiceDataAccess.GetByApplicationId(" +
                    InfraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        IList<ApplicationService> IApplicationServiceDataAccess.GetAppMonitorBy_IO_Id(int InfraObjectId)
        {
            try
            {
                const string sp = "MONSERVICESTATUS_GET_BYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iID", DbType.Int32, InfraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        IList<ApplicationService> list = CreateEntityBuilder<ApplicationService>().BuildEntities(reader);
                        return list;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IApplicationServiceDataAccess.GetByApplicationId(" +
                    InfraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        #endregion Methods
    }
}