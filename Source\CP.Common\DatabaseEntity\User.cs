﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "User", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class User : BaseEntity
    {
        #region Member Variables

        private UserInfo _userInfo = new UserInfo();

        private CustomRoleSubType _customRoleSubType = new CustomRoleSubType();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public string LoginName { get; set; }

        [DataMember]
        public string LoginPassword { get; set; }

        [DataMember]
        public int CompanyId { get; set; }

        [DataMember]
        public UserRole Role { get; set; }

        [DataMember]
        public DateTime LastLoginDate { get; set; }

        [DataMember]
        public DateTime CurrentLoginDate { get; set; }

        [DataMember]
        public DateTime LastPasswordChanged { get; set; }

        [DataMember]
        public string CurrentLoginIP { get; set; }

        [DataMember]
        public string LastLoginIP { get; set; }

        [DataMember]
        public int LastAlertId { get; set; }

        [DataMember]
        public int LastIncidentId { get; set; }

        [DataMember]
        public bool IsReset { get; set; }

        //[DataMember]
        //public bool GroupAllFlag { get; set; }

        [DataMember]
        public bool InfraObjectAllFlag { get; set; }

        [DataMember]
        public bool ApplicationAllFlag { get; set; }

        [DataMember]
        public LoginType LoginType { get; set; }

        [DataMember]
        public UserInfo UserInformation
        {
            get { return _userInfo; }
            set { _userInfo = value; }
        }

        [DataMember]
        public CustomRoleSubType CustomSubRolType
        {
            get { return _customRoleSubType; }
            set { _customRoleSubType = value; }
        }

        [DataMember]
        public int UserId { get; set; }

        [DataMember]
        public string UserName { get; set; }
        [DataMember]
        public string SessionId { get; set; }

        [DataMember]
        public string Accessrole { get; set; }

        #endregion Properties
    }
}