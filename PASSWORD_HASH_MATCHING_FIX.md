# Password Hash Matching Fix

## Problem Description
After implementing session storage for encrypted passwords, the password hash generated by JavaScript was not matching the hash generated by the server-side code, causing login failures.

## Root Cause Analysis

### Original Issue:
The JavaScript was using `genrateUserNameHash()` function for password encryption, but this function is designed for usernames and uses a different algorithm than what the server expects for passwords.

### Algorithm Differences:

**JavaScript `genrateUserNameHash()` (WRONG for passwords):**
```javascript
// Uses Swaping(encode64(strData))
// Applies shuffling and reversing
// Returns shuffled format
```

**Server-side `Utility.getHashKeyByString()` (CORRECT for passwords):**
```csharp
// Uses Base64Encode(strPass) with character swapping
// No shuffling or reversing
// Returns simple concatenated format
```

## Solution Implemented

### 1. Created JavaScript Equivalent of Server-Side Functions

#### Base64Encode with Character Swapping:
```javascript
function base64EncodeWithSwap(plainText) {
    try {
        // Convert to UTF-8 bytes then base64
        var utf8Bytes = new TextEncoder().encode(plainText);
        var base64 = btoa(String.fromCharCode.apply(null, utf8Bytes));
        
        // Swap adjacent characters (matches server-side logic)
        var actual = "";
        for (var i = 0; i < base64.length; i += 2) {
            if (i + 1 < base64.length) {
                actual += base64[i + 1] + base64[i];
            } else {
                actual += base64[i];
            }
        }
        return actual;
    } catch (ex) {
        // Fallback for older browsers
        return btoa(unescape(encodeURIComponent(plainText)));
    }
}
```

#### Hash Key Generation (matches server-side exactly):
```javascript
function getHashKeyByString(strPass, strGuid) {
    var strResult = "";
    try {
        if (!strPass || strPass === "") {
            return "";
        }
        
        // Use same Base64Encode logic as server-side
        strPass = base64EncodeWithSwap(strPass);
        var strKeyArr = strGuid.split('-');
        var j = 0;
        
        for (var i = 0; i < strPass.length; i++) {
            if (i % 5 === 0) {
                j = 0;
            }
            strKeyArr[j] += strPass[i];
            j++;
        }
        
        for (var k = 0; k < strKeyArr.length; k++) {
            strResult += strKeyArr[k] + "-";
        }
        
        // Remove the last dash
        if (strResult.length > 0) {
            strResult = strResult.substring(0, strResult.length - 1);
        }
    } catch (ex) {
        console.error("Error in getHashKeyByString:", ex);
    }
    return strResult;
}
```

### 2. Updated Password Hashing Function

**Before (WRONG):**
```javascript
function getPasswordHash(control, callback) {
    // ...
    var strData = genrateUserNameHash(passHiddenElement, guid); // WRONG!
    // ...
}
```

**After (CORRECT):**
```javascript
function getPasswordHash(control, callback) {
    var passwordValue = control.value;
    
    getStaticGuidFromServer(function(guid) {
        // Use correct password hashing function
        var strData = getHashKeyByString(passwordValue, guid); // CORRECT!
        
        setEncryptedPasswordInSession(strData, function(success) {
            // Store in session and proceed
        });
    });
}
```

## Algorithm Verification

### Server-Side Process:
1. Get original password from database: `orignalStr = CryptographyHelper.Md5Decrypt(userDetail.LoginPassword)`
2. Generate hash: `userEncryptPass = Utility.getHashKeyByString(orignalStr, GetStaticGuidFromSession())`
3. Compare: `userEncryptPass.Equals(GetEncryptedPasswordFromSession())`

### Client-Side Process:
1. Get user-entered password: `passwordValue = control.value`
2. Generate hash: `strData = getHashKeyByString(passwordValue, guid)`
3. Store in session: `setEncryptedPasswordInSession(strData)`

### Key Points:
- Both use the same GUID from session
- Both use the same `getHashKeyByString` algorithm
- Both use the same `Base64Encode` with character swapping
- No shuffling or reversing for passwords

## Testing Steps

### 1. Debug Logging Added:
```javascript
console.log('Got GUID from server:', guid);
console.log('Generated password hash:', strData);
console.log('Encrypted password stored in session successfully');
```

### 2. Verification Process:
1. Enter password in login form
2. Check browser console for hash generation logs
3. Verify password hash is stored in session
4. Confirm server-side comparison succeeds

### 3. Test Cases:
- [ ] Simple password (e.g., "test123")
- [ ] Complex password with special characters
- [ ] Unicode characters
- [ ] Empty password handling
- [ ] Very long passwords

## Files Modified

- `Source/CP.UI/Script/Login.js`:
  - Added `base64EncodeWithSwap()` function
  - Added `getHashKeyByString()` function
  - Updated `getPasswordHash()` to use correct algorithm

## Debugging Tips

### If passwords still don't match:
1. **Check GUID consistency**: Ensure same GUID used on client and server
2. **Verify character encoding**: Check UTF-8 encoding in both places
3. **Compare step-by-step**: Log intermediate values in both JavaScript and C#
4. **Test with simple password**: Start with "test" to verify algorithm

### Console Debugging:
```javascript
// Add to getPasswordHash function for debugging
console.log('Original password:', passwordValue);
console.log('GUID:', guid);
console.log('Base64 encoded:', base64EncodeWithSwap(passwordValue));
console.log('Final hash:', strData);
```

### Server-Side Debugging:
```csharp
// Add to Login.aspx.cs for debugging
System.Diagnostics.Debug.WriteLine($"Original password: {orignalStr}");
System.Diagnostics.Debug.WriteLine($"GUID: {GetStaticGuidFromSession()}");
System.Diagnostics.Debug.WriteLine($"Server hash: {userEncryptPass}");
System.Diagnostics.Debug.WriteLine($"Session hash: {GetEncryptedPasswordFromSession()}");
```

## Expected Outcome

After this fix:
- JavaScript and server-side should generate identical password hashes
- Login should succeed with correct credentials
- Password comparison should work: `userEncryptPass.Equals(GetEncryptedPasswordFromSession())`
- Console logs should show successful hash generation and storage

## Rollback Plan

If issues persist:
1. Revert to using hidden field temporarily
2. Add extensive logging to compare algorithms step-by-step
3. Consider creating server-side web method to generate hash for comparison
4. Test with known working password/hash combinations
