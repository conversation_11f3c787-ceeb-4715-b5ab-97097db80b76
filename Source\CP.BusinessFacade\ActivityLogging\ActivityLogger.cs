﻿using System;
using System.Web;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using log4net;
using System.Net;

namespace CP.BusinessFacade
{
    public static class ActivityLogger
    {
        #region Variables

        private static readonly IFacade Facade = new Facade();

        private static readonly ILog Logger = LogManager.GetLogger(typeof(ActivityLogger));

        #endregion Variables

        #region Methods

        public static void AddLog(string logOn, UserActionType activityValue, string activityDetail, int creator)
        {
            AddLog(logOn, "-", activityValue, activityDetail, creator);
        }

        public static void AddLog(string logOn, string entity, UserActionType activityEnumValue, string activityDetail, int creator)
        {
            string hostAddress = string.Empty;
            string pageUrl = string.Empty;

            string hostName = Dns.GetHostName();
            hostAddress = Dns.GetHostByName(hostName).AddressList[0].ToString();

            if (HttpContext.Current != null)
            {
                hostAddress = HttpContext.Current.Request.UserHostAddress;
                pageUrl = HttpContext.Current.Request.Url.ToString();
                if (hostAddress.Equals("::1"))
                {
                    hostAddress = Dns.GetHostByName(hostName).AddressList[0].ToString();
                }
            }
            var userActivity = new UserActivity
                {
                    LoginName = logOn,
                    Entity = entity,
                    ActionType = activityEnumValue,
                    PageUrl = pageUrl,
                    UserHostAddress = hostAddress,
                    ActivityDetails = activityDetail,
                    CreatorId = creator
                };
            AddUserActivityIntoFile(userActivity);
            AddUserActivityIntoDatabase(userActivity);
        }

        private static void AddUserActivityIntoFile(UserActivity userActivity)
        {
            try
            {
                Logger.InfoFormat("{0} - {1} - {2}", userActivity.UserHostAddress, userActivity.LoginName, userActivity.ActivityDetails);
            }
            catch (CpException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.ExceptionLogInsert, "Error In ActivityLogger While Executing Function AddUserActivityIntoFile", ex);
            }
        }

        private static void AddUserActivityIntoDatabase(UserActivity userActivity)
        {
            try
            {
                Facade.AddUserActivity(userActivity);
            }
            catch (CpException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Logger.Info("Exception Occured while executing AddUserActivityIntoDatabase :" + ex.InnerException.Message);
                throw new CpException(CpExceptionType.DataAccessInsertOperation, "Error In ActivityLogger While Executing Function AddUserActivityIntoDatabase", ex);
            }
        }

        public static void AddLog1(string logOn, UserActionType activityValue, string activityDetail, int creator, string currentloginip)
        {
            AddLog1(logOn, "-", activityValue, activityDetail, creator, currentloginip);
        }


        public static void AddLog1(string logOn, string entity, UserActionType activityEnumValue, string activityDetail, int creator, string currentloginip)
        {
            string hostAddress = string.Empty;
            string pageUrl = string.Empty;

            string hostName = Dns.GetHostName();
            hostAddress = Dns.GetHostByName(hostName).AddressList[0].ToString();

            if (HttpContext.Current != null)
            {
                hostAddress = HttpContext.Current.Request.UserHostAddress;
                pageUrl = HttpContext.Current.Request.Url.ToString();

                //string hostName = Dns.GetHostName();
                //hostAddress = Dns.GetHostByName(hostName).AddressList[0].ToString();
                if (hostAddress.Equals("::1"))
                {
                    hostAddress = Dns.GetHostByName(hostName).AddressList[0].ToString();
                }
            }
            var userActivity = new UserActivity
            {
                LoginName = logOn,
                Entity = entity,
                ActionType = activityEnumValue,
                PageUrl = pageUrl,
                UserHostAddress = hostAddress,
                ActivityDetails = activityDetail,
                CreatorId = creator,
                CurrentLoginIP = currentloginip
            };

            AddUserActivityIntoFile(userActivity);
            AddUserActivityIntoDatabase(userActivity);
        }


        #endregion Methods
    }
}