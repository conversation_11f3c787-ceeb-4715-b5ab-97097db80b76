﻿using System;
using CP.Common.Base;
using System.Runtime.Serialization;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "SSOConfiguration", Namespace = "http://www.ContinuityPlatform.com/types")]
  public class SSOConfiguration:BaseEntity
  {
      #region Properties
        [DataMember]
        public int SSOTypeId { get; set; }

        [DataMember]
        public string UserName { get; set; }

        [DataMember]
        public int SiteId { get; set; }

        [DataMember]
        public string IpAddress { get; set; }

        [DataMember]
        public int Port { get; set; }

        [DataMember]
        public string ExcecutionPath { get; set; }

        [DataMember]
        public string KeyLocation { get; set; }

        [DataMember]
        public string CredFilePath { get; set; }

        [DataMember]
        public int ConnectionTimeOut { get; set; }

        [DataMember]
        public string ProfileName { get; set; }

        [DataMember]
        public string ARCOSOnlineUrl { get; set; }

        [DataMember]
        public string ARCOSWebAPIURL { get; set; }

        [DataMember]
        public string SharedKey { get; set; }

        [DataMember]
        public string ServiceType { get; set; }

        [DataMember]
        public string DBInstance { get; set; }

      #endregion
  }
}
