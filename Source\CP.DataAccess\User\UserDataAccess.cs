﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class UserDataAccess : BaseDataAccess, IUserDataAccess
    {
        #region Constructors

        public UserDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<User> CreateEntityBuilder<User>()
        {
            return (new UserBuilder()) as IEntityBuilder<User>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="User" /> User table.
        /// </summary>
        /// <param name="user">User</param>
        /// <returns>User</returns>
        /// <author>Kiran Ghadge</author>
        User IUserDataAccess.Add(User user)
        {
            try
            {
                const string sp = "User_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iLoginName", DbType.String, user.LoginName);
                    Database.AddInParameter(cmd, Dbstring+"iLoginPassword", DbType.String,
                        Helper.CryptographyHelper.Md5Encrypt(user.LoginPassword));
                    Database.AddInParameter(cmd, Dbstring+"iCompanyId", DbType.Int32, user.CompanyId);
                    Database.AddInParameter(cmd, Dbstring+"iRole", DbType.String, user.Role.ToString());

                    if (user.LastLoginDate == Convert.ToDateTime("1/1/0001 12:00:00 AM"))
                       Database.AddInParameter(cmd, Dbstring + "iLastLoginDate", DbType.DateTime, DBNull.Value);
                    else
                       Database.AddInParameter(cmd, Dbstring + "iLastLoginDate", DbType.DateTime, user.LastLoginDate);
                  //  Database.AddInParameter(cmd, Dbstring+"iLastLoginDate", DbType.DateTime, user.LastLoginDate);

                    Database.AddInParameter(cmd, Dbstring+"iLastLoginIP", DbType.String, user.LastLoginIP);
                    Database.AddInParameter(cmd, Dbstring+"iLastAlertId", DbType.Int32, user.LastAlertId);
                    Database.AddInParameter(cmd, Dbstring+"iLastPasswordChanged", DbType.DateTime, user.LastPasswordChanged);
                    Database.AddInParameter(cmd, Dbstring+"iIsReset", DbType.Int32, user.IsReset);
                    Database.AddInParameter(cmd, Dbstring+"iIsActive", DbType.Int32, user.IsActive);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectAllFlag", DbType.Int32, user.InfraObjectAllFlag);
                    Database.AddInParameter(cmd, Dbstring+"iLoginType", DbType.String, user.LoginType.ToString());
                    //Database.AddInParameter(cmd, Dbstring+"iApplicationAllFlag", DbType.Int32, user.ApplicationAllFlag);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, user.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iAccessrole", DbType.String, user.Accessrole);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        user = reader.Read() ? CreateEntityBuilder<User>().BuildEntity(reader, user) : null;
                    }

                    if (user == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("User already exists. Please specify another user.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this user.");
                                }
                        }
                    }

                    return user;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting User Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Update <see cref="User" />into User table.
        /// </summary>
        /// <param name="user">User</param>
        /// <returns>User</returns>
        /// <author>Kiran Ghadge</author>
        User IUserDataAccess.Update(User user)
        {
            try
            {
                const string sp = "User_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, user.Id);
                    Database.AddInParameter(cmd, Dbstring+"iLoginName", DbType.String, user.LoginName);
                    Database.AddInParameter(cmd, Dbstring+"iLoginPassword", DbType.String,
                        user.LoginPassword);
                    Database.AddInParameter(cmd, Dbstring+"iCompanyId", DbType.Int32, user.CompanyId);
                    Database.AddInParameter(cmd, Dbstring+"iRole", DbType.String, user.Role.ToString());
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectAllFlag", DbType.Int32, user.InfraObjectAllFlag);
                    //Database.AddInParameter(cmd, Dbstring+"iApplicationAllFlag", DbType.Int32, user.ApplicationAllFlag);
                    Database.AddInParameter(cmd, Dbstring+"iLoginType", DbType.String, user.LoginType.ToString());
                    Database.AddInParameter(cmd, Dbstring+"iUpdatorId", DbType.Int32, user.UpdatorId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        user = reader.Read() ? CreateEntityBuilder<User>().BuildEntity(reader, user) : null;
                    }

                    if (user == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("User already exists. Please specify another user.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while updating this user.");
                                }
                        }
                    }

                    return user;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating User Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        /// <summary>
        ///     Update <see cref="User" /> into User table by id and alertId.
        /// </summary>
        /// <param name="id">ID of the User</param>
        /// <param name="alertId">AlertId of the User</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IUserDataAccess.UpdateByAlertId(int id, int alertId)
        {
            try
            {
                const string sp = "User_UpdateByAlertId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring+"ialertId", DbType.Int32, alertId);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While updating User by aler id : " + id, ex);
            }
        }

        bool IUserDataAccess.UpdateByIncidentId(int id, int incidentId)
        {
            try
            {
                const string sp = "User_UpdateByIncidentId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring+"iincidentId", DbType.Int32, incidentId);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While updating User by aler id : " + id, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="User" /> From User table by id.
        /// </summary>
        /// <param name="id">ID of the User</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        User IUserDataAccess.GetById(int id)
        {
            try
            {
                const string sp = "User_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<User>()).BuildEntity(reader, new User()) : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="User" /> From User table by name.
        /// </summary>
        /// <param name="name">Name of the User</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        User IUserDataAccess.GetByName(string name)
        {
            try
            {
                const string sp = "User_GetByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.AnsiString, name);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<User>()).BuildEntity(reader, new User()) : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetByName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
        bool IUserDataAccess.UpdateSessionIdByID(int id, string sessionId)
        {
            try
            {
                const string sp = "USER_UpdateSessionIdById";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iSessionId", DbType.AnsiString, sessionId);
                    int returnValue = Database.ExecuteNonQuery(cmd);
                    if (returnValue < 0)
                        return true;
                    else
                        return false;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While update user Session" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        /// <summary>
        ///     Get <see cref="User" /> From User table by Role.
        /// </summary>
        /// <param name="name">Name of the User</param>
        /// <returns>bool</returns>
        /// <author>Meenakshi Patil</author>
        User IUserDataAccess.UpdateRoleById(int id, string role)
        {
            try
            {
                const string sp = "User_GetByRole";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iRole", DbType.AnsiString, role);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<User>()).BuildEntity(reader, new User()) : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetByRole(" + role + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Lock <see cref="User" /> User table by Loginname.
        /// </summary>
        /// <param name="loginName">LoginName of the User</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IUserDataAccess.LockAccount(string loginName)
        {
            try
            {
                const string sp = "User_LockAccount";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iLoginName", DbType.AnsiString, loginName);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetByName(" + loginName + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        bool IUserDataAccess.LockAccountById(int iId)
        {
            try
            {
                const string sp = "User_LockAccountById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.AnsiString, iId);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetByName(" + iId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        bool IUserDataAccess.UnLockUserAccount(int id)
        {
            try
            {
                const string sp = "User_UnLockAccount";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetByName(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     GetAll <see cref="User" />From User table .
        /// </summary>
        /// <returns>User List</returns>
        /// <author>Kiran Ghadge</author>
        IList<User> IUserDataAccess.GetAll()
        {
            try
            {
                IList<User> usersProfiles = new List<User>();

                const string sp = "User_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var userProfile = new User
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),

                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]),

                                CompanyId = Convert.IsDBNull(reader["CompanyId"]) ? 0 : Convert.ToInt32(reader["CompanyId"]),

                               LoginName = Convert.IsDBNull(reader["LoginName"])
                ? string.Empty
                : Convert.ToString(reader["LoginName"]),
                                Role = Convert.IsDBNull(reader["Role"])
                              ? UserRole.Undefined
                              : (UserRole)Enum.Parse(typeof(UserRole), Convert.ToString(reader["Role"]), true),
                                IsActive = Convert.IsDBNull(reader["IsActive"])
                                    ? 0
                                    : Convert.ToInt32(reader["IsActive"]),
                                UserInformation =
                                {
                                    Email = Convert.IsDBNull(reader["Email"]) ? string.Empty : Convert.ToString(reader["Email"]),
                                    Mobile = Convert.IsDBNull(reader["Mobile"]) ? string.Empty : Convert.ToString(reader["Mobile"]),
                                    AlertMode = Convert.IsDBNull(reader["AlertMode"])
                ? AlertModeType.Undefined
                : (AlertModeType)Enum.Parse(typeof(AlertModeType), Convert.ToString(reader["AlertMode"]), true)
                                },
                                UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]),

                                UserId = Convert.IsDBNull(reader["UserId"]) ? 0 : Convert.ToInt32(reader["UserId"]),

                                LoginType =  Convert.IsDBNull(reader["LoginType"]) ? LoginType.Undefined : (LoginType)Enum.Parse(typeof(LoginType), Convert.ToString(reader["LoginType"]), true)
                                  
                            };

                            usersProfiles.Add(userProfile);
                        }
                    }
                }

                return usersProfiles;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="User" /> From User table by Id.
        /// </summary>
        /// <param name="id">Id of the User</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IUserDataAccess.DeleteById(int id)
        {
            try
            {
                const string sp = "User_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting User Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     check <see cref="User" /> From User table by Name.
        /// </summary>
        /// <param name="name">Name of the User</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IUserDataAccess.IsExistByName(string name)
        {
            try
            {
                const string sp = "User_IsExistByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.AnsiString, name);

                    //#if ORACLE
                    //                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
                    //#endif

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this companyProfile.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.IsExistByName (" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Changepassword <see cref="User" /> into User table.
        /// </summary>
        /// <param name="user">User</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IUserDataAccess.ChangePassword(User user)
        {
            try
            {
                const string sp = "User_ChangePassword";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iLoginName", DbType.AnsiString, user.LoginName);
                    Database.AddInParameter(cmd, Dbstring+"iPassword", DbType.AnsiString, user.LoginPassword);

                    Database.ExecuteNonQuery(cmd);
                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeSuccessAdd:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeFailOperation:
                            {
                                return false;
                            }
                        default:
                            {
                                return false;
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While User ChangePassword by aler name : " + user.LoginName, ex);
            }
        }

        /// <summary>
        ///     Reset Pasword <see cref="User" /> into User table.
        /// </summary>
        /// <param name="id">ID of the User</param>
        /// <param name="password">Password of the User</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IUserDataAccess.ResetPasswordById(int id, string password)
        {
            try
            {
                const string sp = "User_ResetPassword";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iPassword", DbType.AnsiString, password);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    int returCode = Database.ExecuteNonQuery(cmd);
                    return returCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While User ResetPassword by aler id : " + id, ex);
            }
        }

        /// <summary>
        ///     Update <see cref="User" /> into User table.
        /// </summary>
        /// <param name="user">User</param>
        /// <returns>User</returns>
        /// <author>Kiran Ghadge</author>
        User IUserDataAccess.UpdateByLogin(User user)
        {
            try
            {
                const string sp = "User_UpdateByLogin";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, user.Id);
                    Database.AddInParameter(cmd, Dbstring+"iLoginIP", DbType.AnsiString, user.CurrentLoginIP);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        user = reader.Read() ? CreateEntityBuilder<User>().BuildEntity(reader, new User()) : null;
                    }
                    return user;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                           ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                           "Error In DAL While updating User by aler id : " + user.Id, ex);
            }
        }

        IList<User> IUserDataAccess.GetByCompanyId(int companyId, bool isParent)
        {
            try
            {
                IList<User> usersProfiles = new List<User>();

                const string sp = "User_GetByCompanyId";
               // const string sp = "User_GetByCompId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iCompanyId", DbType.AnsiString, companyId);
                    Database.AddInParameter(cmd, Dbstring+"iIsParent", DbType.Int32, isParent);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //return CreateEntityBuilder<User>().BuildEntities(reader);

                        while (reader.Read())
                        {
                            var userProfile = new User
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                LoginName = Convert.IsDBNull(reader["LoginName"])
                ? string.Empty
                : Convert.ToString(reader["LoginName"]),

                                CompanyId = Convert.IsDBNull(reader["CompanyId"]) ? 0 : Convert.ToInt32(reader["CompanyId"]),

                                Role = Convert.IsDBNull(reader["Role"])
                ? UserRole.Undefined
                : (UserRole)Enum.Parse(typeof(UserRole), Convert.ToString(reader["Role"]), true),
                                IsActive = Convert.IsDBNull(reader["IsActive"])
                                    ? 0
                                    : Convert.ToInt32(reader["IsActive"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"])
                        ? 0
                        : Convert.ToInt32(reader["CreatorId"]),
                                UserInformation =
                                {
                                    Email = Convert.IsDBNull(reader["Email"]) ? string.Empty : Convert.ToString(reader["Email"]),
                                    Mobile = Convert.IsDBNull(reader["Mobile"]) ? string.Empty : Convert.ToString(reader["Mobile"]),
                                    AlertMode = Convert.IsDBNull(reader["AlertMode"])
                ? AlertModeType.Undefined
                : (AlertModeType)Enum.Parse(typeof(AlertModeType), Convert.ToString(reader["AlertMode"]), true)
                                },
                                LoginType = Convert.IsDBNull(reader["LoginType"]) ? LoginType.Undefined : (LoginType)Enum.Parse(typeof(LoginType), Convert.ToString(reader["LoginType"]), true)
                                
                            };

                            usersProfiles.Add(userProfile);
                        }
                    }
                }
                return usersProfiles;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetSitesByCompanyId()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
        IList<User> IUserDataAccess.GetByCompId(int companyId, bool isParent)
        {
            try
            {
                IList<User> usersProfiles = new List<User>();

                 const string sp = "User_GetByCompId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.AnsiString, companyId);
                    Database.AddInParameter(cmd, Dbstring + "iIsParent", DbType.Int32, isParent);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //return CreateEntityBuilder<User>().BuildEntities(reader);

                        while (reader.Read())
                        {
                            var userProfile = new User
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                LoginName = Convert.IsDBNull(reader["LoginName"])
                ? string.Empty
                : Convert.ToString(reader["LoginName"]),

                                CompanyId = Convert.IsDBNull(reader["CompanyId"]) ? 0 : Convert.ToInt32(reader["CompanyId"]),

                                Role = Convert.IsDBNull(reader["Role"])
                ? UserRole.Undefined
                : (UserRole)Enum.Parse(typeof(UserRole), Convert.ToString(reader["Role"]), true),
                                IsActive = Convert.IsDBNull(reader["IsActive"])
                                    ? 0
                                    : Convert.ToInt32(reader["IsActive"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"])
                        ? 0
                        : Convert.ToInt32(reader["CreatorId"]),
                                UserInformation =
                                {
                                  //  Email = Convert.IsDBNull(reader["Email"]) ? string.Empty : Convert.ToString(reader["Email"]),
                                    Mobile = Convert.IsDBNull(reader["Mobile"]) ? string.Empty : Convert.ToString(reader["Mobile"]),
                                    AlertMode = Convert.IsDBNull(reader["AlertMode"])
                ? AlertModeType.Undefined
                : (AlertModeType)Enum.Parse(typeof(AlertModeType), Convert.ToString(reader["AlertMode"]), true)
                                },
                                LoginType = Convert.IsDBNull(reader["LoginType"]) ? LoginType.Undefined : (LoginType)Enum.Parse(typeof(LoginType), Convert.ToString(reader["LoginType"]), true)

                            };

                            usersProfiles.Add(userProfile);
                        }
                    }
                }
                return usersProfiles;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetSitesByCompanyId()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
        IList<User> IUserDataAccess.GetByLoginId(int iId)
        {
            try
            {
               
                const string sp = "User_GetLoginId";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, iId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<User>().BuildEntities(reader);
                    }
                }
                //return usersProfiles;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetByLoginId()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        IList<User> IUserDataAccess.GetCompanyId(int companyId)
        {
            try
            {
                if (companyId < 1)
                {
                    throw new ArgumentNullException("companyId");
                }
                const string sp = "User_GetCompanyId";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iCompanyId", DbType.Int32, companyId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<User>().BuildEntities(reader);
                    }
                }
                //return usersProfiles;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetSitesByCompanyId()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="User" /> From User table.
        /// </summary>
        /// <param name="user">User</param>
        /// <returns>User</returns>
        /// <author>Kiran Ghadge</author>
        User IUserDataAccess.GetByLogin(User user)
        {
            try
            {
                const string sp = "User_GetByLogin";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iloginName", DbType.AnsiString, user.LoginName);
                    Database.AddInParameter(cmd, Dbstring+"iloginPassword", DbType.AnsiString, user.LoginPassword);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? CreateEntityBuilder<User>().BuildEntity(reader, user) : null;
                    }
                }
            }
            catch (Exception exe)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature User.GetByLogin)", exe);
            }
        }

        /// <summary>
        ///     GetPassword <see cref="User" /> From User table.
        /// </summary>
        /// <param name="loginName">LoginName of the User</param>
        /// <returns>User List</returns>
        /// <author>Kiran Ghadge</author>
        IList<string> IUserDataAccess.GetLastFivePassword(string loginName)
        {
            try
            {
                var passwordList = new List<string>();

                const string sp = "USERPASSW_GETLASTFIVEPASSWORD";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iLoginName", DbType.AnsiString, loginName);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("USERPASSWORDCur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            passwordList.Add(reader[0].ToString());
                        }
                    }
                }

                return passwordList;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetLastFivePassword(" + loginName +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<User> IUserDataAccess.GetAlusr()
        {
            try
            {
                const string sp = "UserGetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<User>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetAlusr" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        User IUserDataAccess.GetByCrtrId(int creatorid)
        {
            try
            {
                if (creatorid < 1)
                {
                    throw new ArgumentNullException("creatorid");
                }

                const string sp = "Users_GetLoginNameByCrtrId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"icreatorid", DbType.Int32, creatorid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<User>()).BuildEntity(reader, new User()) : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByCrtrId(" + creatorid + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        User IUserDataAccess.GetByDROPId(int droperationid)
        {
            try
            {
                const string sp = "UserName_GetByDROperationId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iDroperationId", DbType.Int32, droperationid); 
                                                           
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<User>()).BuildEntity(reader, new User()) : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetByDROPId(" + droperationid + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        User IUserDataAccess.GetByLoginandLoginType(User user)
        {
            try
            {
                const string sp = "User_GetByLoginAndLoginType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iloginName", DbType.AnsiString, user.LoginName);
                    Database.AddInParameter(cmd, Dbstring+"iloginPassword", DbType.AnsiString, user.LoginPassword);
                    Database.AddInParameter(cmd, Dbstring+"iloginType", DbType.AnsiString, user.LoginType);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? CreateEntityBuilder<User>().BuildEntity(reader, user) : null;
                    }
                }
            }
            catch (Exception exe)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature User.GetByLoginandLoginType)", exe);
            }
        }

        User IUserDataAccess.GetByNameAndType(string name, string type)
        {
            try
            {
                const string sp = "User_GetByNameAndType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);
                    Database.AddInParameter(cmd, Dbstring + "iLoginType", DbType.AnsiString, type);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<User>()).BuildEntity(reader, new User()) : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetByNameAndType(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<User> IUserDataAccess.GetSuperUserId(int id)
        {
            try
            {
                IList<User> usersProfiles = new List<User>();

                const string sp = "Users_GetSuperAdmin";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    //  Database.AddInParameter(cmd, Dbstring + "iIsParent", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //return CreateEntityBuilder<User>().BuildEntities(reader);

                        while (reader.Read())
                        {
                            var userProfile = new User
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                LoginName = Convert.IsDBNull(reader["LoginName"])
                ? string.Empty
                : Convert.ToString(reader["LoginName"]),
                                Role = Convert.IsDBNull(reader["Role"])
                ? UserRole.Undefined
                : (UserRole)Enum.Parse(typeof(UserRole), Convert.ToString(reader["Role"]), true),
                                IsActive = Convert.IsDBNull(reader["IsActive"])
                                    ? 0
                                    : Convert.ToInt32(reader["IsActive"]),
                                UserInformation =
                                {
                                    Email = Convert.IsDBNull(reader["Email"]) ? string.Empty : Convert.ToString(reader["Email"]),
                                }
                            };

                            usersProfiles.Add(userProfile);
                        }
                    }
                }
                return usersProfiles;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetSitesByCompanyId()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        #endregion Methods
    }
}