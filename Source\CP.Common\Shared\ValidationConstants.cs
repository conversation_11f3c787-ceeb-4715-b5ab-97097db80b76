﻿using System;
using System.Web.UI.WebControls;

namespace CP.Common.Shared
{
    public sealed partial class Constants
    {
        public static class ValidationConstants
        {
            public static class ErrorMessage
            {
                public const string CommonReq = "Required";
                public const string CommonInvalidNumber = "Invalid numeric value";
                public const string CommonInvalidEmailaddress = "Invalid email address";
                public const string CommonInvalidPhonenumber = "Invalid phone number";
                public const string CommonInvalidMobilenumber = "Invalid mobile number";
                public const string CommonInvalidDecimal = "Invalid decimal value";
                public const string CommonCompare = "Min value should be less than or equal to max value";
                public const string ContactNumber = "Please input at least one contact number.";
                public const string LoginFailed = "The username or password you entered is incorrect.";
                public const string PasswordCompare = "Password does not match!";

                public const string InvalidDelete =
                    "Some users are currently logged on to this company. Deletion not allowed.";

                public static string GetMessage(Type controlType, string itemName)
                {
                    if (controlType == typeof(TextBox))
                    {
                        return "Please input " + itemName;
                    }
                    if (controlType == typeof(DropDownList) || controlType == typeof(ListBox) ||
                        controlType == typeof(CheckBoxList))
                    {
                        return "Please select " + itemName;
                    }
                    if (controlType == typeof(RadioButtonList))
                    {
                        return "Please choose " + itemName;
                    }
                    return itemName + " required.";
                }

                public static string GetCompareMessage(string firstItemName, string secondItemName,
                    ValidationCompareOperator compareType)
                {
                    string compareTypeString = string.Empty;
                    if (compareType == ValidationCompareOperator.Equal)
                    {
                        compareTypeString = " equal to ";
                    }
                    else if (compareType == ValidationCompareOperator.GreaterThan)
                    {
                        compareTypeString = " greater than ";
                    }
                    else if (compareType == ValidationCompareOperator.GreaterThanEqual)
                    {
                        compareTypeString = " greater than or equal to ";
                    }
                    else if (compareType == ValidationCompareOperator.LessThan)
                    {
                        compareTypeString = " less than ";
                    }
                    else if (compareType == ValidationCompareOperator.LessThanEqual)
                    {
                        compareTypeString = " less than or equal to ";
                    }
                    else if (compareType == ValidationCompareOperator.NotEqual)
                    {
                        compareTypeString = " not equal to ";
                    }
                    return firstItemName + " should not " + compareTypeString + secondItemName;
                }
            }

            public static class RegularExpression
            {
                public const string Email = @"\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*";

                /// <summary>
                ///     alphabetic letters only
                /// </summary>
                public const string Web =
                    @"^((ht|f)tp(s?)\:\/\/|~/|/)?([\w]+:\w+@)?([a-zA-Z]{1}([\w\-]+\.)+([\w]{2,5}))(:[\d]{1,5})?((/?\w+/)+|/?)(\w+\.[\w]{3,4})?((\?\w+=\w+)?(&\w+=\w+)*)?";

                /// <summary>
                ///     alphabetic letters only
                /// </summary>
                public const string Name = @"^[A-Z a-z \. \-]*$";

                /// <summary>
                ///     use numbers,(),+ and -
                /// </summary>
                public const string Phone = @"^[+]?([0-9 ( ) \s \-]{6,19})([^-+ a-zA-Z/(/)])$";

                /// <summary>
                ///     2-4 digits
                /// </summary>
                public const string PhoneExtension = @"^[0-9]{2,4}$";

                /// <summary>
                ///     5-10 digits
                /// </summary>
                public const string Mobile = @"^[+]?([0-9 ( ) \s \-]{6,19})([^-+ a-zA-Z/(/)])$";

                /// <summary>
                /// </summary>
                public const string PostCode = @"^[A-Z a-z 0-9]{1,1}([A-Z a-z 0-9 \-]{3,9})";

                /// <summary>
                ///     4-10 digits
                /// </summary>
                public const string Experience = "[0-9]{1,2}((.1[0-1])|(.[1-9]))?"; //@"^[0-9'.]{1,3}$";

                /// <summary>
                ///     use numbers,(),+ and -
                /// </summary>
                public const string FaxNo = @"^[+]?([0-9 ( ) \s \-]{6,19})([^-+ a-zA-Z/(/)])$";

                /// <summary>
                ///     numbers
                /// </summary>
                public const string EinNumber = @"([0-9]*)";

                /// <summary>
                ///     numbers
                /// </summary>
                public const string Photo =
                    @"(.*\.[jJ][pP][gG])|(.*\.[jJ][pP][eE][gG])|(.*\.[gG][iI][fF])|(.*\.[pP][nN][gG])";

                /// <summary>
                ///     File
                /// </summary>
                public const string File =
                    @"(.*\.[dD][oO][cC][xX])|(.*\.[dD][oO][cC])|(.*\.[rR][tT][fF])|(.*\.[tT][xX][tT])|(.*\.[zZ][iI][pP])|(.*\.[pP][dD][fF])";

                /// <summary>
                ///     decimal numbers
                /// </summary>
                public const string Salary = @"([0-9]+\.[0-9]+|[0-9]+)";

                /// <summary>
                ///     decimal numbers
                /// </summary>
                public const string GPA = @"^(\d+(\.\d{1,2}))|([0-9]{1,5})$";

                /// <summary>
                ///     decimal numbers
                /// </summary>
                public const string Number = @"^[0-9]{1,2}";

                /// <summary>
                ///     decimal numbers/integer with 2 digits and or 2 decimal
                /// </summary>
                public const string Decimal = @"([0-9]{0,2}[.][0-9]{1,2}|[0-9]{0,2})";
            }

            public static class SuccessMessage
            {
                public static string GetSingleDataOperationMessage(string itemName, TransactionType transactionType)
                {
                    string operation = string.Empty;
                    if (transactionType == TransactionType.Add)
                    {
                        operation = "added";
                    }
                    else if (transactionType == TransactionType.Update)
                    {
                        operation = "updated";
                    }
                    else if (transactionType == TransactionType.Delete)
                    {
                        operation = "deleted";
                    }
                    else if (transactionType == TransactionType.Save)
                    {
                        operation = "saved";
                    }
                    else if (transactionType == TransactionType.InActive)
                    {
                        operation = "Inactivated";
                    }
                    else if (transactionType == TransactionType.Deactive)
                    {
                        operation = "de-activated";
                    }
                    else if (transactionType == TransactionType.Execute)
                    {
                        operation = "executed";
                    }
                    else if (transactionType == TransactionType.Active)
                    {
                        operation = "activated";
                    }
                    else if (transactionType == TransactionType.Exists)
                    {
                        return itemName;
                    }
                    return itemName + " has been successfully " + operation;
                }

                public static string GetMultipleDataOperationMessage(string itemName, int countSelected, int countAdded,
                    TransactionType transactionType, ref bool isError)
                {
                    string operation = string.Empty;
                    switch (transactionType)
                    {
                        case TransactionType.Add:
                            operation = "add";
                            break;

                        case TransactionType.Update:
                            operation = "update";
                            break;

                        case TransactionType.Delete:
                            operation = "delete";
                            break;

                        case TransactionType.Save:
                            operation = "save";
                            break;
                    }
                    switch (countSelected)
                    {
                        case 0:
                            isError = true;
                            return "Please select at least one " + itemName + " to be " + operation + "ed.";

                        default:
                            if (countAdded == 0)
                            {
                                isError = true;
                                return "The selected " + itemName + "(s) are already in the list.";
                            }
                            if (countAdded == countSelected)
                            {
                                isError = false;
                                return "The selected " + itemName + "(s) are " + operation + "ed successfully.";
                            }
                            if (countAdded > 0 && countAdded < countSelected)
                            {
                                isError = false;
                                return countAdded + " of " + countSelected + " selected " + itemName + "(s) are " +
                                       operation + "ed successfully.";
                            }
                            return string.Empty;
                    }
                }
            }
        }
    }
}