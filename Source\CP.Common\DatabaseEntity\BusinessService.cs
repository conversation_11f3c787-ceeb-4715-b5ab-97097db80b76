﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "BusinessService", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class BusinessService : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public bool DRReadyness { get; set; }

        [DataMember]
        public int CompanyId { get; set; }

        [DataMember]
        public int SiteId { get; set; }

        [DataMember]
        public string ConfiguredRPO { get; set; }

        [DataMember]
        public string ConfiguredRTO { get; set; }

        [DataMember]
        public string ConfiguredMAO { get; set; }

        [DataMember]
        public string Availabilty { get; set; }

        [DataMember]
        public string Health { get; set; }

        [DataMember]
        public int Priority { get; set; }

        [DataMember]
        public int Up { get; set; }

        [DataMember]
        public int Down { get; set; }

        [DataMember]
        public int Maintenance { get; set; }

        [DataMember]
        public int InfraObjectCount { get; set; }

        //
        [DataMember]
        public string Location { get; set; }

        [DataMember]
        public string CompanyName { get; set; }

        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public string SiteType { get; set; }

        #endregion Properties
    }
}