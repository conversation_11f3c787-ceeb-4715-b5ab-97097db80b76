﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.DatabaseSqlplus
{
    internal sealed class DatabaseSqlPlusDataAccess : BaseDataAccess, IDatabaseSqlplusDataAccess
    {
        #region Constructors

        public DatabaseSqlPlusDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<DatabaseSqlPlus> CreateEntityBuilder<DatabaseSqlPlus>()
        {
            return (new DatabaseSqlPlusBuilder()) as IEntityBuilder<DatabaseSqlPlus>;
        }

        #endregion Constructors

        IList<DatabaseSqlPlus> IDatabaseSqlplusDataAccess.GetAllDatabaseSqlplus()
        {
            try
            {
                const string sp = "DATABASESQLPLUS_GETALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_actionset"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DatabaseSqlPlus>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseOracleDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        DatabaseSqlPlus IDatabaseSqlplusDataAccess.GetSqlName(string name)
        {
            try
            {
                const string sp = "DatabaseAuth_GetBySqlName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<DatabaseSqlPlus>()).BuildEntity(reader, new DatabaseSqlPlus()) : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserDataAccess.GetByName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
    }
}
