﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess.ActivDirectory
{
    internal sealed class ActiveDirectoryDataAccess : BaseDataAccess, IActiveDirectoryDataAccess
    {
        #region Constructors

        public ActiveDirectoryDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ActiveDirectory> CreateEntityBuilder<ActiveDirectory>()
        {
            return (new ActiveDirectoryBuilder()) as IEntityBuilder<ActiveDirectory>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>

        ActiveDirectory IActiveDirectoryDataAccess.Add(ActiveDirectory activeDirectory)
        {
            try
            {
                const string sp = "ActiveDirectory_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iReplicationId", DbType.Int32, activeDirectory.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iTargetName", DbType.String, activeDirectory.TargetName);
                    Database.AddInParameter(cmd, Dbstring + "iScopeName", DbType.String, activeDirectory.ScopeName);
                    Database.AddInParameter(cmd, Dbstring + "iCREATEDATE", DbType.DateTime, activeDirectory.CREATEDATE);
                    

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        activeDirectory = reader.Read() ? CreateEntityBuilder<ActiveDirectory>().BuildEntity(reader, activeDirectory) : null;
                    }
                    return activeDirectory;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting ActiveDirectory Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        
        ActiveDirectory IActiveDirectoryDataAccess.Update(ActiveDirectory activeDirectory)
        {
            try
            {
                const string sp = "ActiveDirectory_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, activeDirectory.Id);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationId", DbType.Int32, activeDirectory.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iTargetName", DbType.String, activeDirectory.TargetName);
                    Database.AddInParameter(cmd, Dbstring + "iScopeName", DbType.String, activeDirectory.ScopeName);
                    Database.AddInParameter(cmd, Dbstring + "iCREATEDATE", DbType.DateTime, activeDirectory.CREATEDATE);
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        activeDirectory = reader.Read() ? CreateEntityBuilder<ActiveDirectory>().BuildEntity(reader, activeDirectory) : null;
                    }
                    return activeDirectory;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating ActiveDirectory Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }
       

        

        ActiveDirectory IActiveDirectoryDataAccess.GetById(int id)
        {
            try
            {
                const string sp = "ActiveDirectory_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ActiveDirectory>()).BuildEntity(reader, new ActiveDirectory());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IActiveDirectoryDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        ActiveDirectory IActiveDirectoryDataAccess.GetByReplicationId(int id)
        {
            try
            {
               // const string sp = "ActiveDirectory_GetByReplicationId";//
                const string sp = "ActiveDirectory_GetByRepId";



                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ActiveDirectory>()).BuildEntity(reader, new ActiveDirectory());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IActiveDirectoryDataAccess.GetByReplicationId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>

        IList<ActiveDirectory> IActiveDirectoryDataAccess.GetAll(string replicationtype)
        {
            try
            {
                const string sp = "ActiveDirectory_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iTypeId", DbType.AnsiString, replicationtype);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildActiveDirectoryEntities(reader, replicationtype);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IActiveDirectoryDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        IList<ActiveDirectory> IActiveDirectoryDataAccess.GetByCompanyId(int companyId, bool isParent, string replicationtype)
        {
            try
            {
                const string sp = "ActiveDirectory_GetByCompanyId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.Int32, companyId);
                    Database.AddInParameter(cmd, Dbstring + "iisParent", DbType.Int32, isParent);
                    Database.AddInParameter(cmd, "TypeId", DbType.String, replicationtype);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildActiveDirectoryEntities(reader, replicationtype);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IActiveDirectoryDataAccess.GetByCompanyId(" + companyId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }



        private IList<ActiveDirectory> BuildActiveDirectoryEntities(IDataReader reader, string replicationtype)
        {
            var activeDirectory = new List<ActiveDirectory>();

            while (reader.Read())
            {
                var type = Convert.ToString(reader["Type"]);
                if (Convert.ToString(reader["Type"]).Trim() == replicationtype)
                {
                    var emc = new ActiveDirectory
                    {
                        ReplicationBase =
                        {
                            Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                            Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                            // reptype = reader.IsDBNull(2) ? string.Empty : reader.GetString(2)\
                            Type = Convert.IsDBNull(reader["Type"])
                ? ReplicationType.Undefined
                : (ReplicationType)Enum.Parse(typeof(ReplicationType), Convert.ToString(reader["Type"]), true)
                        }
                    };

                    emc.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0
                : Convert.ToInt32(reader["ReplicationId"]);
                    emc.TargetName = Convert.IsDBNull(reader["TargetName"]) ? string.Empty : Convert.ToString(reader["TargetName"]);

                    emc.ScopeName = Convert.IsDBNull(reader["ScopeName"])
                ? string.Empty
                : Convert.ToString(reader["ScopeName"]);
                    activeDirectory.Add(emc);
                }
            }

            return (activeDirectory.Count > 0) ? activeDirectory : null;
        }

       
        #endregion Methods

    }
}

