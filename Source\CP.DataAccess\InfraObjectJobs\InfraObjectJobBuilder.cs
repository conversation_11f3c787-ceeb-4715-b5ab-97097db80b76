﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class InfraObjectJobBuilder : IEntityBuilder<InfraObjectJob>
    {
        IList<InfraObjectJob> IEntityBuilder<InfraObjectJob>.BuildEntities(IDataReader reader)
        {
            var infraObjectjobs = new List<InfraObjectJob>();

            while (reader.Read())
            {
                infraObjectjobs.Add(((IEntityBuilder<InfraObjectJob>)this).BuildEntity(reader, new InfraObjectJob()));
            }

            return (infraObjectjobs.Count > 0) ? infraObjectjobs : null;
        }

        InfraObjectJob IEntityBuilder<InfraObjectJob>.BuildEntity(IDataReader reader, InfraObjectJob infraObjectjob)
        {
            infraObjectjob.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["ID"]);
            infraObjectjob.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            infraObjectjob.BusinessServiceId = Convert.IsDBNull(reader["BusinessServiceId"])
                ? 0
                : Convert.ToInt32(reader["BusinessServiceId"]);
            infraObjectjob.StorageImageId = Convert.IsDBNull(reader["StorageImageId"])
                ? string.Empty
                : Convert.ToString(reader["StorageImageId"]);
            infraObjectjob.JobId = Convert.IsDBNull(reader["JobId"]) ? 0 : Convert.ToInt32(reader["JobId"]);
            infraObjectjob.TriggerName = Convert.IsDBNull(reader["TriggerName"])
                ? string.Empty
                : Convert.ToString(reader["TriggerName"]);
            infraObjectjob.CronExpression = Convert.IsDBNull(reader["CronExpression"])
                ? string.Empty
                : Convert.ToString(reader["CronExpression"]);
            infraObjectjob.CronTime = Convert.IsDBNull(reader["CronTime"])
                ? string.Empty
                : Convert.ToString(reader["CronTime"]);
            infraObjectjob.NextFireTime = Convert.IsDBNull(reader["NextFireTime"])
                ? string.Empty
                : Convert.ToString(reader["NextFireTime"]);
            infraObjectjob.LastFireTime = Convert.IsDBNull(reader["LastFireTime"])
                ? string.Empty
                : Convert.ToString(reader["LastFireTime"]);
            infraObjectjob.JobStatus = Convert.IsDBNull(reader["JobStatus"])
                ? string.Empty
                : Convert.ToString(reader["JobStatus"]);
            infraObjectjob.IsEnabled = Convert.IsDBNull(reader["IsEnabled"]) ? 0 : Convert.ToInt32(reader["IsEnabled"]);
            infraObjectjob.InfraObjType = Convert.IsDBNull(reader["InfraObjType"]) ? 0 : Convert.ToInt32(reader["InfraObjType"]);
            infraObjectjob.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            infraObjectjob.CronExpressionChange = Convert.IsDBNull(reader["CronExpressionChange"]) ? 0 : Convert.ToInt32(reader["CronExpressionChange"]);
            infraObjectjob.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            infraObjectjob.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);
            infraObjectjob.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            infraObjectjob.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"]);
            
            return infraObjectjob;
        }
    }
}