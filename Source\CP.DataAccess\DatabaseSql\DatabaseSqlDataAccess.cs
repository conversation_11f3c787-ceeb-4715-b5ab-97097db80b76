﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class DatabaseSqlDataAccess : BaseDataAccess, IDatabaseSqlDataAccess
    {
        #region Constructors

        public DatabaseSqlDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<DatabaseSql> CreateEntityBuilder<DatabaseSql>()
        {
            return (new DatabaseSqlBuilder()) as IEntityBuilder<DatabaseSql>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="DatabaseSql" /> into bcms_database_sql Table
        /// </summary>
        /// <param name="databaseSql">databaseSql</param>
        /// <returns>DatabaseSql</returns>
        /// <author>Ranjith Singh</author>
        DatabaseSql IDatabaseSqlDataAccess.Add(DatabaseSql databaseSql)
        {
            try
            {
                const string sp = "DatabaseSql_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iBaseDatabaseId", DbType.Int32, databaseSql.BaseDatabaseId);
                    Database.AddInParameter(cmd, Dbstring+"iDatabaseSID", DbType.String, databaseSql.DatabaseSID);
                    Database.AddInParameter(cmd, Dbstring+"iUserName", DbType.String, databaseSql.UserName);
                    Database.AddInParameter(cmd, Dbstring+"iPassword", DbType.String, databaseSql.Password);
                    Database.AddInParameter(cmd, Dbstring+"iPort", DbType.Int32, databaseSql.Port);
                    Database.AddInParameter(cmd, Dbstring+"iAuthenticationMode", DbType.String, databaseSql.AuthenticationMode);
                    Database.AddInParameter(cmd, Dbstring + "iInstanceName", DbType.String, databaseSql.InstanceName);
                    Database.AddInParameter(cmd, Dbstring+"iDataFilePath", DbType.String, databaseSql.DataFilePath);
                    Database.AddInParameter(cmd, Dbstring+"iTransLogPath", DbType.String, databaseSql.TransLogPath);
                    Database.AddInParameter(cmd, Dbstring+"iUndoFilePath", DbType.String, databaseSql.UndoFilePath);
                    Database.AddInParameter(cmd, Dbstring+"iBackupRestorePath", DbType.String, databaseSql.BackupRestorePath);
                    Database.AddInParameter(cmd, Dbstring+"iNetworksharedPath", DbType.String, databaseSql.NetworkSharedPath);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databaseSql = reader.Read()
                            ? CreateEntityBuilder<DatabaseSql>().BuildEntity(reader, databaseSql)
                            : null;
                    }

                    if (databaseSql == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "DatabaseSql already exists. Please specify another bcms_database_sql.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this bcms_database_sql.");
                                }
                        }
                    }

                    return databaseSql;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting DatabaseSql Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Update <see cref="DatabaseSql" /> into bcms_database_sql Table
        /// </summary>
        /// <param name="databaseSql">databaseSql</param>
        /// <returns>DatabaseSql</returns>
        /// <author>Ranjith Singh</author>
        DatabaseSql IDatabaseSqlDataAccess.Update(DatabaseSql databaseSql)
        {
            try
            {
                const string sp = "DatabaseSql_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, databaseSql.Id);
                    Database.AddInParameter(cmd, Dbstring+"iDatabaseSID", DbType.String, databaseSql.DatabaseSID);
                    Database.AddInParameter(cmd, Dbstring+"iUserName", DbType.String, databaseSql.UserName);
                    Database.AddInParameter(cmd, Dbstring+"iPassword", DbType.String, databaseSql.Password);
                    Database.AddInParameter(cmd, Dbstring+"iPort", DbType.Int32, databaseSql.Port);
                    Database.AddInParameter(cmd, Dbstring+"iAuthenticationMode", DbType.String,
                        databaseSql.AuthenticationMode.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iInstanceName", DbType.String, databaseSql.InstanceName);
                    Database.AddInParameter(cmd, Dbstring+"iDataFilePath", DbType.String, databaseSql.DataFilePath);
                    Database.AddInParameter(cmd, Dbstring+"iTransLogPath", DbType.String, databaseSql.TransLogPath);
                    Database.AddInParameter(cmd, Dbstring+"iUndoFilePath", DbType.String, databaseSql.UndoFilePath);
                    Database.AddInParameter(cmd, Dbstring+"iBackupRestorePath", DbType.String, databaseSql.BackupRestorePath);
                    Database.AddInParameter(cmd, Dbstring+"iNetworksharedPath", DbType.String, databaseSql.NetworkSharedPath);
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databaseSql = reader.Read()
                            ? CreateEntityBuilder<DatabaseSql>().BuildEntity(reader, databaseSql)
                            : null;
                    }

                    if (databaseSql == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "DatabaseSql already exists. Please specify another bcms_database_sql.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this bcms_database_sql.");
                                }
                        }
                    }

                    return databaseSql;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating DatabaseSql Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        DatabaseSql IDatabaseSqlDataAccess.UpdateByDatabaseBaseId(DatabaseSql databaseSql)
        {
            try
            {
                const string sp = "DatabaseSql_UpdtByDtbasBsId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iDatabaseId", DbType.Int32, databaseSql.BaseDatabaseId);
                    Database.AddInParameter(cmd, Dbstring+"iDatabaseSID", DbType.String, databaseSql.DatabaseSID);
                    Database.AddInParameter(cmd, Dbstring+"iUserName", DbType.String, databaseSql.UserName);
                    Database.AddInParameter(cmd, Dbstring+"iPassword", DbType.String, databaseSql.Password);
                    Database.AddInParameter(cmd, Dbstring+"iPort", DbType.Int32, databaseSql.Port);
                    Database.AddInParameter(cmd, Dbstring+"iAuthenticationMode", DbType.String, databaseSql.AuthenticationMode.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iInstanceName", DbType.String, databaseSql.InstanceName);
                    Database.AddInParameter(cmd, Dbstring+"iDataFilePath", DbType.String, databaseSql.DataFilePath);
                    Database.AddInParameter(cmd, Dbstring+"iTransLogPath", DbType.String, databaseSql.TransLogPath);
                    Database.AddInParameter(cmd, Dbstring+"iUndoFilePath", DbType.String, databaseSql.UndoFilePath);
                    Database.AddInParameter(cmd, Dbstring+"iBackupRestorePath", DbType.String, databaseSql.BackupRestorePath);
                    Database.AddInParameter(cmd, Dbstring+"iNetworksharedPath", DbType.String, databaseSql.NetworkSharedPath);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databaseSql = reader.Read()
                            ? CreateEntityBuilder<DatabaseSql>().BuildEntity(reader, databaseSql)
                            : null;
                    }

                    if (databaseSql == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "DatabaseOracle already exists. Please specify another bcms_database_oracle.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this bcms_database_oracle.");
                                }
                        }
                    }

                    return databaseSql;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating By DatabaseBaseId DatabaseOracle Entry " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DatabaseSql" /> from bcms_database_sql Table by Id
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>DatabaseSql</returns>
        /// <author>Ranjith Singh</author>
        DatabaseSql IDatabaseSqlDataAccess.GetById(int id)
        {
            try
            {
                const string sp = "DatabaseSql_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<DatabaseSql>()).BuildEntity(reader, new DatabaseSql())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseSqlDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DatabaseSql" /> from bcms_database_exchange Table by Id
        /// </summary>
        /// <param name="databaseBaseId">id</param>
        /// <returns>DatabaseExchange</returns>
        /// <author>Martin</author>
        DatabaseSql IDatabaseSqlDataAccess.GetByDatabaseBaseId(int databaseBaseId)
        {
            try
            {
                const string sp = "DatabaseSql_ByDtabasBsId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iDatabaseBaseId", DbType.Int32, databaseBaseId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<DatabaseSql>()).BuildEntity(reader, new DatabaseSql())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseSqlDataAccess.GetByDatabaseBaseId(" +
                    databaseBaseId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DatabaseSql" /> from bcms_database_sql table.
        /// </summary>
        /// <returns>DatabaseSql IList</returns>
        /// <author>Ranjith Singh</author>
        IList<DatabaseSql> IDatabaseSqlDataAccess.GetAll()
        {
            try
            {
                const string sp = "DatabaseSql_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DatabaseSql>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseSqlDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="DatabaseSql" /> bcms_database_sql by Id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>bool</returns>
        /// <author>Ranjith Singh</author>
        bool IDatabaseSqlDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DatabaseSql_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeSuccessDelete:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeErrorChildExists:
                            {
                                throw new ArgumentException("Cannot delete a bcms_database_sql which has association.");
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this bcms_database_sql.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting DatabaseSql Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Check <see cref="CompanyProfile" /> bcms_database_sql Is exist by name.
        /// </summary>
        /// <param name="name">name</param>
        /// <returns>bool</returns>
        /// <author>Ranjith Singh</author>
        bool IDatabaseSqlDataAccess.IsExistByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "DatabaseSql_IsExistByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.AnsiString, name);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this companyProfile.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseSqlDataAccess.IsExistByName (" + name +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods
    }
}