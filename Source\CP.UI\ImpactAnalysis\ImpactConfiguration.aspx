﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ImpactConfiguration.aspx.cs" Inherits="CP.UI.ImpactConfiguration" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title runat="server" id="usefortitle"></title>
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/bootstrap.min.js"></script>
    <script src="../Script/bootstrap-select.js"></script>
    <script src="../Script/bootstrap-select.init.js"></script>
    <script>
        $(document).ready(function () {

            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();


            //$("#btnSave").live("click", function () {
            //    ("#rwCloseButton").trigger("click");
            //});

        });
        function pageLoad() {
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
        }
        function GetRadWindow() {
            var oWindow = null;
            if (window.radWindow) oWindow = window.radWindow;
            else if (window.frameElement.radWindow) oWindow = window.frameElement.radWindow;
            return oWindow;
        }
        function Close() {
            GetRadWindow().Close();
        }


    </script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js"></script>
    <script>
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
    <%-- <style>
        .chosen-select + .chosen-container {
            width: 10% !important;
            opacity: 1 !important;
        }
    </style>--%>

    <style>
        .width70 .chosen-container {
            width: 80% !important;
        }


        div#ddlParentdBF_chosen {
            width: 93% !important;
        }

        div#ddlParentImpactType_chosen {
            width: 82% !important;
        }

        div#ddlParentBusinessServiceBStoBS_chosen {
            width: 87% !important;
        }

        div#dllParentImpactTypeBStoBs_chosen {
            width: 64% !important;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <telerik:RadScriptManager runat="server" ID="RadScriptManager1" />

        <div class="innerLR margin-top">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-body">

                    <div class="row">
                        <asp:UpdatePanel ID="pnlUpDateImpact" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-xs-12 form-horizontal uniformjs">
                                    <asp:Panel ID="upFilter" runat="server" Visible="false">
                                        <div class="form-group">
                                            <label class="col-xs-2 control-label" style="width: 14%;">Buiness Service </label>
                                            <div class="col-xs-2 padding-none">
                                                <asp:DropDownList ID="ddlBusinessService" runat="server" CssClass="selectpicker col-xs-12 padding-none"
                                                    data-style="btn-default" OnSelectedIndexChanged="ddlBusinessService_SelectedIndexChanged" AutoPostBack="true">
                                                    <asp:ListItem Value="0">-Select Business Service-</asp:ListItem>
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="rfvBusinessServ_Fltr" runat="server" InitialValue="0"
                                                    ControlToValidate="ddlBusinessService" ErrorMessage="*" CssClass="error"
                                                    Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                            </div>

                                            <asp:Label ID="lblInfrFilter" Style="width: 14%;" class="col-xs-2 control-label" Text="Infra Object" runat="server"> </asp:Label>
                                            <div class="col-xs-2 padding-none">
                                                <asp:DropDownList ID="ddlInfraobject" runat="server" CssClass="selectpicker col-xs-12 padding-none"
                                                    data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ddlInfraobject_SelectedIndexChanged">
                                                    <asp:ListItem Value="0">-Select InfraObject-</asp:ListItem>
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="rfvInfraPbject_Fltr" runat="server" InitialValue="0"
                                                    ControlToValidate="ddlInfraobject" ErrorMessage="*" CssClass="error"
                                                    Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                            </div>
                                            <asp:Label ID="lblInfracomp" Style="width: 14%;" class="col-xs-2 control-label" Text="Infra Component" runat="server"> </asp:Label>
                                            <div class="col-xs-2 padding-none" style="width: 21%;">

                                                <asp:DropDownList ID="ddlinfraObjectComponent" runat="server" CssClass="chosen-select col-xs-12 padding-none"
                                                    data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlinfraObjectComponent_SelectedIndexChanged">
                                                    <asp:ListItem Value="0">-Select InfraObject Component-</asp:ListItem>
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" InitialValue="0"
                                                    ControlToValidate="ddlinfraObjectComponent" ErrorMessage="*" CssClass="pull-left margin-right control-label error"
                                                    Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                            </div>
                                            <div class="clearfix"></div>
                                            <%-- <label class="col-xs-2 control-label" style="width: 18%;">Apply Process/Service Rule</label>--%>
                                            <asp:Label Style="width: 14%;" class="col-xs-2 control-label" runat="server"></asp:Label>
                                            <div class="col-xs-2 padding-none" style="margin-top: 8px">
                                                <asp:CheckBox ID="Chkprocess" runat="server" AutoPostBack="true" OnCheckedChanged="Chkprocess_CheckedChanged" Text="Apply Process Rule" />
                                            </div>
                                            <%--<label class="col-xs-2 control-label" style="width: 18%;">Apply Queue Rule</label>--%>
                                            <asp:Label Style="width: 14%;" class="col-xs-2 control-label" runat="server"></asp:Label>
                                            <div class="col-xs-2 padding-none" style="margin-top: 8px">
                                                <asp:CheckBox ID="ChkQueue" runat="server" AutoPostBack="true" OnCheckedChanged="CheckBox1_CheckedChanged" Text="Apply Queue Rule" />
                                            </div>
                                        </div>
                                        <hr class="" />
                                    </asp:Panel>
                                    <asp:Panel ID="pnlInfraToBF" runat="server" Visible="false">
                                        <div class="form-group">
                                            <asp:Panel ID="lblProcess" runat="server" Visible="false">
                                                <label id="lblrulesubtype" runat="server" class="col-xs-2 control-label" style="width: 14%;"></label>
                                                <div class="col-xs-2 padding-none">

                                                    <asp:DropDownList ID="ddlAppliactionProcess" runat="server" CssClass="selectpicker col-xs-12 padding-none"
                                                        data-style="btn-default">
                                                        <asp:ListItem Value="0">-Select Process/Service-</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="rfvInfraPbject_AppProcess" runat="server" InitialValue="0"
                                                        ControlToValidate="ddlAppliactionProcess" ErrorMessage="*" CssClass="error"
                                                        Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                                </div>
                                            </asp:Panel>
                                            <label class="col-xs-2 control-label" style="width: 14%;">is not available then</label>

                                            <div class="col-xs-2 padding-none">
                                                <asp:DropDownList ID="ddlBusinessFunction" runat="server" CssClass="selectpicker col-xs-12 padding-none"
                                                    data-style="btn-default" OnSelectedIndexChanged="ddlBusinessFunction_SelectedIndexChanged" AutoPostBack="true">
                                                    <asp:ListItem Value="0">-Select Business Function-</asp:ListItem>
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="rfvBF_infraToBF" runat="server" InitialValue="0"
                                                    ControlToValidate="ddlBusinessFunction" ErrorMessage="*" CssClass="pull-left margin-right control-label error"
                                                    Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                            </div>
                                            <label class="col-xs-2 control-label" style="width: 14%;">will be</label>
                                            <div class="col-xs-2 padding-none" style="width: 20%;">
                                                <asp:DropDownList ID="ddlImpactTypeBF" runat="server" CssClass="selectpicker col-xs-8 padding-none"
                                                    data-style="btn-default">
                                                    <asp:ListItem Value="0">-Select Impact-</asp:ListItem>
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="rfvBFImpact_infraToBF" runat="server" InitialValue="0"
                                                    ControlToValidate="ddlImpactTypeBF" ErrorMessage="*" CssClass="pull-left margin-left control-label  error"
                                                    Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                                <label class="pull-left control-label margin-right">&nbsp;Then</label>

                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-xs-2 control-label" style="width: 14%;">Business Service </label>
                                            <div class="col-xs-2 padding-none">
                                                <asp:DropDownList ID="ddlBS_BFtoBS" runat="server" CssClass="selectpicker col-xs-12 padding-none"
                                                    data-style="btn-default">
                                                    <asp:ListItem Value="0">-Select Business Service-</asp:ListItem>
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="rfvBS_BFtoBS" runat="server" InitialValue="0"
                                                    ControlToValidate="ddlBS_BFtoBS" ErrorMessage="*" CssClass="pull-left margin-right control-label error"
                                                    Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                            </div>
                                            <label class="col-xs-2 control-label" style="width: 14%;">will be</label>
                                            <div class="col-xs-2 padding-none" style="width: 20%;">
                                                <asp:DropDownList ID="ddlBSImpactType_BFtoBS" runat="server" CssClass="selectpicker col-xs-8 padding-none"
                                                    data-style="btn-default">
                                                    <asp:ListItem Value="0">-Select Impact-</asp:ListItem>
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="rfvBSImpact_BFtoBS" runat="server" InitialValue="0"
                                                    ControlToValidate="ddlBSImpactType_BFtoBS" ErrorMessage="*" CssClass="pull-left margin-right control-label error"
                                                    Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                            </div>
                                        </div>
                                    </asp:Panel>

                                    <asp:Panel ID="pnlBFtoBF" runat="server" Visible="false">
                                        <div class="form-group">
                                            <label class="col-xs-2 control-label" style="width: 14%;">IF Business Function </label>
                                            <div class="col-xs-10 padding-none">

                                                <div class="col-md-3 width70" style="padding-left: 0;">
                                                    <asp:DropDownList ID="ddlParentdBF" runat="server" CssClass="chosen-select col-xs-3 padding-none" OnSelectedIndexChanged="ddlParentdBF_SelectedIndexChanged"
                                                        data-style="btn-default" AutoPostBack="true">
                                                        <%--chosen-select--%>
                                                    </asp:DropDownList>
                                                    <label class="pull-right control-label"> is </label>
                                                    <asp:RequiredFieldValidator ID="rfvParentBF_BFtoBF" runat="server" InitialValue="0"
                                                        ControlToValidate="ddlParentdBF" ErrorMessage="*" CssClass="pull-right control-label error"
                                                        Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                                </div>
                                                <div class="col-md-3 width70">
                                                    <asp:DropDownList ID="ddlParentImpactType" runat="server" CssClass="chosen-select col-xs-2 padding-none"
                                                        data-style="btn-default" Style="margin-left: 15px!important;">
                                                        <asp:ListItem Value="0">-Select Impact-</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <label class="pull-right control-label"> Then</label>
                                                    <asp:RequiredFieldValidator ID="rfvParentImpact_BFtoBF" runat="server" InitialValue="0"
                                                        ControlToValidate="ddlParentImpactType" ErrorMessage="*" CssClass="pull-right control-label error"
                                                        Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                                </div>
                                                <div class="col-md-3 width70">
                                                    <asp:DropDownList ID="ddlChildBF" runat="server" CssClass="chosen-select col-xs-2 padding-none"
                                                        data-style="btn-default" Style="margin-left: 35px!important;">
                                                        <asp:ListItem Value="0">-Select Business Function-</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <label class="pull-right control-label"> will be</label>
                                                    <asp:RequiredFieldValidator ID="rfvChildBF_BFtoBF" runat="server" InitialValue="0"
                                                        ControlToValidate="ddlChildBF" ErrorMessage="*" CssClass="pull-right control-label error"
                                                        Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                                </div>
                                                <div class="col-md-3 width70">
                                                    <asp:DropDownList ID="ddlChildImpactType" runat="server" CssClass="chosen-select col-xs-2 padding-none"
                                                        data-style="btn-default" Style="margin-left: 42px!important;">
                                                        <asp:ListItem Value="0">-Select Impact-</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="rfvChildImpact_BFtoBF" runat="server" InitialValue="0"
                                                        ControlToValidate="ddlChildImpactType" ErrorMessage="*" CssClass="control-label error"
                                                        Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                                </div>
                                                <%--   <label class="pull-left control-label">Impacted</label>--%>
                                            </div>
                                        </div>
                                    </asp:Panel>

                                    <asp:Panel ID="pnlBStoBS" runat="server" Visible="false">
                                        <div class="form-group">
                                            <label class="col-xs-2 control-label" style="width: 14%;">Business Service </label>
                                            <div class="col-xs-10 padding-none">

                                                <div class="col-md-3 width70" style="padding-left: 0px">
                                                    <asp:DropDownList ID="ddlParentBusinessServiceBStoBS" runat="server" CssClass="chosen-select col-xs-3 padding-none" OnSelectedIndexChanged="ddlParentBusinessServiceBStoBS_SelectedIndexChanged"
                                                        data-style="btn-default" AutoPostBack="true">
                                                    </asp:DropDownList>
                                                    <label class="pull-right margin-right control-label"> is </label>
                                                    <asp:RequiredFieldValidator ID="rfvParentbusinessServiceBStoBS" runat="server" InitialValue="0"
                                                        ControlToValidate="ddlParentBusinessServiceBStoBS" ErrorMessage="*" CssClass="pull-right control-label error"
                                                        Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                                </div>
                                                <div class="col-md-3 width70" style="padding-left:0px">

                                                    <asp:DropDownList ID="dllParentImpactTypeBStoBs" runat="server" CssClass="chosen-select col-xs-2 padding-none"
                                                        data-style="btn-default">
                                                        <asp:ListItem Value="0">-Select Impact-</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <label class="pull-right control-label margin-right"> By Service</label>
                                                    <asp:RequiredFieldValidator ID="rfvParentImpactTypeBStoBS" runat="server" InitialValue="0"
                                                        ControlToValidate="dllParentImpactTypeBStoBs" ErrorMessage="*" CssClass="pull-right control-label error"
                                                        Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                                </div>
                                                <div class="col-md-3 width70" style="padding-left:0px">
                                                    <asp:DropDownList ID="ddlChildBusinessServiceBStoBS" runat="server" CssClass="chosen-select col-xs-3 padding-none "
                                                        data-style="btn-default">
                                                        <asp:ListItem Value="0">-Select Business Service-</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="rfvChildbusinessServiceBStoBS" runat="server" InitialValue="0"
                                                        ControlToValidate="ddlChildBusinessServiceBStoBS" ErrorMessage="*" CssClass="pull-right control-label error"
                                                        Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                                    <%--<label class="pull-left control-label margin-right">will be</label>--%>
                                                </div>
                                                <div class="col-md-3 width70">
                                                    <asp:DropDownList ID="dllChildImpactTypeBStoBs" runat="server" CssClass="chosen-select col-xs-2 padding-none"
                                                        data-style="btn-default" Visible="false">
                                                        <asp:ListItem Value="0">-Select Impact-</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="rfvChildImpactTypeBStoBS" runat="server" InitialValue="0"
                                                        ControlToValidate="dllChildImpactTypeBStoBs" ErrorMessage="*" CssClass="pull-right control-label error"
                                                        Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>
                                                    <%--   <label class="pull-left control-label">Impacted</label>--%>
                                                </div>
                                            </div>
                                        </div>
                                    </asp:Panel>

                                    <%--  <asp:Panel ID="pnlBFtoBS" runat="server" Visible="false">
                        <div class="form-group">
                            <label class="col-xs-2 control-label">IF Business Function </label>
                            <div class="col-xs-10">


                                <asp:DropDownList ID="ddlBF_BFtoBS" runat="server" CssClass="selectpicker col-xs-3 padding-none"
                                    data-style="btn-default">
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvBF_BFtoBS" runat="server" InitialValue="0"
                                    ControlToValidate="ddlBF_BFtoBS" ErrorMessage="*" CssClass="pull-left margin-right control-label error"
                                    Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>

                                <label class="pull-left margin-right control-label">is </label>
                                <asp:DropDownList ID="ddlBFImpactType_BFtoBS" runat="server" CssClass="selectpicker col-xs-2 padding-none"
                                    data-style="btn-default">
                                    <asp:ListItem Value="0">-Select Impact-</asp:ListItem>
                                </asp:DropDownList>
                                 <asp:RequiredFieldValidator ID="rfvBFImpact_BFtoBS" runat="server" InitialValue="0"
                                    ControlToValidate="ddlBFImpactType_BFtoBS" ErrorMessage="*" CssClass="pull-left margin-right control-label error"
                                    Display="Dynamic" ValidationGroup="vgImpactconfig"></asp:RequiredFieldValidator>

                               
                                <%--   <label class="pull-left control-label">Impacted</label>
                            </div>
                        </div>
                    </asp:Panel>--%>
                                    <div class="form-group ">
                                        <label class="col-xs-2 control-label" style="width: 14%;">Effective Date </label>
                                        <div class="col-xs-10 padding-none">
                                            <%--  <asp:TextBox ID="TextBox19" runat="server" class="form-control "></asp:TextBox>--%>
                                            <telerik:RadAjaxPanel ID="RadAjaxPanel1" runat="server">

                                                <telerik:RadDateTimePicker ID="rdEffectDate" runat="server" DateInput-DateFormat="dd-MM-yyyy hh:mm tt"
                                                    Height="33px" Width="200px" DateInput-CausesValidation="true">
                                                </telerik:RadDateTimePicker>
                                                <asp:RequiredFieldValidator ID="rfvEffectiveDate" runat="server"
                                                    ControlToValidate="rdEffectDate" ErrorMessage="*"
                                                    Display="Dynamic" ValidationGroup="vgImpactconfig" CssClass=" margin-left control-label error"></asp:RequiredFieldValidator>
                                            </telerik:RadAjaxPanel>

                                        </div>
                                    </div>
                                    <div class="form-group ">
                                        <label class="col-xs-2 control-label" style="width: 14%;">Description</label>
                                        <div class="col-xs-10 padding-none">
                                            <asp:TextBox ID="txtDescription" runat="server" class="form-control" Style="width: 26%;" TextMode="MultiLine"></asp:TextBox>
                                        </div>
                                        <div class="clearfix"></div>
                                        <asp:Label ID="lblErr" runat="server" CssClass="error col-md-12" Visible="false"></asp:Label>
                                    </div>


                                    <hr />
                                    <div class="form-group">
                                        <div class="col-md-12">
                                            <div class="pull-right">
                                                <asp:Button ID="btnSave" CssClass="btn btn-primary " runat="server" OnClick="btnSave_Click" ValidationGroup="vgImpactconfig" CausesValidation="true"
                                                    Text="Save"></asp:Button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>
                </div>
            </div>

        </div>
    </form>
</body>
</html>
