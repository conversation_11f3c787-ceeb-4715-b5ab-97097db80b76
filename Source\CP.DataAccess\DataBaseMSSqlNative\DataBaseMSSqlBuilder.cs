﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.DataAccess
{
    internal sealed class DataBaseMSSqlBuilder : IEntityBuilder<DatabaseSqlNative2008>
    {
        IList<DatabaseSqlNative2008> IEntityBuilder<DatabaseSqlNative2008>.BuildEntities(IDataReader reader)
        {
            var databaseMsSql = new List<DatabaseSqlNative2008>();
            while (reader.Read())
            {
                databaseMsSql.Add(((IEntityBuilder<DatabaseSqlNative2008>)this).BuildEntity(reader, new DatabaseSqlNative2008()));
            }

            return (databaseMsSql.Count > 0) ? databaseMsSql : null;
        }

        DatabaseSqlNative2008 IEntityBuilder<DatabaseSqlNative2008>.BuildEntity(IDataReader reader, DatabaseSqlNative2008 databasemsSql)
        {
            databasemsSql.DatabaseName = Convert.IsDBNull(reader["DatabaseName"]) ? string.Empty : Convert.ToString(reader["DatabaseName"]);
            databasemsSql.InstanceName = Convert.IsDBNull(reader["InstanceName"]) ? string.Empty : Convert.ToString(reader["InstanceName"]);
            databasemsSql.UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]);
            databasemsSql.Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]);
            databasemsSql.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);
            databasemsSql.AuthenticationMode = Convert.IsDBNull(reader["AuthenticationMode"]) ? SqlAuthenticateType.Undefined :
                (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(reader["AuthenticationMode"]), true);
            return databasemsSql;
            //databasemsSql.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            //databasemsSql.BaseDatabaseId = Convert.IsDBNull(reader["BaseDatabaseId"]) ? 0 : Convert.ToInt32(reader["BaseDatabaseId"]);
            //databasemsSql.DatabaseName = Convert.IsDBNull(reader["DatabaseName"]) ? string.Empty : Convert.ToString(reader["DatabaseName"]);
            //databasemsSql.InstanceName = Convert.IsDBNull(reader["InstanceName"]) ? string.Empty : Convert.ToString(reader["InstanceName"]);
            //databasemsSql.UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]);
            //databasemsSql.Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]);
            //databasemsSql.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);
            //databasemsSql.AuthenticationMode = Convert.IsDBNull(reader["AuthenticationMode"]) ? SqlAuthenticateType.Undefined :
            //  (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(reader["AuthenticationMode"]), true);
            //return databasemsSql;
                        
         //   databasemsSql.DatabaseName = Convert.IsDBNull(reader["DatabaseName"]) ? string.Empty : Convert.ToString(reader["DatabaseName"]);
        
         //   databasemsSql.UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]);
         //   databasemsSql.Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]);
         //   databasemsSql.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);
         //   databasemsSql.AuthenticationMode = Convert.IsDBNull(reader["AuthenticationMode"]) ? SqlAuthenticateType.Undefined :
         //       (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(reader["AuthenticationMode"]).ToString(), true);
         


        }
    }
}
