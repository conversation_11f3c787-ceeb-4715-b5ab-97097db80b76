﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class ExchangeDAGCompMonitorDataAccess : BaseDataAccess, IExchangeDAGCompMonitorDataAccess
    {
        #region Constructors

        public ExchangeDAGCompMonitorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ExchangeDAGComponantMonitor> CreateEntityBuilder<ExchangeDAGComponantMonitor>()
        {
            return (new ExchangeDAGCompMonitorBuilder()) as IEntityBuilder<ExchangeDAGComponantMonitor>;
        }

        #endregion Constructors

        #region IExchangeDataAccess Members

        /// <summary>
        ///     Get <see cref="ExchangeService" /> From bcms_exchange_healthstatus table by Id.
        /// </summary>
        /// <param name="InfraObjectID">InfraObjectID of the ExchangeHealth</param>
        /// <returns>ExchangeHealth</returns>
        /// <author><PERSON></author>
        /// <Modified> <PERSON><PERSON><PERSON> - 30-04-2014 </Modified>
        ExchangeDAGComponantMonitor IExchangeDAGCompMonitorDataAccess.GetByInfraObjectID(int InfraObjectID)
        {
            try
            {
                if (InfraObjectID < 1)
                {
                    throw new ArgumentNullException("InfraObjectID");
                }

                const string sp = "ExchangeHealth_GetByInfraObjectID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, InfraObjectID);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ExchangeDAGComponantMonitor>()).BuildEntity(reader,
                                new ExchangeDAGComponantMonitor());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetByInfraObjectID(" + InfraObjectID +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        ExchangeDAGComponantMonitor IExchangeDAGCompMonitorDataAccess.GetByInfraObjectIDandPrDatabaseId(int InfraObjectID,
            int prdatabaseid)
        {
            try
            {
                if (InfraObjectID < 1)
                {
                    throw new ArgumentNullException("InfraObjectID");
                }

                //const string sp = "EXCDAGCOMPMON_GETBYIFRAOBJID";
                const string sp = "EXCDAG_COM_MON_GETBYIFRAOBJID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, InfraObjectID);
                    Database.AddInParameter(cmd, Dbstring + "iDataBaseId", DbType.Int32, prdatabaseid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ExchangeDAGComponantMonitor>()).BuildEntity(reader,
                                new ExchangeDAGComponantMonitor());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetByInfraObjectID(" + InfraObjectID +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        ExchangeDAGComponantMonitor IExchangeDAGCompMonitorDataAccess.GetByInfraObjectIDandMailBoxName(int InfraObjectID, int mailboxid,
            string mailboxname)
        {
            try
            {
                if (InfraObjectID < 1)
                {
                    throw new ArgumentNullException("InfraObjectID");
                }

                //const string sp = "EXDGCOMO_GTBYIOBIDANDMBOXNME";
                const string sp = "EXCDAG_COMON_GTBYIOBIDNMBXNME";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, InfraObjectID);
                    Database.AddInParameter(cmd, Dbstring + "iMailBoxId", DbType.Int32, mailboxid);
                    Database.AddInParameter(cmd, Dbstring + "iMailBoxName", DbType.String, mailboxname);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_EXDGCOMOP"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ExchangeDAGComponantMonitor>()).BuildEntity(reader,
                                new ExchangeDAGComponantMonitor());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetByInfraObjectID(" + InfraObjectID +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
       
        /// <summary>
        ///     Get <see cref="ExchangeService" /> From bcms_exchange_healthstatus table.
        /// </summary>
        /// <returns>ExchangeHealth List</returns>
        /// <author>Kiran Ghadge</author>
        /// <Modified> Kuntesh Thakker - 30-04-2014 </Modified>
        IList<ExchangeDAGComponantMonitor> IExchangeDAGCompMonitorDataAccess.GetByInfraObjectIDGetAll(int InfraObjectID)
        {
            try
            {
                const string sp = "EXCDAGCOMPMON_GETBYIFRAOBJID";
                
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, InfraObjectID);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                        return CreateEntityBuilder<ExchangeDAGComponantMonitor>().BuildEntities(reader);
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetByInfraObjectIDGetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<ExchangeDAGComponantMonitor> IExchangeDAGCompMonitorDataAccess.GetAll()
        {
            try
            {
                const string sp = "EXCHANGEHEALTHSTATUS_GETALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ExchangeDAGComponantMonitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<ExchangeDAGComponantMonitor> IExchangeDAGCompMonitorDataAccess.GetByDate(int InfraObjectID, string startDate,
            string endDate)
        {
            try
            {
                const string sp = "ExchangeDag_GetByDate";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    startDate = Convert.ToDateTime(startDate).ToString("dd-MM-yy");
                    endDate = Convert.ToDateTime(endDate).ToString("dd-MM-yy");
#endif
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, InfraObjectID);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.AnsiString, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.AnsiString, endDate);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ExchangeDAGComponantMonitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IExchangeDAGCompMonitorDataAccess.GetByDate(" + InfraObjectID + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        IList<ExchangeDAGComponantMonitor> IExchangeDAGCompMonitorDataAccess.GetExcDagHourlyByGroupId(int groupid)
        {
            try
            {
                const string sp = "ExchangeDag_HourlyGetByGroupId";
                IList<ExchangeDAGComponantMonitor> excdag = new List<ExchangeDAGComponantMonitor>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iGroupId", DbType.Int32, groupid);
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
#if ORACLE
                        cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                        while (reader.Read())
                        {
                            ExchangeDAGComponantMonitor exdag = new ExchangeDAGComponantMonitor();
                            //exdag.PRLastGenLog = reader[0].ToString();
                            //exdag.PRLastGenLogTime = reader[1].ToString();
                            //exdag.DRLastLogReplayed = reader[2].ToString();
                            //exdag.DRLastReplayedLogTime = reader[3].ToString();
                            //exdag.CurrentDatalag = reader[4].ToString();
                            //exdag.CreateDate = Convert.ToDateTime(reader[5].ToString());
                            exdag.PRLastGenLog = reader["PRLastGenLog"].ToString();
                            exdag.PRLastGenLogTime = reader["PRLastGenLogTime"].ToString();
                            exdag.DRLastLogReplayed = reader["DRLastLogReplayed"].ToString();
                            exdag.DRLastReplayedLogTime = reader["DRLastReplayedLogTime"].ToString();
                            exdag.CurrentDatalag = reader["CurrentDatalag"].ToString();
                            exdag.CreateDate = Convert.ToDateTime(reader["CreateDate"].ToString());
                            excdag.Add(exdag);
                        }
                    }
                }
                return excdag;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                "Error In DAL While Executing Function Signature IExchangeDAGCompMonitorDataAccess.GetExcDagHourlyByGroupId(" + groupid + ")" +
                Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        ExchangeDAGComponantMonitor IExchangeDAGCompMonitorDataAccess.ExchangeDagByInfraObjectID(int InfraObjectID)
        {
            try
            {
                if (InfraObjectID < 1)
                {
                    throw new ArgumentNullException("InfraObjectID");
                }

                const string sp = "ExchangeDag_GETBYIFRAOBJID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, InfraObjectID);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                       if (reader.Read())
                        {
                            return (CreateEntityBuilder<ExchangeDAGComponantMonitor>()).BuildEntity(reader,
                                new ExchangeDAGComponantMonitor());
                        }
                       return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.ExchangeDagByInfraObjectID(" + InfraObjectID +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion IExchangeDataAccess Members
    }
}