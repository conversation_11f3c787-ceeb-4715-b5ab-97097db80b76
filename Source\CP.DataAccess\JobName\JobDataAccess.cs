﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    //class JobNameDataAccess : BaseDataAccess, IJobNameDataAccess
    internal sealed class JobDataAccess : BaseDataAccess, IJobDataAccess
    {
        #region Constructors

        public JobDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<Job> CreateEntityBuilder<Job>()
        {
            return (new JobBuilder()) as IEntityBuilder<Job>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="JobName" /> bcms_job Table.
        /// </summary>
        /// <param name="jobName">JobName</param>
        /// <returns>JobName Ilist</returns>
        /// <author>Satyam <PERSON>.</author>
        Job IJobDataAccess.Add(Job jobName)
        {
            try
            {
                const string sp = "Job_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.String, jobName.Name);
                    Database.AddInParameter(cmd, Dbstring + "iJobTypeName", DbType.String, jobName.JobTypeName);
                    Database.AddInParameter(cmd, Dbstring + "iBcmsClassName", DbType.String, jobName.BcmsClassName);
                    Database.AddInParameter(cmd, Dbstring + "iJobCategory", DbType.String, jobName.JobCategory);
                    Database.AddInParameter(cmd, Dbstring + "iReptype", DbType.Int32, !string.IsNullOrEmpty(jobName.Replicationtype ) ? Convert.ToInt32(jobName.Replicationtype) : 0);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, jobName.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iParameterType", DbType.String, jobName.Parameter);
                    Database.AddInParameter(cmd, Dbstring + "iIsSchedule", DbType.Int32, jobName.IsSchedule);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<Job>()).BuildEntity(reader, new Job());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting jobName Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        Job IJobDataAccess.Update(Job job)
        {
            try
            {
                const string sp = "Job_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, job.Id);
                    Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.String, job.Name);
                    Database.AddInParameter(cmd, Dbstring + "iJobTypeName", DbType.String, job.JobTypeName);
                    Database.AddInParameter(cmd, Dbstring + "iBcmsClassName", DbType.String, job.BcmsClassName);
                    Database.AddInParameter(cmd, Dbstring + "iJobCategory", DbType.String, job.JobCategory);
                    Database.AddInParameter(cmd, Dbstring + "iReptype", DbType.Int32, !string.IsNullOrEmpty(job.Replicationtype) ? Convert.ToInt32(job.Replicationtype) : 0);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, job.UpdatorId);
                    Database.AddInParameter(cmd, Dbstring + "iParameterType", DbType.String, job.Parameter);
                    Database.AddInParameter(cmd, Dbstring + "iIsSchedule", DbType.Int32, job.IsSchedule);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        job = reader.Read() ? CreateEntityBuilder<Job>().BuildEntity(reader, job) : null;
                    }

                    return job;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting jobName Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     GetAll <see cref="JobName" /> from bcms_job Table.
        /// </summary>
        /// <returns>JobName Ilist</returns>
        /// <author>Satyam Kumar</author>
        IList<Job> IJobDataAccess.GetAll()
        {
            try
            {
                const string sp = "JobName_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Job>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IJobNameDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<Job> IJobDataAccess.GetInfrJobById(int id)
        {
            try
            {
                //const string sp = "Job_GetById";
                const string sp = "JOB_GETBYID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Job>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IJobNameDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        IList<Job> IJobDataAccess.GetJobyInfraId(int id)
        {
            try
            {
                const string sp = "Job_GetByInfrJobId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Job>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IJobNameDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<Job> IJobDataAccess.GetJobyByBusinessId(int id)
        {
            try
            {
                const string sp = "InfraObjectJob_GetJobByBusinessId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Job>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IJobNameDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="JobName" /> from bcms_job table by id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>JobName</returns>
        /// <author>Satyam kumar</author>
        Job IJobDataAccess.GetById(int id)
        {
            if (id < 1)
            {
                throw new ArgumentNullException("id");
            }

            const string sp = "Job_GetById";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                using (IDataReader reader = Database.ExecuteReader(cmd))
                {
                    if (reader.Read())
                    {
                        return (CreateEntityBuilder<Job>()).BuildEntity(reader, new Job());
                    }
                    return null;
                }
            }
        }

        Job IJobDataAccess.GetByName(string name)
        {
            const string sp = "Job_GetByName";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.AnsiString, name);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                using (IDataReader reader = Database.ExecuteReader(cmd))
                {
                    if (reader.Read())
                    {
                        return (CreateEntityBuilder<Job>()).BuildEntity(reader, new Job());
                    }
                    return null;
                }
            }
        }

        /// <summary>
        ///     UpdateIsSavedColumnById <see cref="JobName" /> from bcms_job Table.
        /// </summary>
        /// <returns>bool</returns>
        /// <author>Satyam kumar</author>
        bool IJobDataAccess.UpdateIsSavedColumnById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "Job_UpdateIsSavedColumn";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Job Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        /// <summary>
        ///     GetAllUnsaved <see cref="JobName" /> from bcms_job Table.
        /// </summary>
        /// <returns>JobName Ilist</returns>
        /// <author>Satyam kumar</author>
        IList<Job> IJobDataAccess.GetAllUnsaved()
        {
            try
            {
                const string sp = "Job_GetAllUnsaved";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Job>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IJobNameDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     GetByReplicationId <see cref="JobName" /> from bcms_job Table.
        /// </summary>
        /// <returns>JobName Ilist</returns>
        /// <author>Satyam kumar</author>
        IList<Job> IJobDataAccess.GetByReplicationId(int id)
        {
            try
            {
                const string sp = "Job_GetById_RepId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iRepId", DbType.Int32, id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Job>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IJobNameDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<Job> IJobDataAccess.GetInfraByReplicationId(int id)
        {
            try
            {
                const string sp = "InfraJob_GetByRepId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iRepId", DbType.Int32, id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Job>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IJobNameDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     GelAlreadyExistsJobsTypeName  <see cref="JobName" /> from bcms_job Table.
        /// </summary>
        /// <returns>bool</returns>
        /// <author>Satyam kumar</author>
        bool IJobDataAccess.IsJobTypeNameExists(string jobTypeName)
        {
            try
            {
                if (jobTypeName == string.Empty)
                {
                    throw new ArgumentNullException("jobTypeName");
                }

                const string sp = "JobName_IsExists";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iJobTypeName", DbType.AnsiString, jobTypeName);

                    Database.ExecuteNonQuery(cmd);
                    int returnCode = GetReturnCodeFromParameter(cmd);

                    //var returnCode = Database.ExecuteNonQuery(cmd);
                    //return returnCode > 0;

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while fetching this JobTypeName.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Job Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        IList<Job> IJobDataAccess.GetByCategory(string category)
        {
            try
            {
                const string sp = "Job_GetByCategory";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iJobCategory", DbType.AnsiString, category);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Job>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IJobNameDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods
    }
}