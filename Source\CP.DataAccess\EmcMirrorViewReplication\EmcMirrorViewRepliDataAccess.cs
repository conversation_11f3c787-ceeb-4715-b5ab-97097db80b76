﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.EmcMirrorViewReplication
{
    internal sealed class EmcMirrorViewRepliDataAccess : BaseDataAccess, IEmcMirrorViewRepliDataAccess
    {
          #region Constructors

        public EmcMirrorViewRepliDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<EmcMirrorView> CreateEntityBuilder<EmcMirrorView>()
        {
            return (new EmcMirrorViewRepliBuilder()) as IEntityBuilder<EmcMirrorView>;
        }

        #endregion Constructors

        EmcMirrorView IEmcMirrorViewRepliDataAccess.AddEmcMirrorView(EmcMirrorView emcmirrorview)
        {
            try
            {
                const string sp = "EmcMirrorViewRepli_create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationId", DbType.Int32, emcmirrorview.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, emcmirrorview.ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iNavispherePath", DbType.AnsiString, emcmirrorview.NavispherePath);
                    Database.AddInParameter(cmd, Dbstring + "iCGName", DbType.AnsiString, emcmirrorview.CGName);
                    Database.AddInParameter(cmd, Dbstring + "iSPNameOrIP", DbType.AnsiString, emcmirrorview.SPNameOrIP);
                    Database.AddInParameter(cmd, Dbstring + "iSPUserName", DbType.AnsiString, emcmirrorview.SPUserName);
                    Database.AddInParameter(cmd, Dbstring + "iSPPassword", DbType.AnsiString, emcmirrorview.SPPassword);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, emcmirrorview.CreatorId);
                   
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        emcmirrorview = reader.Read()
                            ? CreateEntityBuilder<EmcMirrorView>().BuildEntity(reader, emcmirrorview)
                            : null;
                    }

                    if (emcmirrorview == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "Emc MirrorView Replication already exists. Please specify another Emc MirrorView Replication.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this Emc MirrorView Replication.");
                                }
                        }
                    }

                    return emcmirrorview;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting Emc MirrorView Replication Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        EmcMirrorView IEmcMirrorViewRepliDataAccess.UpdateEmcMirrorView(EmcMirrorView emcmirrorview)
        {
            try
            {
                const string sp = "EmcMirrorView_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    //Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, emcmirrorview.Id);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationId", DbType.Int32, emcmirrorview.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, emcmirrorview.ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iNavispherePath", DbType.AnsiString, emcmirrorview.NavispherePath);
                    Database.AddInParameter(cmd, Dbstring + "iCGName", DbType.AnsiString, emcmirrorview.CGName);
                    Database.AddInParameter(cmd, Dbstring + "iSPNameOrIP", DbType.AnsiString, emcmirrorview.SPNameOrIP);
                    Database.AddInParameter(cmd, Dbstring + "iSPUserName", DbType.AnsiString, emcmirrorview.SPUserName);
                    Database.AddInParameter(cmd, Dbstring + "iSPPassword", DbType.AnsiString, emcmirrorview.SPPassword);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, emcmirrorview.UpdatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        emcmirrorview = reader.Read()
                            ? CreateEntityBuilder<EmcMirrorView>().BuildEntity(reader, emcmirrorview)
                            : null;
                    }

                    return emcmirrorview;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Emc MirrorView Storage Replication Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
            throw new NotImplementedException();
        }

        EmcMirrorView IEmcMirrorViewRepliDataAccess.GetEmcMirrorViewbyReplicationId(int id)
        {
            try
            {
                const string sp = "EmcMirrorView_GetByRepliId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<EmcMirrorView>()).BuildEntity(reader,
                                new EmcMirrorView());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEmcMirrorViewRepliDataAccess.GetEmcMirrorViewbyReplicationId(" + id +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<EmcMirrorView> IEmcMirrorViewRepliDataAccess.EmcMirrorViewReplication_GetAll()
        {
            const string sp = "EmcMirrorView_GETALL";
            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                using (IDataReader reader = Database.ExecuteReader(cmd))
                {
                    return CreateEntityBuilder<EmcMirrorView>().BuildEntities(reader);
                }
            }
        }

        public IList<EmcMirrorView> EmcMirrorView_GetAllByReplicationType(string replicationtype)
        {
            IList<EmcMirrorView> lstEMCSRDFStarRepli = new List<EmcMirrorView>();

            try
            {

                const string sp = "EmcMirrorView_GetAllRepli";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iTypeId", DbType.String, replicationtype);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            EmcMirrorView objEmcMirrorView = new EmcMirrorView();

                            objEmcMirrorView.ReplicationId = reader["ReplicationId"] != null ? Convert.ToInt32(reader["ReplicationId"]) : 0;
                            objEmcMirrorView.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                            objEmcMirrorView.NavispherePath = Convert.IsDBNull(reader["NavispherePath"]) ? string.Empty : Convert.ToString(reader["NavispherePath"]);
                            objEmcMirrorView.CGName = Convert.IsDBNull(reader["CGName"]) ? string.Empty : Convert.ToString(reader["CGName"]);
                            objEmcMirrorView.ReplicationBase.Id = reader["ReplicationId"] != null ? Convert.ToInt32(reader["ReplicationId"]) : 0;
                            objEmcMirrorView.ReplicationBase.reptype = reader["ReplicationType"] != null ? Convert.ToString(reader["ReplicationType"]) : string.Empty;
                            objEmcMirrorView.ReplicationBase.Name = reader["ReplicationName"] != null ? Convert.ToString(reader["ReplicationName"]) : string.Empty;

                            lstEMCSRDFStarRepli.Add(objEmcMirrorView);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return lstEMCSRDFStarRepli;
        }

        EmcMirrorView IEmcMirrorViewRepliDataAccess.GetEmcMirrorViewbyServerId(int ServerId)
        {
            try
            {
                const string sp = "EmcMirror_GetbyServerID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, ServerId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<EmcMirrorView>()).BuildEntity(reader,
                                new EmcMirrorView());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEmcMirrorViewRepliDataAccess.GetEmcMirrorViewbyServerId(" + ServerId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<EmcMirrorView> IEmcMirrorViewRepliDataAccess.GetEmcMirrorViewbyServerType(String ServerType)
        {
            try
            {
                const string sp = "EmcMirror_GetbyServerType";
                IList<EmcMirrorView> lstEMCSRDFStarRepli = new List<EmcMirrorView>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iServerType", DbType.String, ServerType);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //while (reader.Read())
                        //{
                        //    EmcMirrorView objEmcMirrorView = new EmcMirrorView();

                        //   // objEmcMirrorView.Id = reader["Id"] != null ? Convert.ToInt32(reader["Id"]) : 0;                    
                        //    objEmcMirrorView.server.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                        //    //objEmcMirrorView.server.Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
                        //    //objEmcMirrorView.server.SiteId = Convert.IsDBNull(reader["SiteId"]) ? 0 : Convert.ToInt32(reader["SiteId"]);
                        //    //objEmcMirrorView.server.IsActive = reader["IsActive"] != null ? Convert.ToInt32(reader["IsActive"]) : 0;

                        //    //objEmcMirrorView.ReplicationId = reader["ReplicationId"] != null ? Convert.ToInt32(reader["ReplicationId"]) : 0;
                        //    //objEmcMirrorView.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                        //    //objEmcMirrorView.NavispherePath = Convert.IsDBNull(reader["NavispherePath"]) ? string.Empty : Convert.ToString(reader["NavispherePath"]);
                        //    //objEmcMirrorView.CGName = Convert.IsDBNull(reader["CGName"]) ? string.Empty : Convert.ToString(reader["CGName"]);
                        //    //objEmcMirrorView.ReplicationBase.Id = reader["ReplicationId"] != null ? Convert.ToInt32(reader["ReplicationId"]) : 0;
                        //    //objEmcMirrorView.ReplicationBase.reptype = reader["ReplicationType"] != null ? Convert.ToString(reader["ReplicationType"]) : string.Empty;
                        //    //objEmcMirrorView.ReplicationBase.Name = reader["ReplicationName"] != null ? Convert.ToString(reader["ReplicationName"]) : string.Empty;

                        //    lstEMCSRDFStarRepli.Add(objEmcMirrorView);
                        //}

                        var serlogs = new List<EmcMirrorView>();
                        EmcMirrorView objEmcMirrorView = new EmcMirrorView();
                        while (reader.Read())
                        {                        
                            
                                objEmcMirrorView.ReplicationBase.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);

                            serlogs.Add(objEmcMirrorView);

                        }
                        return serlogs;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEmcMirrorViewRepliDataAccess.GetEmcMirrorViewbyServerId(" + ServerType +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
    }
}
