﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ApplicationService", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ApplicationService : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string ServiceName { get; set; }

        [DataMember]
        public bool Status { get; set; }

        [DataMember]
        public String ServerType { get; set; }

        #endregion Properties
    }
}