﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class AlertDataAccess : BaseDataAccess, IAlertDataAccess
    {
        #region Constructors

        public AlertDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<Alert> CreateEntityBuilder<Alert>()
        {
            return (new AlertBuilder()) as IEntityBuilder<Alert>;
        }

        #endregion Constructors

        /// <summary>
        ///     Check <see cref="Alert" /> alert.
        /// </summary>
        /// <param name="alert">alert</param>
        /// <returns>bool</returns>
        /// <author><PERSON><PERSON><PERSON></author>
        /// <Modified> <PERSON><PERSON><PERSON> - CP 4.0v - 08-05-2014 for InfraObjectId</Modified>
        bool IAlertDataAccess.Add(Alert alert)
        {
            
            try
            {
                const string sp = "Alert_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iType", DbType.AnsiString, alert.Type);
                    Database.AddInParameter(cmd, Dbstring+"iSeverity", DbType.AnsiString, alert.Severity);
                    Database.AddInParameter(cmd, Dbstring+"iSystemMessage", DbType.AnsiString, alert.SystemMessage);
                    Database.AddInParameter(cmd, Dbstring+"iUserMessage", DbType.AnsiString, alert.UserDefinedMessage);
                    Database.AddInParameter(cmd, Dbstring+"iJobName", DbType.AnsiString, alert.JobName);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, alert.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring +"iIPADDRESS", DbType.Int32, alert.IPAddress);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    // Database.AddInParameter(cmd, Dbstring+"iIsApplication", DbType.Int32, alert.IsApplication);
                    int value = Database.ExecuteNonQuery(cmd);

                    if (value > 0)
                    {
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.Add (" + alert + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Alert" /> from alert table by InfraObjectId.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>Alert</returns>
        /// <author>Shivraj Mujumale</author>
        /// <Modified> Kuntesh Thakker - CP 4.0v - 08-05-2014 for InfraObjectId</Modified>
        Alert IAlertDataAccess.GetByInfraObjectId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "ALERT_GETBYINFRAOBJECTID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<Alert>()).BuildEntity(reader, new Alert()) : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByInfraObjectId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Alert" /> from alert table by all InfraObjectId.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>Alert List</returns>
        /// <author>Shivraj Mujumale</author>
        /// <Modified> Kuntesh Thakker - CP 4.0v - 08-05-2014 for InfraObjectId</Modified>
        IList<Alert> IAlertDataAccess.GetAllByInfraObjectId(int id)
        {
            
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "ALERT_GETALLBYINFRAOBJECTID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAllByInfraObjectId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Alert" /> from alert table by user Id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>Alert List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<Alert> IAlertDataAccess.GetByUserId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "ALERT_GETBYUSERID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByUserId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Alert" /> from alert table by user name.
        /// </summary>
        /// <param name="name">name</param>
        /// <returns>Alert List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<Alert> IAlertDataAccess.GetByUserName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }
                const string sp = "ALERT_GETBYUSERNAME";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.AnsiString, name);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByUserName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Alert" /> from alert table by Last Alert Id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>Alert List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<Alert> IAlertDataAccess.GetByLastAlertId(int id)
        {
            try
            {
                const string sp = "ALERT_GETBYLASTALERTID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByLastAlertId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Alert" /> from alert table by Last Alert Id.
        /// </summary>
        /// <param name="startDate,endDate,groupId">startDate,endDate,infraObjectId</param>
        /// <returns>Alert List</returns>
        /// <author>Shivraj Mujumale</author>
        /// <Modified> Kuntesh Thakker - CP 4.0v - 08-05-2014 for InfraObjectId</Modified>
        IList<Alert> IAlertDataAccess.GetByDate(int infraObjectId, string startDate, string endDate)
        {
            try
            {
                if (infraObjectId < 1)
                {
                    throw new ArgumentNullException("infraObjectId");
                }
                if (startDate == string.Empty)
                {
                    throw new ArgumentNullException("startDate");
                }
                if (endDate == string.Empty)
                {
                    throw new ArgumentNullException("endDate");
                }

                const string sp = "ALERT_GETBYDATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                   // startDate = Convert.ToDateTime(startDate).ToString("dd-MMM-yy");
                   // endDate = Convert.ToDateTime(endDate).ToString("dd-MMM-yy");
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iStartDate", DbType.AnsiString, startDate);
                    Database.AddInParameter(cmd, Dbstring+"iEndDate", DbType.AnsiString, endDate);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByDate(" + startDate + ", " +
                    endDate + ", " + infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Alert" /> from alert table by Last Alert Id.
        /// </summary>
        /// <param name="startDate,endDate,groupId">startDate,endDate</param>
        /// <returns>Alert List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<Alert> IAlertDataAccess.GetAllByDate(string startDate, string endDate)
        {
            try
            {
                if (startDate == string.Empty)
                {
                    throw new ArgumentNullException("startDate");
                }
                if (endDate == string.Empty)
                {
                    throw new ArgumentNullException("endDate");
                }

                const string sp = "Alert_GetAllByDate";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iStartDate", DbType.AnsiString, startDate);
                    Database.AddInParameter(cmd, "iEndDate", DbType.AnsiString, endDate);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByDate(" + startDate + ", " +
                    endDate + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="BusinessFunction" /> from bcms_application table.
        /// </summary>
        /// <param name="">Alert</param>
        /// <returns>Alert</returns>
        /// <author>Jeyapandi</author>
        IList<Alert> IAlertDataAccess.GetAll()
        {
            try
            {
                const string sp = "ALERT_GETALL";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Alert" /> from alert table by usermessage.
        /// </summary>
        /// <param name="name">usermsg</param>
        /// <returns>Alert List</returns>
        /// <author>Pratik T</author>
        IList<Alert> IAlertDataAccess.GetAllByUserMessage(string userMsg, string startDate, string endDate, int infraid)
        {
            try
            {
                if (userMsg == string.Empty)
                {
                    throw new ArgumentNullException("userMsg");
                }
                userMsg = "%" + userMsg + "%";
                const string sp = "ALERT_GETBYUSERMSG";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iUserMsg", DbType.AnsiString, userMsg);
                    Database.AddInParameter(cmd, Dbstring+"iStartDate", DbType.AnsiString, startDate);
                    Database.AddInParameter(cmd, Dbstring+"iEndDate", DbType.AnsiString, endDate);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, infraid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAllByUserMessage(" + userMsg + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Alert> IAlertDataAccess.AlertsByusermsgnall(string userMsg, string startDate, string endDate)
        {
            try
            {
                if (userMsg == string.Empty)
                {
                    throw new ArgumentNullException("userMsg");
                }
                userMsg = "%" + userMsg + "%";
                const string sp = "ALERT_USERMSGstdtetdtall";
                IList<Alert> objprl = new List<Alert>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iUserMsg", DbType.AnsiString, userMsg);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.AnsiString, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.AnsiString, endDate);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var obj = new Alert
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                Type = Convert.IsDBNull(reader["Type"])
                                    ? string.Empty
                                    : Convert.ToString(reader["Type"]),
                                Severity = Convert.IsDBNull(reader["Severity"])
                                    ? string.Empty
                                    : Convert.ToString(reader["Severity"]),
                                SystemMessage = Convert.IsDBNull(reader["SystemMessage"])
                                    ? string.Empty
                                    : Convert.ToString(reader["SystemMessage"]),
                                UserDefinedMessage = Convert.IsDBNull(reader["USERMESSAGE"])
                                    ? string.Empty
                                    : Convert.ToString(reader["USERMESSAGE"]),
                                JobName = Convert.IsDBNull(reader["JobName"])
                                    ? string.Empty
                                    : Convert.ToString(reader["JobName"]),
                                InfraObjectName = Convert.IsDBNull(reader["InfraObjectName"])
                                    ? string.Empty
                                    : Convert.ToString(reader["InfraObjectName"]),
                                //  alert.IsApplication = reader.IsDBNull(FLD_IsApplication) ? 0 : reader.GetInt32(FLD_IsApplication);
                                CreateDate = Convert.IsDBNull(reader["CreateDate"])
                                    ? DateTime.MinValue
                                    : Convert.ToDateTime(reader["CreateDate"].ToString())

                            };
                            objprl.Add(obj);

                        }
                        return objprl;
                    }
                }
            }



            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.AlertsByusermsgnall(" + userMsg + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Alert> IAlertDataAccess.GetAlertByType()
        {
            try
            {
                IList<Alert> _alert = new List<Alert>();

                const string sp = "ALERTBYTYPE_GETALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var userProfile = new Alert
                            {
                                //Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),

                                Type = Convert.IsDBNull(reader["TYPE"]) ? string.Empty : Convert.ToString(reader["TYPE"])



                            };

                            _alert.Add(userProfile);
                        }
                    }
                }

                return _alert;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature GetAlertByType.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<Alert> IAlertDataAccess.GetAlertsByInfraType(string type, int infraid)
        {
            try
            {
                if (type == string.Empty)
                {
                    throw new ArgumentNullException("type");
                }
                const string sp = "ALERT_GETBYTYPE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "ITYPE", DbType.AnsiString, type);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, infraid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAllByUserMessage(" + type + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Alert> IAlertDataAccess.GetAllAlert24Hrs()
        {
            try
            {
                const string sp = "ALERT_GETALLBY24Hr";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Alert> IAlertDataAccess.GetAlertsBySeverity(string severity, int infraid)
        {
            try
            {
                if (severity == string.Empty)
                {
                    throw new ArgumentNullException("type");
                }
                const string sp = "ALERT_GETBYSEVERITY";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "ISeverity", DbType.AnsiString, severity);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, infraid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAlertsBySeverity(" + severity + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Alert> IAlertDataAccess.GetAllAlertWithoutInformation()
        {
            try
            {
                const string sp = "ALERT_GETALLWITHOUTINFO";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        IList<Alert> IAlertDataAccess.GetAlertTypeBySeverity(string severity)
        {
            try
            {
                IList<Alert> _alert = new List<Alert>();

                const string sp = "ALERTBYTYPE_GETBYSEVERITY";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "ISeverity", DbType.AnsiString, severity);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var userProfile = new Alert
                            {
                                //Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),

                                Type = Convert.IsDBNull(reader["TYPE"]) ? string.Empty : Convert.ToString(reader["TYPE"])



                            };

                            _alert.Add(userProfile);
                        }
                    }
                }

                return _alert;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAlertTypeBySeverity" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        Alert IAlertDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "ALERT_GETBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<Alert>()).BuildEntity(reader, new Alert()) : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        //restapi
        IList<Alert> IAlertDataAccess.GetAlertByBusinessServiceName(string name)
        {
            IList<Alert> AlertList = new List<Alert>();
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "GetAlertsByBusinessServiceName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }

            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAlertByBusinessServiceName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<Alert> IAlertDataAccess.GetCurrentAlert()
        {

            try
            {
                const string sp = "GetCurrentAlert_Details";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetCurrentAlert" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }

        }

        IList<Alert> IAlertDataAccess.GetCurrentAlertCount()
        {

            try
            {
                const string sp = "GetCurrentAlert";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetCurrentAlertCount" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }

        }

        IList<Alert> IAlertDataAccess.GetCriticalAlertCount()
        {

            try
            {
                const string sp = "GetCritical_AlertsCount";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Alert>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetCriticalAlertCount" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }

        }

        IList<Alert> IAlertDataAccess.GetAlertsByBusServDate(int BusinessServiceId, string startDate, string endDate)
        {
            try
            {
                //if (BusinessServiceId < 1)
                //{
                //    throw new ArgumentNullException("BusinessServiceId");
                //}
                if (startDate == string.Empty)
                {
                    throw new ArgumentNullException("startDate");
                }
                if (endDate == string.Empty)
                {
                    throw new ArgumentNullException("endDate");
                }

                const string sp = "ALERT_GETBYDATE_BS_RPT";

                IList<Alert> alert_1 = new List<Alert>();

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    // startDate = Convert.ToDateTime(startDate).ToString("dd-MMM-yy");
                    // endDate = Convert.ToDateTime(endDate).ToString("dd-MMM-yy");
                    Database.AddInParameter(cmd, Dbstring + "IBUSINESSSERVICEID", DbType.Int32, BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring + "ISTARTDATE", DbType.AnsiString, startDate);
                    Database.AddInParameter(cmd, Dbstring + "IENDDATE", DbType.AnsiString, endDate);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //return CreateEntityBuilder<Alert>().BuildEntities(reader);

                        while (reader.Read())
                        {
                            Alert objalert = new Alert();

                            objalert.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            objalert.Type = Convert.IsDBNull(reader["Type"])
                                ? string.Empty
                                : Convert.ToString(reader["Type"]);
                            objalert.Severity = Convert.IsDBNull(reader["Severity"])
                                ? string.Empty
                                : Convert.ToString(reader["Severity"]);
                            objalert.SystemMessage = Convert.IsDBNull(reader["SystemMessage"])
                                ? string.Empty
                                : Convert.ToString(reader["SystemMessage"]);
                            objalert.UserDefinedMessage = Convert.IsDBNull(reader["USERMESSAGE"])
                                ? string.Empty
                                : Convert.ToString(reader["USERMESSAGE"]);
                            objalert.JobName = Convert.IsDBNull(reader["JobName"])
                                ? string.Empty
                                : Convert.ToString(reader["JobName"]);
                            objalert.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
                            objalert.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["CreateDate"].ToString());

                            objalert.BusinessServiceID = Convert.IsDBNull(reader["BusinessServiceID"]) ? 0 : Convert.ToInt32(reader["BusinessServiceID"]);
                            objalert.BusinessServiceName = Convert.IsDBNull(reader["BusinessServiceName"])
                               ? string.Empty
                               : Convert.ToString(reader["BusinessServiceName"]);

                            objalert.IPAddress = Convert.IsDBNull(reader["IPADDRESS"])
                                 ? string.Empty
                                 : Convert.ToString(reader["IPADDRESS"]);

                            objalert.HostName = Convert.IsDBNull(reader["HOSTNAME"])
                                ? string.Empty
                                : Convert.ToString(reader["HOSTNAME"]);

                            alert_1.Add(objalert);

                        }
                    }
                }
                return alert_1;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByDate(" + startDate + ", " +
                    endDate + ", " + BusinessServiceId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
    
    }
}