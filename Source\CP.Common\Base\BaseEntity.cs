﻿using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Xml;
using System.Xml.Serialization;

namespace CP.Common.Base
{
    [Serializable]
    public abstract class BaseEntity : ICloneable
    {
        [DataMember]
        public virtual int Id { get; set; }

        [DataMember]
        public bool IsRemoved { get; set; }

        [DataMember]
        public DateTime CreateDate { get; set; }

        [DataMember]
        public DateTime UpdateDate { get; set; }

        [DataMember]
        public int IsActive { get; set; }

        [DataMember]
        public int IsMail { get; set; }

        [DataMember]
        public int CreatorId { get; set; }

        [DataMember]
        public int UpdatorId { get; set; }

        [XmlIgnore]
        public virtual bool IsNew
        {
            [DebuggerStepThrough]
            get { return (Id <= 0); }
        }

        object ICloneable.Clone()
        {
            return Clone();
        }

        public virtual BaseEntity Clone()
        {
            using (var ms = new MemoryStream())
            {
                (new BinaryFormatter()).Serialize(ms, this);

                ms.Seek(0, SeekOrigin.Begin);

                return (new BinaryFormatter()).Deserialize(ms) as BaseEntity;
            }
        }

        public string GetSerializeText()
        {
            Type currentEntityType = GetType();

            var serializedContent = new StringBuilder();
            var xmlWriterSettings = new XmlWriterSettings
            {
                Encoding = Encoding.UTF8,
                CloseOutput = true,
                OmitXmlDeclaration = true
            };
            using (XmlWriter xmlWriter = XmlWriter.Create(serializedContent, xmlWriterSettings))
            {
                var serializer = new XmlSerializer(currentEntityType);
                serializer.Serialize(xmlWriter, this);
                xmlWriter.Flush();
                xmlWriter.Close();
            }
            return serializedContent.ToString();
        }
    }
}