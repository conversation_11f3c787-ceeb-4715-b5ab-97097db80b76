﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using System.Text;
using CP.ExceptionHandler;
using System.Xml;
using System.Xml.Serialization;
using System.Configuration;
using CP.Helper.Interface;
using System.Web.Services;
using System.Globalization;
using log4net;
using System.Security.Cryptography;
using System.IO;

namespace CP.UI
{
    public static class Utility
    {

        private static readonly IFacade NewFacade = new Facade();
        public static string dbParaPrefix = "";
        static ILogger log = new Log();

        public static string GetFullName(string firstName, string middleName, string lastName)
        {
            return firstName.Concate(' ', middleName).Concate(' ', lastName);
        }

        public static bool IsValidEmailCheck(string emailAddress)
        {
            if (emailAddress != string.Empty)
            {
                var expReg = new Regex(@"\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*", RegexOptions.IgnoreCase);
                var currentMatchs = expReg.Matches(emailAddress);
                if (currentMatchs.Count > 0)
                {
                    return true;
                }
            }
            return false;
        }

        public static void PopulateApplication(ListView lstApp)
        {
            lstApp.DataSource = null;
            var applicationList = NewFacade.GetAllApplicationGroups();
            if (applicationList == null) return;
            lstApp.DataSource = applicationList;
            lstApp.DataBind();
        }

        public static void PopulateApplicationByIsReplication(ListControl lstapp, bool addDefaultItem, int loginId, bool ApplicationAllFlag)
        {
            //lstApp.DataSource = null;
            //var applicationList =
            //if (applicationList == null) return;
            //lstApp.DataSource = applicationList;
            //lstApp.DataBind();

            lstapp.DataSource = null;

            //if (ApplicationAllFlag)
            //{
            var applicationList = NewFacade.GetAllApplicationGroupsByIsReplication();
            if (applicationList == null) return;
            lstapp.DataSource = applicationList;
            lstapp.DataTextField = "Name";
            lstapp.DataValueField = "ReplicationId";
            lstapp.DataBind();
            if (addDefaultItem)
            {
                lstapp.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectApplicationName);
            }
            //}
            //else

            //{
            //    var applicationList = NewFacade.GetBusinessFunctionByLoginId(loginId);

            //    if (applicationList == null) return;
            //    lstapp.DataSource = applicationList;
            //    lstapp.DataTextField = "Name";
            //    lstapp.DataValueField = "Id";
            //    lstapp.DataBind();
            //    if (addDefaultItem)
            //    {
            //        lstapp.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectApplicationName);
            //    }
            //}
        }

        public static void PopulateWorkflowsByLogInId(ListControl lstWorkflows, string _Issuperadmin, int userid, bool addDefaultItem)
        {
            lstWorkflows.Items.Clear();
            var workflowList = NewFacade.GetWorkflowsByUserrole(_Issuperadmin, userid);
            if (workflowList != null)
            {
                var workflowListByOrder = from a in workflowList orderby a.Name ascending select a;

                lstWorkflows.DataSource = workflowListByOrder;
                lstWorkflows.DataTextField = "Name";
                lstWorkflows.DataValueField = "Id";
                lstWorkflows.DataBind();
            }
            if (addDefaultItem)
            {
                lstWorkflows.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectWorkflowName);
            }
        }

        public static void PopulateApplication(ListControl lstapp, bool addDefaultItem, int loginId, bool ApplicationAllFlag)
        {
            lstapp.DataSource = null;
            //var applicationList = NewFacade.GetAllApplications();

            if (ApplicationAllFlag)
            {
                var applicationList = NewFacade.GetAllApplicationGroups();
                if (applicationList == null) return;
                lstapp.DataSource = applicationList;
                lstapp.DataTextField = "Name";
                lstapp.DataValueField = "Id";
                lstapp.DataBind();
                if (addDefaultItem)
                {
                    lstapp.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectApplicationName);
                }
            }
            else
            {
                var applicationList = NewFacade.GetBusinessFunctionByLoginId(loginId);

                if (applicationList == null) return;
                lstapp.DataSource = applicationList;
                lstapp.DataTextField = "Name";
                lstapp.DataValueField = "Id";
                lstapp.DataBind();
                if (addDefaultItem)
                {
                    lstapp.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectApplicationName);
                }
            }
        }

        public static void PopulateBusinessFunction(ListControl lstBusinessFunction, bool addDefaultItem, int businessServiceId)
        {
            lstBusinessFunction.Items.Clear();
            if (businessServiceId > 0)
            {
                var functionList = NewFacade.GetBusinessFunctionsByBusinessServiceId(businessServiceId);
                if (functionList != null)
                {
                    lstBusinessFunction.DataSource = functionList;
                    lstBusinessFunction.DataTextField = "Name";
                    lstBusinessFunction.DataValueField = "Id";
                    lstBusinessFunction.DataBind();
                }
            }
            if (addDefaultItem)
                lstBusinessFunction.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessFunction);
        }

        public static void PopulateBusinessFunction(ListControl lstBusinessFunction, bool addDefaultItem, int businessServiceId, string businessFunctionId)
        {
            lstBusinessFunction.Items.Clear();
            if (businessServiceId > 0)
            {
                var functionList = NewFacade.GetBusinessFunctionsByBusinessServiceId(businessServiceId);
                if (functionList != null)
                {
                    lstBusinessFunction.DataSource = functionList;
                    lstBusinessFunction.DataTextField = "Name";
                    lstBusinessFunction.DataValueField = "Id";
                    lstBusinessFunction.DataBind();
                    if (!string.IsNullOrEmpty(businessFunctionId))
                    {
                        var functionIdList = functionList.Where(a => a.Id == Convert.ToInt32(businessFunctionId));
                        if (functionIdList != null && functionIdList.Count() > 0)
                            lstBusinessFunction.SelectedValue = businessFunctionId;
                    }
                }
            }
            if (addDefaultItem)
                lstBusinessFunction.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessFunction);
        }

        public static void PopulateBusinessType(ListControl lstBusinessType, bool addDefaultItem)
        {
            lstBusinessType.Items.Clear();
            foreach (var enmSiteLangItem in Enum.GetValues(typeof(InfraObjectType)))
            {
                if ((int)enmSiteLangItem != 0)
                {
                    var oListItem = new ListItem(enmSiteLangItem.ToString(), ((int)enmSiteLangItem).ToString());
                    if (oListItem.Text == "Application" || oListItem.Text == "DB" || oListItem.Text == "Virtual" || oListItem.Text == "Undefined")
                    {
                        lstBusinessType.Items.Add(oListItem);
                    }
                }
            }

            if (addDefaultItem)
                lstBusinessType.AddDefaultItemWithValue(Constants.UIConstants.DropDownlItemPleaseSelectBusinessType);
        }

        public static void PopulateReplicationModes(ListControl lstReplicationModes)
        {
            lstReplicationModes.Items.Clear();

            var replicationModes = Enum.GetNames(typeof(ReplicationMode)).Select(x => new { text = x, value = (int)Enum.Parse(typeof(ReplicationMode), x) });
            if (replicationModes != null)
            {
                lstReplicationModes.DataSource = replicationModes;
                lstReplicationModes.DataTextField = "text";
                lstReplicationModes.DataValueField = "value";
                lstReplicationModes.DataBind();
            }
        }

        public static void PopulateSubBusinessType(ListControl lstSubBusinessType, Type enumType, bool addDefaultItem)
        {
            //lstSubBusinessType.Items.Clear();
            //foreach (var enmSiteLangItem in Enum.GetValues(typeof(InfraObjectType)))
            //{
            //    if ((int)enmSiteLangItem != 0)
            //    {
            //        var oListItem = new ListItem(enmSiteLangItem.ToString(), ((int) enmSiteLangItem).ToString());
            //        if (oListItem.Text == "DatabaseNativeReplication" || oListItem.Text == "DatabaseStorageReplicationFullDB" || oListItem.Text == "DatabaseStorageReplicationLogshipping" || oListItem.Text == "DataBaseDataSyncReplication")
            //        {
            //            lstSubBusinessType.Items.Add(oListItem);
            //        }
            //    }
            //}
            lstSubBusinessType.Items.Clear();
            foreach (var enmSiteLangItem in Enum.GetNames(enumType))
            {
                if ((enmSiteLangItem != null) && (enmSiteLangItem.Length > 0))
                {
                    int value = (int)Enum.Parse(enumType, enmSiteLangItem);
                    string strDescription = EnumHelper.GetDescription((Enum)Enum.Parse(enumType, enmSiteLangItem));
                    var oListItem = new ListItem(strDescription, value.ToString());
                    if (oListItem.Text == "Database - Native Replication" || oListItem.Text == "Database - Storage Replication (Full DB)" || oListItem.Text == "Database - Storage Replication (Logshipping)" || oListItem.Text == "Database - DataSync Replication" || oListItem.Text == "Database - RSync Replication" || oListItem.Text == "Database - Third Party Replication" || oListItem.Text == "Sybase-SRS" || oListItem.Text == "MariaDB Galera Cluster")
                    {
                        lstSubBusinessType.Items.Add(oListItem);
                    }
                }
            }

            if (addDefaultItem)
                lstSubBusinessType.AddDefaultItemWithValue(Constants.UIConstants.DropDownlItemPleaseSelectBusinessType);
        }

        public static void PopulateSubAppBusinessType(ListControl lstSubBusinessType, Type enumType, bool addDefaultItem)
        {
            //lstSubBusinessType.Items.Clear();
            //foreach (var enmSiteLangItem in Enum.GetValues(typeof(InfraObjectType)))
            //{
            //    if ((int)enmSiteLangItem != 0)
            //    {
            //        var oListItem = new ListItem(enmSiteLangItem.ToString(), ((int)enmSiteLangItem).ToString());
            //        if (oListItem.Text == "ApplicationStorageReplication" || oListItem.Text == "ApplicationDataSyncReplication")
            //        {
            //            lstSubBusinessType.Items.Add(oListItem);
            //        }
            //    }
            //}

            lstSubBusinessType.Items.Clear();
            foreach (var enmSiteLangItem in Enum.GetNames(enumType))
            {
                if ((enmSiteLangItem != null) && (enmSiteLangItem.Length > 0))
                {
                    int value = (int)Enum.Parse(enumType, enmSiteLangItem);
                    string strDescription = EnumHelper.GetDescription((Enum)Enum.Parse(enumType, enmSiteLangItem));
                    var oListItem = new ListItem(strDescription, value.ToString());
                    if (oListItem.Text == "Application - Storage Replication" || oListItem.Text == "Application - DataSync Replication" || oListItem.Text == "Application - Custom Replication" || oListItem.Text == "Application - Native Replication" || oListItem.Text == "Application - No Replication" || oListItem.Text == "Application - Third Party Replication" || oListItem.Text == "Sybase-SRS")
                    {
                        lstSubBusinessType.Items.Add(oListItem);
                    }
                }
            }

            if (addDefaultItem)
                lstSubBusinessType.AddDefaultItemWithValue(Constants.UIConstants.DropDownlItemPleaseSelectBusinessType);
        }

        public static void PopulateSubVirtualBusinessType(ListControl lstSubBusinessType, Type enumType, bool addDefaultItem)
        {
            lstSubBusinessType.Items.Clear();
            foreach (var enmSiteLangItem in Enum.GetNames(enumType))
            {
                if ((enmSiteLangItem != null) && (enmSiteLangItem.Length > 0))
                {
                    int value = (int)Enum.Parse(enumType, enmSiteLangItem);
                    string strDescription = EnumHelper.GetDescription((Enum)Enum.Parse(enumType, enmSiteLangItem));
                    var oListItem = new ListItem(strDescription, value.ToString());
                    if (oListItem.Text == "VMware - Native Replication" || oListItem.Text == "VMware - Storage Replication" || oListItem.Text == "VMware - DataSync Replication" || oListItem.Text == "Hyper-V-Native Replication" || oListItem.Text == "ESXI-HP3Par-Replication" || oListItem.Text == "eBDR Replication-Perpetuuiti" || oListItem.Text == "Nutanix - Leap Replication" || oListItem.Text == "Nutanix - Protection Domain Replication")
                    {
                        lstSubBusinessType.Items.Add(oListItem);
                    }
                }
            }

            if (addDefaultItem)
                lstSubBusinessType.AddDefaultItemWithValue(Constants.UIConstants.DropDownlItemPleaseSelectBusinessType);
        }

        public static void PopulateBusinessServicePriority(ListControl lstBusinessPriority, bool addDefaultItem)
        {
            lstBusinessPriority.Items.Clear();
            foreach (var enmSiteLangItem in Enum.GetValues(typeof(BusinessServicePriority)))
            {
                if ((int)enmSiteLangItem != 0)
                {
                    var oListItem = new ListItem(enmSiteLangItem.ToString(), ((int)enmSiteLangItem).ToString());
                    lstBusinessPriority.Items.Add(oListItem);
                }
            }
            if (addDefaultItem)
                lstBusinessPriority.AddDefaultItemWithValue(Constants.UIConstants.DropDownlItemPleaseSelectBusinessServicePriority);
        }

        public static void PopulateBusinessServiceReportSch(ListControl appGroup, bool addDefaultItem)
        {
            appGroup.Items.Clear();
            var appGroupList = NewFacade.GetAllBusinessServices();
            if (appGroupList != null)
            {
                appGroup.DataSource = appGroupList;
                appGroup.DataTextField = "Name";
                appGroup.DataValueField = "Id";
                appGroup.DataBind();

            }
            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownItemPleaseSelectBusinessServiceAll);
                // appGroup.Items.Insert(0, new ListItem(" All ", "0", true));
                //appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessService);
            }
        }

        public static void PopulateReplicationType(ListControl lstBusinessType, bool addDefaultItem)
        {
            lstBusinessType.Items.Clear();
            foreach (var enmSiteLangItem in Enum.GetValues(typeof(ReplicationType)))
            {
                if ((int)enmSiteLangItem != 0)
                {
                    var oListItem = new ListItem(enmSiteLangItem.ToString(), ((int)enmSiteLangItem).ToString());
                    lstBusinessType.Items.Add(oListItem);
                }
            }
            if (addDefaultItem)
                lstBusinessType.AddDefaultItemWithValue(Constants.UIConstants.DropDownItemPleaseSelectReplicationName);
        }

        public static void PopulateImpactTypeMaster(ListControl lstGroup, bool addDefaultItem)
        {
            lstGroup.Items.Clear();
            var impacttypemasterList = NewFacade.GetAllImpactTypeMaster();
            if (impacttypemasterList == null) return;
            {
                var SortListByOrder = from a in impacttypemasterList orderby a.ImpactOrder ascending select a;

                lstGroup.DataSource = SortListByOrder;
                lstGroup.DataTextField = "ImpactTypeName";
                lstGroup.DataValueField = "ID";
                lstGroup.DataBind();
            }
            if (addDefaultItem)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownItemPleaseSelectImpactTypeMaster);
            }
        }

        public static void PopulateBusinessTimeInterval(ListControl lstGroup, bool addDefaultItem)
        {
            lstGroup.Items.Clear();
            var BusinessTimeIntervalList = NewFacade.GetAllBusinessTimeInterval();
            if (BusinessTimeIntervalList == null) return;
            {
                var SortTimeitervals = from a in BusinessTimeIntervalList orderby a.MinIntervalUnit == 1 && a.MaxIntervalUnit == 1 descending select a;

                var SortTimeitervalsByMinInt = from a in SortTimeitervals orderby a.MinInterval ascending select a;

                lstGroup.DataSource = BusinessTimeIntervalList;
                lstGroup.DataTextField = "TimeIntervalText";
                lstGroup.DataValueField = "TId";
                lstGroup.DataBind();
            }
            //if (addDefaultItem)
            //{
            //    lstGroup.AddDefaultItem(Constants.UIConstants.DropDownItemPleaseSelectImpactTypeMaster);
            //}
        }

        public static void PopulateImpactMaster(ListControl lstGroup, bool addDefaultItem, int ImpactId)
        {
            lstGroup.Items.Clear();
            var impacttypemasterList = NewFacade.GetAllImpactMasterByImpactTypeID(ImpactId);
            if (impacttypemasterList == null) return;
            {
                var SortListByOrder = from a in impacttypemasterList orderby a.ImpactOrder ascending select a;

                lstGroup.DataSource = SortListByOrder;
                lstGroup.DataTextField = "ImpactName";
                lstGroup.DataValueField = "ID";
                lstGroup.DataBind();
            }
            //if (addDefaultItem)
            //{
            //    lstGroup.AddDefaultItem(Constants.UIConstants.DropDownItemPleaseSelectImpactTypeMaster);
            //}
        }
        // Facade.GetAllImpactMasterByImpactTypeID(Convert.ToInt32(ddlImpactType.SelectedValue));

        public static void PopulateReplicationBaseByType(ListControl lstReplicationBase, ReplicationType type, bool addDefaultItem)
        {
            lstReplicationBase.Items.Clear();
            var repListunsorted = NewFacade.GetReplicationBasesByType(type);

            if (repListunsorted != null && repListunsorted.Count > 0)
            {
                var repList = repListunsorted.OrderBy(o => o.Name).ToList();

                lstReplicationBase.DataSource = repList;
                lstReplicationBase.DataTextField = "Name";
                lstReplicationBase.DataValueField = "Id";
                lstReplicationBase.DataBind();
            }

            if (addDefaultItem)
                lstReplicationBase.AddDefaultItemWithValue(Constants.UIConstants.DropDownItemPleaseSelectReplicationName);
        }

        public static void PopulateReplicationBaseByTypeAndRolewithCompanyId(ListControl lstReplicationBase, ReplicationType type, bool addDefaultItem, int companyId, bool isSuperAdmin, bool isParent)
        {
            lstReplicationBase.Items.Clear();
            //var repList = NewFacade.GetReplicationBasesByType(type);

            //  var repList = NewFacade.GetReplicationBasesByTypeAndCompanyAndRole(type, isSuperAdmin,companyId, isParent);
            var repListunsorted = NewFacade.GetReplicationBasesByUserCompanyIdAndRole(companyId, isSuperAdmin, isParent);

            if (repListunsorted != null && repListunsorted.Count > 0)
            {
                var repList = repListunsorted.OrderBy(o => o.Name).ToList();

                lstReplicationBase.DataSource = repList;
                lstReplicationBase.DataTextField = "Name";
                lstReplicationBase.DataValueField = "Id";
                lstReplicationBase.DataBind();
            }

            if (addDefaultItem)
                lstReplicationBase.AddDefaultItemWithValue(Constants.UIConstants.DropDownItemPleaseSelectReplicationName);
        }

        public static void PopulateGroup(ListControl lstGroup, bool addDefaultItem, int loginId, bool GroupAllFlag)
        {
            lstGroup.Items.Clear();
            //var  groupList = NewFacade.GetAllGroups();
            var groupList = GroupAllFlag ? NewFacade.GetAllGroups() : NewFacade.GetGroupsByLoginId(loginId);
            if (groupList != null)
            {
                lstGroup.DataSource = groupList;
                lstGroup.DataTextField = "Name";
                lstGroup.DataValueField = "Id";
                lstGroup.DataBind();
            }
            if (addDefaultItem)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectGroupName);
            }
        }

        public static void PopulateInfraObject(ListControl lstGroup, bool addDefaultItem, int loginId, bool GroupAllFlag)
        {
            lstGroup.Items.Clear();
            //var  groupList = NewFacade.GetAllGroups();
            var groupList = GroupAllFlag ? NewFacade.GetAllInfraObject() : NewFacade.GetInfraObjectByLoginId(loginId);
            if (groupList != null)
            {
                lstGroup.DataSource = groupList;
                lstGroup.DataTextField = "Name";
                lstGroup.DataValueField = "Id";
                lstGroup.DataBind();
            }
            if (addDefaultItem)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectGroupName);
            }
        }

        public static void PopulateGroup(ListControl lstGroup, bool addDefaultItem)
        {
            lstGroup.Items.Clear();
            var groupList = NewFacade.GetAllGroups();
            if (groupList != null)
            {
                lstGroup.DataSource = groupList;
                lstGroup.DataTextField = "Name";
                lstGroup.DataValueField = "Id";
                lstGroup.DataBind();
            }
            if (addDefaultItem)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectGroupName);
            }
        }

        public static void PopulateGroup(DataList lstGroup)
        {
            lstGroup.DataSource = null;
            var groupList = NewFacade.GetAllGroups();
            if (groupList == null) return;
            lstGroup.DataSource = groupList;
            lstGroup.DataBind();
        }

        public static void PopulateGroup(ListView lstGroup)
        {
            lstGroup.DataSource = null;
            var groupList = NewFacade.GetAllGroups();
            if (groupList == null) return;
            lstGroup.DataSource = groupList;
            lstGroup.DataBind();
        }

        public static void PopulateGroupByParentAndRole(ListView lstGroup, int companyId, bool isSuperAdmin, bool isParent)
        {
            lstGroup.DataSource = null;
            var groupList = NewFacade.GetAllGroupsByUserCompanyIdAndRoleAndIsSuperAdmin(companyId, isSuperAdmin, isParent);
            if (groupList == null) return;
            lstGroup.DataSource = groupList;
            lstGroup.DataBind();
        }

        public static void PopulateGroupByParentAndRole(ListControl lstGroup, int companyId, bool isSuperAdmin, bool isParent, bool addDefaultItem)
        {
            var lstGroups = NewFacade.GetAllGroupsByUserCompanyIdAndRoleAndIsSuperAdmin(companyId, isSuperAdmin, isParent);
            if (lstGroups != null)
            {
                lstGroup.DataSource = lstGroups;
                lstGroup.DataTextField = "Name";
                lstGroup.DataValueField = "Id";
                lstGroup.DataBind();
            }
            if (addDefaultItem)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectGroupName);
            }
        }

        public static void PopulateInfrObjectByParentAndRole(ListControl lstInfra, int companyId, bool isSuperAdmin, bool isParent, bool addDefaultItem)
        {
            var lstGroups = NewFacade.GetAllInfraObjectByUserCompanyIdAndRoleAndIsSuperAdmin(companyId, isSuperAdmin, isParent);
            if (lstGroups != null)
            {
                lstInfra.DataSource = lstGroups;
                lstInfra.DataTextField = "Name";
                lstInfra.DataValueField = "Id";
                lstInfra.DataBind();
            }
            if (addDefaultItem)
            {
                lstInfra.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectGroupName);
            }
        }

        public static void PopulateGroupByAllGroupFlagAndUserCopanyInfo(ListControl lstGroup, int userId, int companyId, UserRole role, bool isParent, bool isAllgroup, bool addDefaultItem)
        {
            var lstGroups = NewFacade.GetGroupsByUserIdCompanyIdRoleAndGroupFlag(userId, companyId, role, isParent, isAllgroup);
            if (lstGroups != null)
            {
                lstGroup.DataSource = lstGroups;
                lstGroup.DataTextField = "Name";
                lstGroup.DataValueField = "Id";
                lstGroup.DataBind();
            }
            if (addDefaultItem)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectGroupName);
            }
        }

        public static void PopulateGroupByApplicationId(ListControl lstGroup, int applicationId, bool addDefaultItem)
        {
            lstGroup.Items.Clear();

            var groupList = NewFacade.GetGroupsByBusinessServiceId(applicationId);
            if (groupList == null) return;
            {
                lstGroup.DataSource = groupList;
                lstGroup.DataTextField = "Name";
                lstGroup.DataValueField = "Id";
                lstGroup.DataBind();
            }
            if (addDefaultItem)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectGroupName);
            }
        }

        public static void PopulateBusinessFunctionByBusinessServiceId(ListControl lstGroup, int applicationId, bool addDefaultItem)
        {
            lstGroup.Items.Clear();
            if (applicationId > 0)
            {
                var i = 1;
                if (addDefaultItem)
                {
                    lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessFunction);
                }
                var groupList = NewFacade.GetBusinessFunctionsByBusinessServiceId(applicationId);
                if (groupList == null) return;
                {
                    foreach (var bfbs in groupList)
                    {
                        lstGroup.InsertItem(i, bfbs.Name, bfbs.Id.ToString());
                        i++;
                    }
                    //lstGroup.DataSource = groupList;
                    //lstGroup.DataTextField = "Name";
                    //lstGroup.DataValueField = "Id";
                    //lstGroup.DataBind();
                }
            }
            else if (applicationId == 0)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessFunction);
            }
        }

        public static void PopulateBusinessFunctionByBusinessServiceId(ListControl lstGroup, int applicationId, bool addDefaultItem, bool IsSuperAdmin, int LoggedInUserId)
        {
            lstGroup.Items.Clear();
            if (applicationId > 0)
            {
                var i = 1;
                if (addDefaultItem)
                {
                    lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessFunction);
                }
                var groupList = IsSuperAdmin ? NewFacade.GetBusinessFunctionsByBusinessServiceId(applicationId) : NewFacade.GetBusinessFunctionsByBusinessServiceId(applicationId, LoggedInUserId);

                if (groupList == null) return;
                {
                    foreach (var bfbs in groupList)
                    {
                        lstGroup.InsertItem(i, bfbs.Name, bfbs.Id.ToString());
                        i++;
                    }
                    //lstGroup.DataSource = groupList;
                    //lstGroup.DataTextField = "Name";
                    //lstGroup.DataValueField = "Id";
                    //lstGroup.DataBind();
                }
            }
            else if (applicationId == 0)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessFunction);
            }
        }

        public static void PopulateInfraObjectByBusinessFunctionId(ListControl lstGroup, int businessserviceId, int businessfunctionId, bool addDefaultItem)
        {
            lstGroup.Items.Clear();
            if (addDefaultItem)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName);
            }
            var i = 1;
            var groupList = NewFacade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(businessserviceId, businessfunctionId);
            if (groupList == null) return;
            {
                foreach (var iobsbf in groupList)
                {
                    lstGroup.InsertItem(i, iobsbf.Name, iobsbf.Id.ToString());
                    i++;
                }
                //lstGroup.DataSource = groupList;
                //lstGroup.DataTextField = "Name";
                //lstGroup.DataValueField = "Id";
                //lstGroup.DataBind();
            }
        }

        public static void PopulateInfraObjectByBusinessFunctionId(ListControl lstGroup, int businessserviceId, int businessfunctionId, bool addDefaultItem, bool isSuperAdmin, int loggedInUserId)
        {
            lstGroup.Items.Clear();
            if (addDefaultItem)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName);
            }
            var i = 1;
            var groupList = isSuperAdmin ? NewFacade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(businessserviceId, businessfunctionId) : NewFacade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionIdUserId(businessserviceId, businessfunctionId, loggedInUserId);
            if (groupList == null) return;
            {
                foreach (var iobsbf in groupList)
                {
                    lstGroup.InsertItem(i, iobsbf.Name, iobsbf.Id.ToString());
                    i++;
                }
                //lstGroup.DataSource = groupList;
                //lstGroup.DataTextField = "Name";
                //lstGroup.DataValueField = "Id";
                //lstGroup.DataBind();
            }
        }

        public static void PopulateInfraObjectDefault(ListControl lstGroup, int businessserviceId, int businessfunctionId, bool addDefaultItem)
        {
            lstGroup.Items.Clear();

            if (businessserviceId == 0 && businessfunctionId == 0)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName);
            }
            else
            {
                var groupList = NewFacade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(businessserviceId, businessfunctionId);
                if (groupList == null) return;
                {
                    lstGroup.DataSource = groupList;
                    lstGroup.DataTextField = "Name";
                    lstGroup.DataValueField = "Id";
                    lstGroup.DataBind();
                }
                if (addDefaultItem)
                {
                    lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName);
                }
            }
        }

        public static void PopulateAppGroup(ListView lvAppgroupList)
        {
            lvAppgroupList.DataSource = null;
            var groupappList = NewFacade.GetAllApplicationGroups();
            if (groupappList == null) return;
            lvAppgroupList.DataSource = groupappList;
            lvAppgroupList.DataBind();
        }

        public static void PopulateCompanyProfile(ListControl lstCompany, bool addDefaultItem)
        {
            lstCompany.Items.Clear();

            var companyList = NewFacade.GetAllCompanyProfiles();
            if (companyList != null)
            {
                var companyListByOrder = from a in companyList orderby a.DisplayName ascending select a;

                lstCompany.DataSource = companyListByOrder;
                lstCompany.DataTextField = "DisplayName";
                lstCompany.DataValueField = "Id";
                lstCompany.DataBind();
            }
            if (addDefaultItem)
            {
                lstCompany.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectCompanyCode);
            }
        }

        public static void PopulateCompanyByCompanyIdAndRole(ListControl lstCompany, int companyId, bool isSuperAdmin, bool isParent, bool addDefaultItem)
        {
            lstCompany.Items.Clear();

            var companyList = NewFacade.GetCompanyProfileByUserCompanyIdAndRole(companyId, isSuperAdmin, isParent);
            if (companyList != null)
            {
                var companyListByOrder = from a in companyList orderby a.DisplayName ascending select a;
                lstCompany.DataSource = companyListByOrder;
                lstCompany.DataTextField = "DisplayName";
                lstCompany.DataValueField = "Id";
                lstCompany.DataBind();
            }
            if (addDefaultItem)
            {
                lstCompany.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectCompanyName);
            }
        }

        public static void PopulateCompanyByParentAndRole(ListControl lstCompany, int companyId, bool isSuperAdmin, bool isParent, bool addDefaultItem)
        {
            lstCompany.Items.Clear();

            var companyList = NewFacade.GetCompanyProfilesByParentAndRole(companyId, isSuperAdmin, isParent);
            if (companyList != null)
            {
                var companyListByOrder = from a in companyList orderby a.DisplayName ascending select a;

                lstCompany.DataSource = companyListByOrder;
                lstCompany.DataTextField = "DisplayName";
                lstCompany.DataValueField = "Id";
                lstCompany.DataBind();
            }
            if (addDefaultItem)
            {
                lstCompany.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectCompanyName);
            }
        }

        public static void PopulateSiteByCompanyIdAndRole(ListControl lstSite, int companyId, bool isSuperAdmin, bool isParent, bool addDefaultItem)
        {
            lstSite.Items.Clear();

            var siteList = NewFacade.GetSitesByUserCompanyIdAndRole(companyId, isSuperAdmin, isParent);
            if (siteList != null)
            {

                var siteListByOrder = from a in siteList orderby a.Name ascending select a;

                lstSite.DataSource = siteListByOrder;
                lstSite.DataTextField = "Name";
                lstSite.DataValueField = "Id";
                lstSite.DataBind();
            }
            if (addDefaultItem)
            {
                lstSite.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectSiteName);
            }
        }

        //Below Method is Added For Binding Only PRSite For Configure Business Service 
        //(Bug No. CPROOT-1212 : While configuring Business Service it is displaying PR and DR site, Expected Result:--- It should display only PR site under site selection drop down list)

        public static void PopulateSiteByType(ListControl lstSite, int companyId, bool isSuperAdmin, bool isParent, bool addDefaultItem)
        {
            lstSite.Items.Clear();

            var siteList = NewFacade.GetSitesByUserCompanyIdAndRole(companyId, isSuperAdmin, isParent);

            if (siteList != null)
            {
                var filterPRsite = from fltrsite in siteList
                                   where fltrsite.Type == SiteType.PRSite
                                   select fltrsite;

                if (filterPRsite != null)
                {
                    lstSite.DataSource = filterPRsite;
                    lstSite.DataTextField = "Name";
                    lstSite.DataValueField = "Id";
                    lstSite.DataBind();
                }
            }
            if (addDefaultItem)
            {
                lstSite.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectSiteName);
            }
        }

        public static void PopulateInfraobjects(ListControl lstGroup, bool addDefaultItem)
        {
            lstGroup.Items.Clear();
            var infraList = NewFacade.GetAllInfraObject();
            if (infraList != null)
            {
                var infraListByOrder = from a in infraList orderby a.Name ascending select a;

                lstGroup.DataSource = infraListByOrder;
                lstGroup.DataTextField = "Name";
                lstGroup.DataValueField = "Id";
                lstGroup.DataBind();
            }
            if (addDefaultItem)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName);
            }
        }

        public static void PopulateUsers(ListControl lstItem, bool addDefaultItem)
        {
            lstItem.Items.Clear();
            var userlist = NewFacade.GetAllUsers();
            if (userlist != null)
            {
                var userlistByOrder = from a in userlist orderby a.UserName ascending select a;

                lstItem.DataSource = userlistByOrder;
                lstItem.DataTextField = "UserName";
                lstItem.DataValueField = "Id";
                lstItem.DataBind();
            }
            if (addDefaultItem)
            {
                lstItem.AddDefaultItem(Constants.UIConstants.DropDownItemPleaseSelectUser);
            }
        }

        public static void PopulateServer(ListControl lstServer, bool addDefaultItem)
        {
            lstServer.Items.Clear();
            var serverList = NewFacade.GetAllServers();
            if (serverList != null)
            {
                var serverListByOrder = from a in serverList orderby a.Name ascending select a;

                lstServer.DataSource = serverListByOrder;
                lstServer.DataTextField = "Name";
                lstServer.DataValueField = "Id";
                lstServer.DataBind();
            }
            if (addDefaultItem)
            {
                lstServer.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName);
            }
        }

        public static void PopulateWorkflows(ListControl lstWorkflows, bool addDefaultItem)
        {
            lstWorkflows.Items.Clear();
            var workflowList = NewFacade.GetAllWorkflows();
            if (workflowList != null)
            {
                var workflowListByOrder = from a in workflowList orderby a.Name ascending select a;

                lstWorkflows.DataSource = workflowListByOrder;
                lstWorkflows.DataTextField = "Name";
                lstWorkflows.DataValueField = "Id";
                lstWorkflows.DataBind();
            }
            if (addDefaultItem)
            {
                lstWorkflows.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectWorkflowName);
            }
        }

        public static void PopulateWorkflows(ListControl lstWorkflows, bool addDefaultItem, int chkval, int infraid)
        {
            lstWorkflows.Items.Clear();
            //var workflowList = NewFacade.GetAllWorkflows();
            var workflowList = NewFacade.GetExistingWorkflowbyInfraobject(infraid);
            var grpwrkflwlist = NewFacade.GetAllGroupWorkflows();
            if (workflowList != null && grpwrkflwlist != null)
            {
                //var workflowListByOrder = from a in workflowList orderby a.Name ascending  select a;


                if (chkval == 1)
                {
                    //HashSet<int> diffids = new HashSet<int>(grpwrkflwlist.Select(s => s.WorkflowId && s.ActionType==7));
                    //var results = workflowList.Where(m => diffids.Contains(m.Id)).ToList();

                    //var _workflowListByOrder = (from a in workflowList join b in grpwrkflwlist on a.Id equals b.WorkflowId where b.ActionType == 7 select a).ToList();
                    var _workflowListByOrder = (from a in workflowList join b in grpwrkflwlist on a.Id equals b.WorkflowId where b.ActionType == 7 select a).Distinct().ToList();

                    lstWorkflows.DataSource = _workflowListByOrder;
                    lstWorkflows.DataTextField = "Name";
                    lstWorkflows.DataValueField = "Id";
                    lstWorkflows.DataBind();
                }
                else
                {

                    var _workflowListByOrder = (from a in workflowList join b in grpwrkflwlist on a.Id equals b.WorkflowId where b.ActionType == 8 select a).Distinct().ToList();



                    lstWorkflows.DataSource = _workflowListByOrder;
                    lstWorkflows.DataTextField = "Name";
                    lstWorkflows.DataValueField = "Id";
                    lstWorkflows.DataBind();
                }

            }

            if (addDefaultItem)
            {
                lstWorkflows.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectWorkflowName);
            }
        }

        public static void PopulateDataSyncProperties(ListControl lstDSProperties, bool addDefaultItem)
        {
            lstDSProperties.Items.Clear();
            if (addDefaultItem)
            {
                lstDSProperties.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectDataSyncProperties);
            }
            var dsPropertiesList = NewFacade.GetAllDataSyncProperties();
            if (dsPropertiesList != null)
            {
                int i = 1;
                var dsPropListByOrder = from a in dsPropertiesList orderby a.Name ascending select a;
                foreach (var prop in dsPropListByOrder)
                {
                    lstDSProperties.InsertItem(i, prop.Name, prop.Id.ToString());
                    //lstDSProperties.DataTextField = "Name";
                    //lstDSProperties.DataValueField = "Id";
                    //lstDSProperties.DataBind();
                    i++;
                }
            }
        }

        public static void PopulateServerByTypeAndRole(ListControl lstControl, string type, int companyId, bool isSuperAdmin, bool isParent, bool addDefaultItem)
        {
            lstControl.Items.Clear();

            var serverList = NewFacade.GetServersByUserCompanyIdAndRole(companyId, isSuperAdmin, isParent);

            if (serverList != null)
            {
                var serverListByOrder = from a in serverList orderby a.Name ascending select a;
                foreach (Server server in serverListByOrder.Where(server => server.Type == type))
                {
                    lstControl.Items.Add(new ListItem(server.Name, server.Id.ToString()));
                }
                if (addDefaultItem)
                {
                    lstControl.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName);
                }
            }
        }

        public static void PopulateServerByTypeAndRolewithCompanyId(ListControl lstControl, string type, int companyId, bool isSuperAdmin, bool isParent, bool addDefaultItem)
        {
            lstControl.Items.Clear();

            var serverListunsorted = NewFacade.GetServersByUserCompanyIdAndRole(companyId, isSuperAdmin, isParent);

            if (serverListunsorted != null && serverListunsorted.Count > 0)
            {
                var serverList = serverListunsorted.OrderBy(o => o.Name).ToList();
                if (serverList != null)
                {
                    var serverListByOrder = from a in serverList orderby a.Name ascending select a;
                    foreach (Server server in serverListByOrder)
                    {
                        if (!server.Type.StartsWith(type))
                        {
                            lstControl.Items.Add(new ListItem(server.Name, server.Id.ToString()));
                        }
                    }
                    if (addDefaultItem)
                    {
                        lstControl.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName);
                    }
                }
            }
        }

        public static void PopulateServerByTypeAndAdminRole(ListControl lstControl, string type, int userId, int compId, string role, bool addDefaultItem)
        {
            lstControl.Items.Clear();

            var serverListunsorted = NewFacade.GetServersListByUserinfraId(userId, compId, role);

            if (serverListunsorted != null && serverListunsorted.Count > 0)
            {
                var serverList = serverListunsorted.OrderBy(o => o.Name).ToList();
                if (serverList != null)
                {
                    var serverListByOrder = from a in serverList orderby a.Name ascending select a;
                    foreach (Server server in serverListByOrder)
                    {
                        if (!server.Type.StartsWith(type))
                        {
                            lstControl.Items.Add(new ListItem(server.Name, server.Id.ToString()));
                        }
                    }
                    if (addDefaultItem)
                    {
                        lstControl.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName);
                    }
                }
            }
        }

        public static void PopulateServerByType(ListControl lstControl, string prtype, string type, bool addDefaultItem)
        {
            lstControl.Items.Clear();
            var serverList = NewFacade.GetAllServers();

            if (serverList != null)
            {
                var serverListByOrder = from a in serverList orderby a.Name ascending select a;
                foreach (Server server in serverListByOrder)
                {
                    if (!server.Type.StartsWith(type))
                    {
                        lstControl.Items.Add(new ListItem(server.Name, server.Id.ToString()));
                    }
                    //lstControl.Items.Add(new ListItem(server.Name, server.Id.ToString()));
                }
                if (addDefaultItem)
                {
                    lstControl.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName);
                }
            }
        }

        public static void PopulateServer(ListControl lstControl, string prtype, string type, bool addDefaultItem)
        {
            lstControl.Items.Clear();
            var serverList = NewFacade.GetAllServers();

            if (serverList != null)
            {
                var serverListByOrder = from a in serverList orderby a.Name ascending select a;
                foreach (Server server in serverListByOrder)
                {
                    //if (!server.Type.StartsWith(type))
                    //{
                    //    lstControl.Items.Add(new ListItem(server.Name, server.Id.ToString()));
                    //}
                    lstControl.Items.Add(new ListItem(server.Name, server.Id.ToString()));
                }
                if (addDefaultItem)
                {
                    lstControl.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName);
                }
            }
        }

        public static void PopulateDBServerByRoleAndCompanyIdWithType(ListControl lstControl, bool isSuperAdmin, int companyId, bool isParent, bool addDefaultItem)
        {
            lstControl.Items.Clear();

            var serverList = NewFacade.GetServersByUserCompanyIdAndRole(companyId, isSuperAdmin, isParent);

            if (serverList != null)
            {
                var serverListByOrder = from a in serverList orderby a.Name ascending select a;
                foreach (Server server in serverListByOrder)
                {
                    if (server.Type.ToLower() == "prdbserver" || server.Type.ToLower() == "drdbserver" || server.Type.ToLower() == "neardrdbserver")
                    {
                        lstControl.Items.Add(new ListItem(server.Name, server.Id.ToString() + "~" + server.Type));
                    }
                }
                if (addDefaultItem)
                {
                    lstControl.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName);
                }
            }
        }

        public static void PopulateDBServerByRoleAndCompanyIdWithUserId(ListControl lstControl, int userId, int companyId, string role, bool addDefaultItem)
        {
            lstControl.Items.Clear();

            var serverList = NewFacade.GetServersListByUserinfraId(userId, companyId, role);

            if (serverList != null)
            {
                var serverListByOrder = from a in serverList orderby a.Name ascending select a;
                foreach (Server server in serverListByOrder)
                {
                    if (server.Type.ToLower() == "prdbserver" || server.Type.ToLower() == "drdbserver")
                    {
                        lstControl.Items.Add(new ListItem(server.Name, server.Id.ToString() + "~" + server.Type));
                    }
                }
                if (addDefaultItem)
                {
                    lstControl.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName);
                }
            }
        }

        public static void PopulateVersionByDBType(ListControl lstControl, int dbType, bool addDefaultItem)
        {
            lstControl.Items.Clear();
            var dbList = NewFacade.GetAllDatabaseVersions();
            if (dbList != null)
            {
                foreach (var ver in dbList.Where(ver => ver.DataBaseTypeId == dbType))
                {
                    lstControl.Items.Add(new ListItem(ver.Name, ver.Name));
                }
                if (addDefaultItem)
                {
                    lstControl.AddDefaultItem(Constants.UIConstants.DropDownItemPleaseSelectDBVersion);
                }
            }
        }

        public static void PopulateServerByAddress(ListControl lstServer, bool addDefaultItem)
        {
            lstServer.Items.Clear();
            var serverList = NewFacade.GetAllServers();
            if (serverList != null)
            {
                //lstServer.DataSource = serverList;

                var serverListByOrder = from a in serverList orderby a.Name ascending select a;

                foreach (var server in serverListByOrder)
                {
                    lstServer.Items.Add(new ListItem(server.Name, CryptographyHelper.Md5Decrypt(server.IPAddress)));
                }
                lstServer.DataBind();
            }
            if (addDefaultItem)
            {
                lstServer.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName);
            }
        }

        public static void PopulateSite(ListControl lstSite, string type, bool addDefaultItem)
        {
            //lstSite.Items.Clear();
            //var siteList = NewFacade.GetAllSite();
            //if (siteList == null) return;
            //foreach (var site in siteList.Where(site => site.Type==type))
            //{
            //    lstSite.Items.Add(new ListItem(site.Name,site.Id.ToString()));
            //    lstSite.DataBind();
            //}
            //if (addDefaultItem)
            //{
            //    lstSite.AddDefaultItem(Constants.UIConstants.DROP_DOWNL_ITEM_PLEASE_SELECT_SITE_NAME);
            //}
        }

        public static void PopulateSite(ListControl lstSite, bool addDefaultItem)
        {
            lstSite.Items.Clear();
            var siteList = NewFacade.GetAllSites();
            if (siteList != null)
            {
                var siteListByOrder = from a in siteList orderby a.Name ascending select a;
                lstSite.DataSource = siteListByOrder;
                lstSite.DataTextField = "Name";
                lstSite.DataValueField = "Id";
                lstSite.DataBind();
            }
            if (addDefaultItem)
            {
                lstSite.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectSiteName);
            }
        }

        public static void PopulateDatabase(ListControl lstDatabase, bool isSuperAdmin, int companyId, bool isParent, bool addDefaultItem)
        {
            lstDatabase.Items.Clear();

            IList<DatabaseBase> bases = isSuperAdmin ? NewFacade.GetAllDatabaseBases() : NewFacade.GetAllDatabaseByUserCompanyId(companyId, isSuperAdmin, isParent);

            if (bases != null)
            {
                var basesListByOrder = from a in bases orderby a.Name ascending select a;
                lstDatabase.DataSource = basesListByOrder;
                lstDatabase.DataTextField = "Name";
                lstDatabase.DataValueField = "Id";
                lstDatabase.DataBind();
            }
            if (addDefaultItem)
            {
                lstDatabase.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectSiteName);
            }
        }

        public static void PopulateDatabaseByServerID(ListControl lstDatabase, int serverId, string dbType, bool addDefaultItem)
        {
            lstDatabase.Items.Clear();

            IList<DatabaseBase> bases = NewFacade.GetDatabaseBasesByServerIdAndType(serverId, dbType);
            if (bases != null)
            {
                if (bases.Count > 0)
                {

                    var basesListByOrder = from a in bases orderby a.Name ascending select a;

                    //var dbList = bases.Where(db => db.Type == dbType).ToList();
                    //if (dbList.Count > 0)
                    //{
                    lstDatabase.DataSource = basesListByOrder;
                    lstDatabase.DataTextField = "Name";
                    lstDatabase.DataValueField = "Id";
                    lstDatabase.DataBind();
                    //}
                }
            }
            if (addDefaultItem)
            {
                lstDatabase.AddDefaultItem(Constants.UIConstants.DropDownItemPleaseSelectDBName);
            }
        }
        public static void PopulateDatabaseByDBType(ListControl lstDatabase, string dbtype, string Type, bool addDefaultItem)
        {
            lstDatabase.Items.Clear();

            IList<DatabaseBase> bases = NewFacade.GetByDBType(dbtype, Type);
            if (bases != null)
            {
                if (bases.Count > 0)
                {
                    //var dbList = bases.Where(db => db.Type == dbType).ToList();
                    //if (dbList.Count > 0)
                    //{
                    lstDatabase.DataSource = bases;
                    lstDatabase.DataTextField = "Name";
                    lstDatabase.DataValueField = "Id";
                    lstDatabase.DataBind();
                    //}
                }
            }
            if (addDefaultItem)
            {
                lstDatabase.AddDefaultItem(Constants.UIConstants.DropDownItemPleaseSelectDBName);
            }
        }

        public static void PopulateApplicationGroup(ListControl appGroup, bool addDefaultItem)
        {
            appGroup.Items.Clear();
            var appGroupList = NewFacade.GetAllApplicationGroups();
            if (appGroupList != null)
            {
                var appGroupListByOrder = from a in appGroupList orderby a.Name ascending select a;
                appGroup.DataSource = appGroupListByOrder;
                appGroup.DataTextField = "Name";
                appGroup.DataValueField = "Id";
                appGroup.DataBind();
            }
            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectApplicationName);
            }
        }

        public static void PopulateBusinessService(ListControl appGroup, bool addDefaultItem)
        {
            appGroup.Items.Clear();
            var appGroupList = NewFacade.GetAllBusinessServices();
            if (appGroupList != null)
            {
                var appGroupListByOrder = from a in appGroupList orderby a.Name ascending select a;
                appGroup.DataSource = appGroupListByOrder;
                appGroup.DataTextField = "Name";
                appGroup.DataValueField = "Id";
                appGroup.DataBind();
            }
            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessService);
            }
        }

        public static void PopulateBusinessService(ListControl appGroup, bool addDefaultItem, bool IsSuperAdmin, int loggedInUserId)
        {
            appGroup.Items.Clear();
            var appGroupList = IsSuperAdmin ? NewFacade.GetAllBusinessServices() : NewFacade.GetBusinessServicesByUserId(loggedInUserId);
            if (appGroupList != null)
            {
                var appGroupListByOrder = from a in appGroupList orderby a.Name ascending select a;
                appGroup.DataSource = appGroupListByOrder;
                appGroup.DataTextField = "Name";
                appGroup.DataValueField = "Id";
                appGroup.DataBind();
            }
            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessService);
            }
        }

        public static void PopulateBusinessServiceByCompanyIdAndRole(ListControl appGroup, bool addDefaultItem, bool IsUserSuperAdmin, int LoggedInUserCompanyId, CompanyProfile LoggedInUserCompany, UserRole LoggedInUserRole)
        {
            appGroup.Items.Clear();
            var appGroupList = IsUserSuperAdmin ? NewFacade.GetAllBusinessServices() : NewFacade.GetBusinessServiceByCompanyIdAndRole(LoggedInUserCompanyId, LoggedInUserCompanyId, LoggedInUserRole, LoggedInUserCompany.IsParent, true);
            if (appGroupList != null)
            {
                var appGroupListByOrder = from a in appGroupList orderby a.Name ascending select a;
                appGroup.DataSource = appGroupListByOrder;
                appGroup.DataTextField = "Name";
                appGroup.DataValueField = "Id";
                appGroup.DataBind();
            }
            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessService);
            }
        }

        public static void Populat(ListControl appGroup, bool addDefaultItem)
        {
            appGroup.Items.Clear();

            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectAuthentication);
            }
        }

        public static void PopulateSingleSignOnType(ListControl lstsinglesignontype, bool addDefultItem)
        {
            lstsinglesignontype.Items.Clear();

            foreach (var enmSiteLangItem in Enum.GetValues(typeof(SingleSignOnType)))
            {
                if ((int)enmSiteLangItem != 0)
                {
                    var oListItem = new ListItem(enmSiteLangItem.ToString(), ((int)enmSiteLangItem).ToString());
                    if (oListItem.Text == "TPAM" || oListItem.Text == "CyberArk" || oListItem.Text == "Arcos")
                    {
                        lstsinglesignontype.Items.Add(oListItem);
                    }
                }
            }
            if (addDefultItem)
                lstsinglesignontype.AddDefaultItemWithValue(Constants.UIConstants.DropDownlItemPleaseSelectBusinessType);
        }

        public static void PopulateBusinessFunction(ListControl appGroup, bool addDefaultItem, bool IsUserSuperAdmin, int LoggedInUserCompanyId, CompanyProfile LoggedInUserCompany)
        {
            appGroup.Items.Clear();
            var appGroupList = IsUserSuperAdmin ? NewFacade.GetAllBusinessFunctions() : NewFacade.GetAllBusinessFunctionByCompanyIdAndRole(LoggedInUserCompanyId, LoggedInUserCompany.IsParent);
            if (appGroupList != null)
            {
                var appGroupListByOrder = from a in appGroupList orderby a.Name ascending select a;
                appGroup.DataSource = appGroupListByOrder;
                appGroup.DataTextField = "Name";
                appGroup.DataValueField = "Id";
                appGroup.DataBind();
            }
            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessFunction);
            }
        }

        public static void PopulateASMInstance(ListControl appGroup, bool addDefaultItem)
        {
            appGroup.Items.Clear();
            var appGroupList = NewFacade.GetAllASMGrid();
            if (appGroupList != null)
            {
                var appGroupListByOrder = from a in appGroupList orderby a.ASMInstanceName ascending select a;
                appGroup.DataSource = appGroupListByOrder;
                appGroup.DataTextField = "ASMInstanceName";
                appGroup.DataValueField = "Id";
                appGroup.DataBind();
            }
            if (addDefaultItem)
            {
                appGroup.AddDefaultItem("- Select ASM-Instance - ");
            }
        }

        public static void PopulateImpactRating(ListControl lstType)
        {
            string[] enumNames = Enum.GetNames(typeof(ImpactRating));
            lstType.Items.Clear();
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(ImpactRating), item);
                ListItem listItem = new ListItem(item, value.ToString());
                lstType.Items.Add(listItem);
            }
            lstType.Items.Insert(0, new ListItem("Select", "0"));
        }

        public static void PopulateImpactSeverity(ListControl lstBusinessType, bool addDefaultItem)
        {
            lstBusinessType.Items.Clear();
            foreach (var enmSiteLangItem in Enum.GetValues(typeof(ImpactSeverity)))
            {
                if ((int)enmSiteLangItem != 0)
                {
                    var oListItem = new ListItem(enmSiteLangItem.ToString(), ((int)enmSiteLangItem).ToString());
                    lstBusinessType.Items.Add(oListItem);
                }
            }
            if (addDefaultItem)
                lstBusinessType.AddDefaultItemWithValue(Constants.UIConstants.DropDownlItemPleaseSelectImpact);
        }

        public static void PopulateImpactRelType(ListControl lstImpactRelType, bool addDefaultItem)
        {
            lstImpactRelType.Items.Clear();
            var ImpactRelTypeList = NewFacade.GetAllImpactRelType();

            var IsExistImpacttype = ImpactRelTypeList.Where(s => s.RelTypeValue == "NA").ToList();

            if (IsExistImpacttype != null && IsExistImpacttype.Count > 0)
            {
                var itemToRemove = ImpactRelTypeList.Single(r => r.RelTypeValue == "NA");
                if (itemToRemove != null)
                    ImpactRelTypeList.Remove(itemToRemove);
            }


            if (ImpactRelTypeList != null)
            {
                lstImpactRelType.DataSource = ImpactRelTypeList;
                lstImpactRelType.DataTextField = "RelTypeDescription";
                lstImpactRelType.DataValueField = "ID";
                lstImpactRelType.DataBind();
            }
            if (addDefaultItem)
            {
                lstImpactRelType.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectImpact);
            }
        }

        public static string CheckImpactRating(Label lblImapactRating)
        {
            if (lblImapactRating.Text.Equals(((int)ImpactRating.Insignificant).ToString()))
                lblImapactRating.Text = ImpactRating.Insignificant.ToString();
            else if (lblImapactRating.Text.Equals(((int)ImpactRating.Major).ToString()))
            {
                lblImapactRating.Text = ImpactRating.Major.ToString();
            }
            else if (lblImapactRating.Text.Equals(((int)ImpactRating.Minor).ToString()))
            {
                lblImapactRating.Text = ImpactRating.Minor.ToString();
            }
            else if (lblImapactRating.Text.Equals(((int)ImpactRating.Severe).ToString()))
            {
                lblImapactRating.Text = ImpactRating.Severe.ToString();
            }
            else if (lblImapactRating.Text.Equals(((int)ImpactRating.Negligible).ToString()))
            {
                lblImapactRating.Text = ImpactRating.Negligible.ToString();
            }
            return lblImapactRating.Text;
        }

        public static string CheckImpactRatingForTextBox(TextBox txtImapactRating)
        {
            string lblImpactRating = string.Empty;
            if (txtImapactRating.Text.Equals(((int)ImpactRating.Insignificant).ToString()))
                lblImpactRating = ImpactRating.Insignificant.ToString();
            else if (txtImapactRating.Text.Equals(((int)ImpactRating.Major).ToString()))
            {
                lblImpactRating = ImpactRating.Major.ToString();
            }
            else if (txtImapactRating.Text.Equals(((int)ImpactRating.Minor).ToString()))
            {
                lblImpactRating = ImpactRating.Minor.ToString();
            }
            else if (txtImapactRating.Text.Equals(((int)ImpactRating.Severe).ToString()))
            {
                lblImpactRating = ImpactRating.Severe.ToString();
            }
            else if (txtImapactRating.Text.Equals(((int)ImpactRating.Negligible).ToString()))
            {
                lblImpactRating = ImpactRating.Negligible.ToString();
            }
            return lblImpactRating;
        }

        public static string GetFormattedRTO(int Hours)
        {
            var dHours = Convert.ToDecimal(Hours);
            var hours = Math.Floor(dHours);
            var week = Math.Floor(dHours / (24 * 7));
            var mth = (int)Math.Floor(dHours / (24 * 30));
            var d = (int)Math.Floor(dHours / 24);
            var h = (int)Math.Floor(hours - (d * 24));
            var timeFormat = "";
            if (dHours == 0)
            {
                timeFormat = string.Empty;
            }
            if (dHours > 0 && dHours < Convert.ToInt32(RTO.Upto24Hrs))
            {
                timeFormat = h + "Hrs";
            }
            if (dHours >= Convert.ToInt32(RTO.Upto24Hrs) && dHours <= Convert.ToInt32(RTO.Upto72Hrs))
            {
                if (d == 1)
                {
                    timeFormat = d + "Day";
                }
                else
                {
                    timeFormat = d + "Days";
                }
            }
            if (dHours > Convert.ToInt32(RTO.Upto72Hrs) && dHours < Convert.ToInt32(RTO.Upto1Month))
            {
                if (week == 1)
                {
                    timeFormat = week + "Week";
                }
                else
                {
                    timeFormat = week + "Weeks";
                }
            }
            if (dHours >= Convert.ToInt32(RTO.Upto1Month))
            {
                if (dHours == Convert.ToInt32(RTO.Upto1Month))
                {
                    timeFormat = mth + "Month";
                }
                else
                {
                    timeFormat = "More than 1 Month";
                }
            }
            return timeFormat;
        }

        public static void PopulateRTO(ListControl lstType)
        {
            string[] enumNames = Enum.GetNames(typeof(RTO));
            lstType.Items.Clear();
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(RTO), item);
                ListItem listItem = new ListItem(item, value.ToString());
                lstType.Items.Add(listItem);
            }
            lstType.Items.Insert(0, new ListItem("Select", "0"));
        }

        public static void PopulateBussinessFunctionRTO(ListControl lstType)
        {
            string[] enumNames = Enum.GetNames(typeof(BusinessFunctionRTO));
            lstType.Items.Clear();
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(BusinessFunctionRTO), item);
                ListItem listItem = new ListItem(item, value.ToString());
                lstType.Items.Add(listItem);
            }
            lstType.Items.Insert(0, new ListItem("Select", "0"));
        }

        public static void ParallelWorkflowProfile(ListControl appGroup, bool addDefaultItem)
        {
            appGroup.Items.Clear();
            var pProfileList = NewFacade.GetAllParallelProfile();
            if (pProfileList != null)
            {
                var profileListByOrder = from a in pProfileList orderby a.ProfileName ascending select a;
                appGroup.DataSource = profileListByOrder;
                appGroup.DataTextField = "ProfileName";
                appGroup.DataValueField = "Id";
                appGroup.DataBind();
            }
            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectParallalprofile);
            }
        }
        public static void ParallelWorkflowProfileCustom(ListControl appGroup, bool addDefaultItem, bool isSuperAdmin, int LoggedInUserId)
        {
            appGroup.Items.Clear();
            List<ParallelProfile> lstprof = new List<ParallelProfile>();

            var pProfileList = NewFacade.GetAllParallelProfile_Custom(LoggedInUserId);
            //var pProfileList = NewFacade.GetAllParallelProfile() ;
            if (pProfileList != null)
            {
                var profileListByOrder = from a in pProfileList orderby a.ProfileName ascending select a;
                appGroup.DataSource = profileListByOrder;
                appGroup.DataTextField = "ProfileName";
                appGroup.DataValueField = "Id";
                appGroup.DataBind();
            }

            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectParallalprofile);
            }
        }
        public static void _ParallelWorkflowProfileCustom(ListControl appGroup, bool addDefaultItem, bool isSuperAdmin, int LoggedInUserId)
        {
            appGroup.Items.Clear();
            List<ParallelProfile> lstprof = new List<ParallelProfile>();

            IList<ParallelProfile> oIList3 = null;
            IList<ParallelProfile> _oIList3 = null;
            IList<ParallelProfile> pProfileList = NewFacade.GetAllParallelProfile_Custom(LoggedInUserId);
            IList<ParallelProfile> _pProfileList = (from resLst in NewFacade.GetAllParallelProfile() where resLst.CreatorId == LoggedInUserId select resLst).ToList<ParallelProfile>();

            //var pProfileList = NewFacade.GetAllParallelProfile_Custom(LoggedInUserId);
            //var _pProfileList = (from resLst in NewFacade.GetAllParallelProfile() where resLst.CreatorId == LoggedInUserId select resLst).ToList<ParallelProfile>();


            if (_pProfileList != null && _pProfileList.Count > 0)
            {
                //oIList3 = pProfileList.Concat(_pProfileList).ToList();
                if (pProfileList != null && pProfileList.Count > 0)
                {
                    var oIList4 = pProfileList.Concat(_pProfileList).ToList();
                    oIList3 = oIList4.GroupBy(s => s.ProfileName)
                            .Select(g => g.FirstOrDefault()).ToList();
                    //oIList3 = oIList.FindAll(x => oIList4.Remove(x)).ToList();
                }
                else
                {
                    oIList3 = _pProfileList;
                }
            }
            else
            {

                oIList3 = pProfileList;
            }


            if (oIList3 != null)
            {
                //var t =(from d in oIList3 orderby d.ProfileName ascending select d).ToList().Distinct();
                var profileListByOrder = from a in oIList3 orderby a.ProfileName ascending select a;
                appGroup.DataSource = profileListByOrder;
                appGroup.DataTextField = "ProfileName";
                appGroup.DataValueField = "Id";
                appGroup.DataBind();
            }

            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectParallalprofile);
            }
        }

        public static void ParallelWorkflowProfile_Admin(ListControl appGroup, bool addDefaultItem, bool isSuperAdmin, int LoggedInUserId)
        {
            appGroup.Items.Clear();

            var infrallst = NewFacade.GetInfraObjectByLoginId(LoggedInUserId);
            if (infrallst != null && infrallst.Count > 0)
            {
                //string infralst = string.Empty;
                //foreach (var _infrobj in infrallst)
                //{
                //    infralst = infralst + _infrobj.Id + ",";
                //}
                //if (!string.IsNullOrEmpty(infralst))
                //{
                //    infralst = infralst.Substring(0, infralst.Length - 1);
                //    var pProfileList = NewFacade.GetAllParallelProfileByInfraId(infralst);
                //    if (pProfileList != null)
                //    {
                //        var profileListByOrder = from a in pProfileList orderby a.ProfileName ascending select a;
                //        appGroup.DataSource = profileListByOrder;
                //        appGroup.DataTextField = "ProfileName";
                //        appGroup.DataValueField = "Id";
                //        appGroup.DataBind();
                //    }
                //}
                IList<ParallelProfile> _lsrprprofile = new List<ParallelProfile>();
                foreach (var _infrobj in infrallst)
                {
                    if (_infrobj!= null)
                    {
                        IList<ParallelProfile> pProfileList = NewFacade.GetAllParallelProfileByInfraId(Convert.ToString(_infrobj.Id));
                        if (pProfileList != null)
                        {
                            foreach(var _pprofilelst in pProfileList)
                            {
                                if(!_lsrprprofile.Any(X => X.ProfileName == _pprofilelst.ProfileName))
                                {
                                    _lsrprprofile.Add(_pprofilelst);
                                }
                            }
                        }
                    }
                }
                if(_lsrprprofile!= null)
                {
                    var Uniparproffile = from a in _lsrprprofile orderby a.ProfileName ascending select a;
                    if (Uniparproffile != null)
                    {
                        //var profileListByOrder = from a in Uniparproffile orderby a.ProfileName ascending select a;
                        appGroup.DataSource = Uniparproffile;
                        appGroup.DataTextField = "ProfileName";
                        appGroup.DataValueField = "Id";
                        appGroup.DataBind();
                    }
                }
            }
            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectParallalprofile);
            }
        }


        public static void ParallelWorkflowProfile(ListControl appGroup, bool addDefaultItem, bool isSuperAdmin, int LoggedInUserId)
        {
            appGroup.Items.Clear();
            List<ParallelProfile> lstprof = new List<ParallelProfile>();
            //if (isSuperAdmin)
            //{
            var pProfileList = isSuperAdmin ? NewFacade.GetAllParallelProfile() : (from resLst in NewFacade.GetAllParallelProfile() where resLst.CreatorId == LoggedInUserId select resLst).ToList<ParallelProfile>();
            // var pProfileList = NewFacade.GetAllParallelProfile() ;
            if (pProfileList != null && pProfileList.Count != 0)
            {
                var profileListByOrder = from a in pProfileList orderby a.ProfileName ascending select a;
                appGroup.DataSource = profileListByOrder;
                appGroup.DataTextField = "ProfileName";
                appGroup.DataValueField = "Id";
                appGroup.DataBind();
            }

            if (addDefaultItem)
            {
                appGroup.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectParallalprofile);
            }
        }


        public static void PopulateParallelWorkflow(ListControl lstPworkflow, bool addDefaultItem)
        {
            lstPworkflow.Items.Clear();
            var dbList = NewFacade.GetAllParallelDROperation();
            if (dbList != null)
            {
                var dbListByOrder = from a in dbList orderby a.Description ascending select a;
                lstPworkflow.DataSource = dbListByOrder;
                lstPworkflow.DataTextField = "Description";
                lstPworkflow.DataValueField = "Id";
                lstPworkflow.DataBind();
            }
            if (addDefaultItem)
            {
                lstPworkflow.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectParallelworkflow);
            }
        }

        public static void PopulateVeritasCluster(ListControl lstVeritasCluster, bool addDefaultItem)
        {
            lstVeritasCluster.Items.Clear();
            var VeritasClusterList = NewFacade.GetAllVeritasCluster();
            if (VeritasClusterList != null)
            {
                var serverListByOrder = from a in VeritasClusterList orderby a.ClusteProfileName ascending select a;

                lstVeritasCluster.DataSource = serverListByOrder;
                lstVeritasCluster.DataTextField = "ClusteProfileName";
                lstVeritasCluster.DataValueField = "Id";
                lstVeritasCluster.DataBind();
            }
            if (addDefaultItem)
            {
                lstVeritasCluster.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectClusterProfileName);
            }
        }

        public static void PopulateParallelWorkflowByTimePeriod(ListControl lstPworkflow, int days, bool addDefaultItem)
        {
            lstPworkflow.Items.Clear();
            var dbList = NewFacade.GetAllParallelDROperation();

            IList<ParallelDROperation> getFilter = new List<ParallelDROperation>();
            switch (days)
            {
                case 1:
                    {
                        getFilter = dbList;
                        break;
                    }
                case 2:
                    {
                        var test = from g in dbList where (g.CreateDate > DateTime.Now.AddDays(-7)) select g;
                        getFilter = test.ToList();
                        break;
                    }
                case 3:
                    {
                        var test = from g in dbList where (g.CreateDate > DateTime.Now.AddDays(-30)) select g;
                        getFilter = test.ToList();
                        break;
                    }
                case 4:
                    {
                        var test = from g in dbList where (g.CreateDate > DateTime.Now.AddYears(-1)) select g;
                        getFilter = test.ToList();
                        break;
                    }
            }
            if (getFilter != null)
            {
                lstPworkflow.DataSource = getFilter;
                lstPworkflow.DataTextField = "Description";
                lstPworkflow.DataValueField = "Id";
                lstPworkflow.DataBind();
            }
            if (addDefaultItem)
            {
                lstPworkflow.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectParallelworkflow);
            }
        }

        public static IList<ListItem> GetSelectedItem(ListControl lstItem)
        {
            return lstItem.Items.Cast<ListItem>().Where(list => list.Selected).ToList();
        }

        public static IList<ListItem> GetSelectedAppItem(ListControl lstAppItem)
        {
            return lstAppItem.Items.Cast<ListItem>().Where(list => list.Selected).ToList();
        }

        public static void ShowMessage(HtmlControl htmlControl, Label label, string message, bool isError)
        {
            htmlControl.Visible = true;
            if (isError)
            {
                htmlControl.Attributes["class"] = "message success no-margin no-bottom-margin";
            }
            else
            {
                htmlControl.Attributes["class"] = "message success no-margin no-bottom-margin";
            }
            label.Text = message;
        }

        public static void PopulateDatabaseBackupInfo(ListControl lstDatabase, bool addDefaultItem)
        {
            lstDatabase.Items.Clear();
            var dbList = NewFacade.GetAllDatabaseBackupInfos();
            if (dbList != null)
            {
                var dbListByOrder = from a in dbList orderby a.DatabaseName ascending select a;
                lstDatabase.DataSource = dbListByOrder;
                lstDatabase.DataTextField = "DatabaseName";
                lstDatabase.DataValueField = "Id";
                lstDatabase.DataBind();
            }
            if (addDefaultItem)
            {
                lstDatabase.AddDefaultItem(Constants.UIConstants.DropDownItemPleaseSelectDBName);
            }
        }

        public static void SelectMenu(MasterPage master, string name)
        {
            if (master == null) return;
            var hd = master.FindControl("hdSelectedMenu") as HiddenField;
            if (hd != null) hd.Value = name;
        }

        public static bool GetDatlagHealth(string datalag, int configureDatalag)
        {
            try
            {
                TimeSpan currentDatalag;

                if (datalag.Contains("N/A"))
                {
                    return false;
                }
                if (TimeSpan.TryParse(datalag, out currentDatalag))
                {
                    TimeSpan configuredLag = new TimeSpan(0, 0, 1, configureDatalag);

                    return currentDatalag <= configuredLag;
                }
            }
            catch (Exception)
            {
                return false;
            }

            return false;
        }

        /// <summary>
        /// Converts CurrentRPO string format "days.hour:minutes:seconds" into minutes.
        /// </summary>
        /// <param name="inputRPO">inputRPO</param>
        /// <returns>string</returns>
        /// <author>Ram mahajan</author>
        public static string ConvertRPOValues(string inputRPO)
        {
            if (!inputRPO.Contains('.'))
            {
                string[] datalag = inputRPO.Split(':');

                double hrs = Convert.ToDouble(datalag[0]);
                if (hrs > 24)
                {
                    TimeSpan result = TimeSpan.FromHours(hrs);
                    inputRPO = string.Empty;
                    inputRPO = result.Days + "." + result.Hours + ":" + datalag[1] + ":" + datalag[2];
                }
            }

            string minutesRPO = string.Empty;
            string minutesRPO1 = string.Empty;
            char split = '.';
            int day = 0;
            int day1 = 0;
            string totalhr = string.Empty;
            int l = inputRPO.IndexOf(":");
            if (l > 0)
            {
                minutesRPO1 = inputRPO.Substring(0, l);
            }
            if (!string.IsNullOrEmpty(inputRPO))
            {
                if (inputRPO.Contains("NA") || inputRPO.Contains("N/A") || inputRPO.Contains("-"))
                {
                    return minutesRPO;
                }
                else
                {
                    if (inputRPO.Contains("."))
                    {
                        string[] splitArray = inputRPO.Split('.');

                        day = Convert.ToInt32(splitArray[0]);

                        if (Convert.ToInt32(splitArray[1].Split(':')[0]) == 24)
                            day1 = 1;
                        else
                            day1 = 0;

                        int totalDays = day + day1;

                        totalhr = totalDays + "day";
                    }
                    else
                    {
                        string[] splitArraybyColon = inputRPO.Split(':');

                        if ((splitArraybyColon[0]) != "00")
                        {
                            totalhr = splitArraybyColon[0] + "Hrs";
                        }
                        else if ((splitArraybyColon[1]) != "00")
                        {
                            totalhr = splitArraybyColon[1] + "Min";
                        }
                        else if ((splitArraybyColon[2]) != "00" || (splitArraybyColon[2]) == "00")
                        {
                            totalhr = "00" + "Min";
                        }
                        else if (splitArraybyColon[0] == "00" && splitArraybyColon[1] == "00" && splitArraybyColon[1] == "00")
                        {
                            totalhr = string.Empty;
                        }
                    }

                    //if (inputRPO.Contains("00"))
                    //{
                    //    string[] minute = inputRPO.Split(':');
                    //    totalhr = minute[1].ToString() + "Min";
                    //}
                    //else
                    //{
                    //    string[] min = minutesRPO1.Split(split);
                    //    day = Convert.ToInt32(min[0]);

                    //    if (min.Count() > 1)
                    //        day1 = Convert.ToInt32(min[1]);
                    //    else
                    //        day1 = 0;
                    //    if (day1 == 0)
                    //    {
                    //        totalhr = day.ToString();
                    //    }
                    //    else
                    //    {
                    //        totalhr = ((day * 24) + day1).ToString();

                    //        if (totalhr.Length > 2)
                    //        {
                    //            totalhr = day.ToString() + "day";
                    //        }
                    //        else
                    //        {
                    //            totalhr = ((day * 24) + day1).ToString() + "Hrs";
                    //        }
                    //    }
                    //}

                    //minutesRPO = totalhr.ToString();

                    // TimeSpan span = TimeSpan.Parse(inputRPO);
                    // minutesRPO = span.TotalMinutes.ToString();
                }
            }
            return totalhr;
        }

        public static string ConvertMinToFormat(int min)
        {
            TimeSpan t = TimeSpan.FromMinutes(min);
            string _timefomat = string.Empty;
            if (t.Days > 0)
            {
                _timefomat = string.Format("{0:D2}.{1:D2}:{2:D2}:{3:D2}",
                   t.Days,
                               t.Hours,
                               t.Minutes,
                               t.Seconds
                             );
            }
            else
            {
                _timefomat = string.Format("{0:D2}:{1:D2}:{2:D2}",
                                                   t.Hours,
                               t.Minutes,
                               t.Seconds
                             );
            }
            return _timefomat;
        }
        public static string ConvertRTOValues(string inputRTO)
        {
            string hoursRTO = string.Empty;
            if (!string.IsNullOrEmpty(inputRTO))
            {
                if (inputRTO.Contains("NA") || inputRTO.Contains("N/A") || inputRTO.Contains("-"))
                {
                    return hoursRTO;
                }
                else
                {
                    TimeSpan span = TimeSpan.Parse(inputRTO);
                    hoursRTO = span.TotalHours.ToString();
                }
            }
            return hoursRTO;
        }

        /// <summary>
        ///  Validates DateTime Column or String
        /// </summary>
        /// <param name="txtDate"></param>
        /// <returns></returns>
        /// <author> Kuntesh Thakker - 19-04-2014 </author>
        public static bool IsDateTime(string txtDate)
        {
            DateTime tempDate;
            return DateTime.TryParse(txtDate, out tempDate) ? true : false;
        }

        /// <summary>
        /// Validates if String is in Timespan foramt or not
        /// </summary>
        /// <param name="Value"></param>
        /// <returns></returns>
        /// <Author> Kuntesh Thakker - 18-05-2014 </Author>
        public static bool IsTimeSpanFormat(string Value)
        {
            if (Value.Contains(".") || Value.Contains(":"))
                return true;
            else
                return false;
        }

        /// <summary>
        ///  Get Formatted String for TimeSpan
        /// </summary>
        /// <param name="timespan"></param>
        /// <returns></returns>
        /// <author> Kuntesh Thakker - 19-04-2014 </author>
        public static string GetFormattedDateTimeString(TimeSpan timespan)
        {
            StringBuilder sbtime = new StringBuilder();
            if (timespan.Days <= 0 && timespan.Hours <= 0 && timespan.Minutes <= 0 && timespan.Seconds <= 0)
            {

                sbtime.Append(string.Format("{0}{1}{2}{3}", "0 Day(s)", "0 Hr(s)", "0 Min(s)", "0 Sec(s)"));
            }
            else
            {
                try
                {

                    if (timespan.Days > 0)
                    {
                        sbtime.Append(timespan.Days + " Day(s) ");
                    }
                    if (timespan.Hours > 0)
                    {
                        sbtime.Append(timespan.Hours + " Hr(s) ");
                    }
                    if (timespan.Minutes > 0)
                    {
                        sbtime.Append(timespan.Minutes + " Min(s) ");
                    }
                    if (timespan.Seconds > 0)
                    {
                        sbtime.Append(timespan.Seconds + " Sec(s)");
                    }

                }
                catch (CpException exc)
                {
                    ExceptionManager.Manage(exc);
                }
                catch (Exception ex)
                {
                    var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting GetFormattedDateTimeString", ex);
                    ExceptionManager.Manage(cpException);
                }
            }
            return sbtime.ToString();

        }

        public static bool GetReportDatlagHealth(string datalag, string configureDatalag)
        {
            try
            {
                TimeSpan currentDatalag;

                if (datalag.Contains("N/A"))
                {
                    return false;
                }
                if (TimeSpan.TryParse(datalag, out currentDatalag))
                {
                    TimeSpan configuredLag = TimeSpan.FromSeconds(Convert.ToDouble(configureDatalag)); //new TimeSpan(0, 0, 1, configureDatalag);

                    return currentDatalag <= configuredLag;
                }
            }
            catch (Exception)
            {
                return false;
            }

            return false;
        }

        public static String GetTimespanConfigRPO(string seconds)
        {
            /*string ConfigRpo;

            double outparam;
            if (Double.TryParse(seconds, out outparam))
                ConfigRpo = TimeSpan.FromSeconds(Convert.ToDouble(seconds)).ToString();
            else
                ConfigRpo = "NA";

            return ConfigRpo;*/
            string ConfigRpo;
            if (seconds == null)
            {
                ConfigRpo = "NA";
            }
            else if (seconds.Contains("NA"))
            {
                ConfigRpo = "NA";
            }
            else
            {
                if (!seconds.Contains(":"))
                {
                    double outparam;
                    if (Double.TryParse(seconds, out outparam))
                        ConfigRpo = TimeSpan.FromSeconds(Convert.ToDouble(seconds)).ToString();
                    else
                        ConfigRpo = "NA";
                }
                else
                {
                    ConfigRpo = seconds;
                }
            }

            return ConfigRpo;
        }

        public static TimeSpan ConvertCronExpressionToTimeSpan(string expression)
        {
            var span = new TimeSpan();

            string[] parts = expression.Split('/');

            if (parts[1].Contains(" * * * ?"))
            {
                var m = expression.Substring(expression.LastIndexOf('/') + 1,
                                             expression.IndexOf('*') - (expression.LastIndexOf('/') + 1));
                m = m.Trim();

                span = new TimeSpan(0, Convert.ToInt32(m), 0);
            }
            else if (parts[1].Contains("* * ?"))
            {
                var t = expression.Substring(1, 3);
                var w = expression.Substring(expression.LastIndexOf('/') + 1,
                                             expression.IndexOf('*') - (expression.LastIndexOf('/') + 1));
                t = t.Trim();
                w = w.Trim();

                span = new TimeSpan(Convert.ToInt32(w), Convert.ToInt32(t), 0);
            }
            else
            {

                var d = expression.Substring(expression.LastIndexOf('/') + 1, expression.IndexOf('*') - (expression.LastIndexOf('/') + 1));

                var m = expression.Substring(1, 3);
                var h = expression.Substring(4, 4);
                m = m.Trim();
                h = h.Trim();

                span = new TimeSpan(Convert.ToInt32(d), Convert.ToInt32(h), Convert.ToInt32(m), 0);

                //var t = expression.Substring(1, 3);
                //var w = expression.Substring(expression.LastIndexOf('/') + 1,
                //                             expression.IndexOf('*') - (expression.LastIndexOf('/') + 1));
                //t = t.Trim();
                //w = w.Trim();

                // span = new TimeSpan(Convert.ToInt32(w), Convert.ToInt32(t), 0);
            }

            return span;
        }

        public static bool IsMD5EncryptedString(string inputString)
        {
            bool isEncrypted = true;
            string decryptString = string.Empty;
            try
            {
                decryptString = CryptographyHelper.Md5Decrypt(inputString);
            }
            catch (Exception)
            {
                isEncrypted = false;
            }
            return isEncrypted;
        }

        /// <summary>
        /// Method to audit data on Add,Update and delete operations on any table.
        /// </summary>
        /// <param name="TableNames">Table name which we want to audit</param>
        /// <param name="CurrentUserId">current logged in users id</param>
        /// <param name="RecordId">Which Record we are adding,updating or deleting its Id</param>
        /// <param name="type">Transactiontype which we are doing.</param>
        /// <param name="OldRecord">Old record before update</param>
        /// <param name="NewRecord">New Record to update</param>
        /// <author>Pandurang Pailvan</author>
        public static void AuditData(string TableNames, int CurrentUserId, int RecordId, TransactionType type, object OldRecord, object NewRecord, string Identity)
        {
            // Instance creation
            Audit audit = new Audit();

            // Variable declartion
            string ColumnNames = string.Empty, NewValues = string.Empty, OldValues = string.Empty;

            // Get users details by its Id
            var userinfo = NewFacade.GetUserById(CurrentUserId);

            // As per Transaction Type switch into blocks.
            switch (type)
            {
                case TransactionType.Add:
                    // string Description = userinfo.LoginName + " has been added new record.";
                    string Description = TableNames + ": " + Identity + " record added by user " + userinfo.LoginName;
                    audit.TableName = TableNames;
                    audit.RecordId = RecordId;
                    audit.OldValue = "N/A";
                    audit.NewValue = "N/A";
                    audit.AuditType = TransactionType.Add;
                    audit.ChangedColumn = "N/A";
                    audit.CreatorId = CurrentUserId;
                    audit.CreateDate = DateTime.Now;
                    audit.Description = Description;
                    audit = NewFacade.AddAudit(audit);
                    break;

                case TransactionType.Update:
                    if (OldRecord != null && NewRecord != null)
                    {
                        XmlDocument xmlDoc = new XmlDocument();
                        XmlDocument xmlDoc2 = new XmlDocument();
                        XmlSerializer serializer = new XmlSerializer(OldRecord.GetType());
                        XmlSerializer serializer2 = new XmlSerializer(NewRecord.GetType());
                        using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
                        {
                            serializer.Serialize(ms, OldRecord);
                            ms.Position = 0;
                            xmlDoc.Load(ms);
                        }

                        using (System.IO.MemoryStream ms2 = new System.IO.MemoryStream())
                        {
                            serializer2.Serialize(ms2, NewRecord);
                            ms2.Position = 0;
                            xmlDoc2.Load(ms2);

                        }

                        XmlNodeReader reader1 = new XmlNodeReader(xmlDoc);
                        XmlNodeReader reader2 = new XmlNodeReader(xmlDoc2);

                        string column = string.Empty;
                        while (reader1.Read())
                        {
                            reader2.Read();
                            switch (reader1.NodeType)
                            {

                                case XmlNodeType.Element:
                                    //Get the node(column) name
                                    column = reader1.Name;
                                    break;

                                case XmlNodeType.Text:

                                    if (reader1.Value != reader2.Value)
                                    {
                                        OldValues = OldValues + column + " : " + reader1.Value + " , ";
                                        NewValues = NewValues + column + " : " + reader2.Value + " , ";
                                        ColumnNames = ColumnNames + column + " , ";

                                    }
                                    break;
                            }



                        }// while end

                        if (!string.IsNullOrEmpty(OldValues) && !string.IsNullOrEmpty(NewValues) && !string.IsNullOrEmpty(ColumnNames))
                        {
                            NewValues = NewValues.Substring(0, NewValues.Length - 2);
                            OldValues = OldValues.Substring(0, OldValues.Length - 2);
                            ColumnNames = ColumnNames.Substring(0, ColumnNames.Length - 2);

                            //string Desc = userinfo.LoginName + " has been updated " + ColumnNames + " column(s) values.";
                            string Desc = TableNames + " : " + Identity + " record was modified by user " + userinfo.LoginName;
                            audit.TableName = TableNames;
                            audit.RecordId = RecordId;
                            audit.OldValue = OldValues;
                            audit.NewValue = NewValues;
                            audit.AuditType = TransactionType.Update;
                            audit.ChangedColumn = ColumnNames;
                            audit.CreatorId = CurrentUserId;
                            audit.CreateDate = DateTime.Now;
                            audit.Description = Desc;
                            audit = NewFacade.AddAudit(audit);

                        }

                    }// if checking null end.
                    break;
                case TransactionType.Delete:

                    //string Desc1 = userinfo.LoginName + " has been updated IsActive column(s) values.";
                    string Desc1 = TableNames + " : " + Identity + " record was deleted by user " + userinfo.LoginName;
                    audit.TableName = TableNames;
                    audit.RecordId = RecordId;
                    audit.OldValue = "1";
                    audit.NewValue = "0";
                    audit.AuditType = TransactionType.Delete;
                    audit.ChangedColumn = "IsActive";
                    audit.CreatorId = CurrentUserId;
                    audit.CreateDate = DateTime.Now;
                    audit.Description = Desc1;
                    audit = NewFacade.AddAudit(audit);
                    break;
            }
        }

        // Telerik Static Methods Start.
        public static string GetFormatedTime(string text)
        {
            if (text.Trim() != null && text.Trim() != "NA" && text.Trim() != "")
            {
                string retText = "NA";
                if (text.Contains("AM") && !text.Contains("/"))
                {
                    retText = Convert.ToDateTime(text).ToString("dd-MM-yyyy hh:mm:ss") + " AM";
                    return retText;
                }
                else if (text.Contains("PM") && !text.Contains("/"))
                {
                    retText = Convert.ToDateTime(text).ToString("dd-MM-yyyy hh:mm:ss") + " PM";
                    return retText;
                }
                else
                {
                    if (text.Contains("-"))
                    {
                        string[] arr = text.Split(' ');
                        string[] arr1 = arr[0].Split('-');
                        string[] TimeData = arr[1].Split(':');
                        string fnl;
                        if (arr1[2].Count() == 4)
                        {
                            if (TimeData != null && TimeData.Length > 0)
                            {
                                if (Convert.ToInt32(TimeData[0]) <= 11 && Convert.ToInt32(TimeData[1]) <= 59)
                                {
                                    if (Convert.ToInt32(arr1[0]) > 12)
                                    {
                                        fnl = arr1[0] + "-" + arr1[1] + "-" + arr1[2] + " " + arr[1];
                                    }
                                    else
                                    {
                                        fnl = arr1[1] + "-" + arr1[0] + "-" + arr1[2] + " " + arr[1];
                                    }
                                    //fnl = arr1[2] + "-" + arr1[1] + "-" + arr1[0] + " " + arr[1];
                                    retText = fnl.ToString(); //Convert.ToDateTime(fnl).ToString("dd-MM-yyyy hh:mm:ss") ;
                                    retText += " AM";
                                }
                                else if (Convert.ToInt32(TimeData[0]) >= 12)
                                {
                                    if (Convert.ToInt32(arr1[0]) > 12)
                                    {
                                        fnl = arr1[0] + "-" + arr1[1] + "-" + arr1[2] + " " + arr[1];
                                    }
                                    else
                                    {
                                        fnl = arr1[1] + "-" + arr1[0] + "-" + arr1[2] + " " + arr[1];
                                    }
                                    //fnl = arr1[2] + "-" + arr1[1] + "-" + arr1[0] + " " + arr[1];
                                    retText = fnl.ToString(); //Convert.ToDateTime(fnl).ToString("dd-MM-yyyy hh:mm:ss");
                                    retText += " PM";
                                }
                            }

                        }
                        else
                        {
                            if (TimeData != null && TimeData.Length > 0)
                            {
                                if (Convert.ToInt32(TimeData[0]) <= 11 && Convert.ToInt32(TimeData[1]) <= 59)
                                {
                                    if (Convert.ToInt32(arr1[1]) > 12)
                                    {
                                        fnl = arr1[1] + "-" + arr1[2] + "-" + arr1[0] + " " + arr[1];
                                    }
                                    else
                                    {
                                        fnl = arr1[2] + "-" + arr1[1] + "-" + arr1[0] + " " + arr[1];
                                    }
                                    //fnl = arr1[2] + "-" + arr1[1] + "-" + arr1[0] + " " + arr[1];
                                    retText = fnl.ToString(); //Convert.ToDateTime(fnl).ToString("dd-MM-yyyy hh:mm:ss");
                                    retText += " AM";

                                }
                                else if (Convert.ToInt32(TimeData[0]) >= 12)
                                {
                                    //fnl = arr1[1] + "-" + arr1[0] + "-" + arr1[2] + " " + arr[1];
                                    if (Convert.ToInt32(arr1[1]) > 12)
                                    {
                                        fnl = arr1[1] + "-" + arr1[2] + "-" + arr1[0] + " " + arr[1];
                                    }
                                    else
                                    {
                                        fnl = arr1[1] + "-" + arr1[2] + "-" + arr1[0] + " " + arr[1];
                                    }
                                    retText = fnl.ToString(); //Convert.ToDateTime(fnl).ToString("dd-MM-yyyy hh:mm:ss");
                                    retText += " PM";
                                }
                            }
                        }
                    }
                    else
                    {
                        string[] arr = text.Split(' ');
                        string[] arr1 = arr[0].Split('/');
                        string[] TimeData = arr[1].Split(':');
                        string fnl;
                        if (arr1[2].Count() == 4)
                        {
                            if (Convert.ToInt32(TimeData[0]) <= 11 && Convert.ToInt32(TimeData[1]) <= 59)
                            {
                                if (Convert.ToInt32(arr1[0]) > 12)
                                {
                                    fnl = arr1[0] + "-" + arr1[1] + "-" + arr1[2] + " " + arr[1];
                                }
                                else
                                {
                                    fnl = arr1[1] + "-" + arr1[0] + "-" + arr1[2] + " " + arr[1];
                                }
                                retText = fnl.ToString(); //Convert.ToDateTime(fnl).ToString("dd-MM-yyyy hh:mm:ss");
                                retText += " AM";
                            }
                            else if (Convert.ToInt32(TimeData[0]) >= 12)
                            {
                                if (Convert.ToInt32(arr1[0]) > 12)
                                {
                                    fnl = arr1[0] + "-" + arr1[1] + "-" + arr1[2] + " " + arr[1];
                                }
                                else
                                {
                                    fnl = arr1[1] + "-" + arr1[0] + "-" + arr1[2] + " " + arr[1];
                                }
                                retText = fnl.ToString(); //Convert.ToDateTime(fnl).ToString("dd-MM-yyyy hh:mm:ss");
                                retText += " PM";
                            }
                        }
                        else
                        {
                            if (TimeData != null && TimeData.Length > 0)
                            {
                                if (Convert.ToInt32(TimeData[0]) <= 11 && Convert.ToInt32(TimeData[1]) <= 59)
                                {
                                    //fnl = arr1[0] + "-" + arr1[1] + "-" + arr1[2] + " " + arr[1];
                                    if (Convert.ToInt32(arr1[1]) > 12)
                                    {
                                        fnl = arr1[1] + "-" + arr1[2] + "-" + arr1[0] + " " + arr[1];
                                    }
                                    else
                                    {
                                        fnl = arr1[2] + "-" + arr1[1] + "-" + arr1[0] + " " + arr[1];
                                    }

                                    retText = fnl.ToString(); //Convert.ToDateTime(fnl).ToString("dd-MM-yyyy hh:mm:ss");
                                    retText += " AM";
                                }
                                else if (Convert.ToInt32(TimeData[0]) >= 12)
                                {
                                    //fnl = arr1[0] + "-" + arr1[1] + "-" + arr1[2] + " " + arr[1];
                                    if (Convert.ToInt32(arr1[1]) > 12)
                                    {
                                        fnl = arr1[1] + "-" + arr1[2] + "-" + arr1[0] + " " + arr[1];
                                    }
                                    else
                                    {
                                        fnl = arr1[2] + "-" + arr1[1] + "-" + arr1[0] + " " + arr[1];
                                    }
                                    retText = fnl.ToString(); //Convert.ToDateTime(fnl).ToString("dd-MM-yyyy hh:mm:ss") ;
                                    retText += " PM";
                                }
                            }
                            //retText = Convert.ToDateTime(text).ToString("dd-MM-yyyy hh:mm:ss") + " PM";
                            //return retText;
                        }
                    }
                }
                return retText;
            }
            else
                return "NA";
        }

        public static string GetDecryptedString(string text)
        {
            if (text != null)
            {
                if (text.Contains("="))
                {
                    string retText = CryptographyHelper.Md5Decrypt(text);
                    return retText;
                }
                else
                    return text;
            }
            else
                return "NA";
        }


        public static string GetIPAndHostNameByServerID(int serverid)
        {
            string str = string.Empty;

            if (serverid == 0)
                return "NA";


            Server svr = NewFacade.GetServerById(serverid);

            if (svr != null)
            {
                if (!string.IsNullOrEmpty(svr.IPAddress) && CryptographyHelper.Md5Decrypt(svr.IPAddress).Contains('.'))
                {
                    return CryptographyHelper.Md5Decrypt(svr.IPAddress);

                }
                else if (!string.IsNullOrEmpty(svr.HostName))
                {

                    return svr.HostName;
                }
                else
                {
                    return "NA";
                }

            }
            else
            {
                return "NA";
            }

        }

        public static string GetDecryptStringAndTimeStapm(string text)
        {
            if (text != null)
            {
                if (text.Contains("="))
                {
                    string retText = CryptographyHelper.Md5Decrypt(text);
                    return retText;
                }
                else if (!text.Contains(":"))
                {
                    double outparam;
                    if (Double.TryParse(text, out outparam))
                    {
                        string retText = TimeSpan.FromSeconds(Convert.ToDouble(text)).ToString();
                        return retText;
                    }
                    else
                    {
                        string retText = text;
                        return retText;
                    }
                }
                string retText1 = text;
                return retText1;
            }
            else
                return "NA";
        }

        public static string GetPRIPAddress(int InfraId)
        {
            InfraObject Infra = NewFacade.GetInfraObjectById(InfraId);
            if (Infra != null)
            {
                Server svr = NewFacade.GetServerById(Infra != null ? Infra.PRServerId : 0);
                return (svr != null ? CryptographyHelper.Md5Decrypt(svr.IPAddress) : "NA");
            }
            else
            {
                return "NA";
            }
        }

        public static string GetDRIPAddress(int InfraId)
        {
            InfraObject Infra = NewFacade.GetInfraObjectById(InfraId);
            if (Infra != null)
            {
                Server svr = NewFacade.GetServerById(Infra != null ? Infra.DRServerId : 0);
                return (svr != null ? CryptographyHelper.Md5Decrypt(svr.IPAddress) : "NA");
            }
            else
            {
                return "NA";
            }
        }

        public static string HadleNull(string text)
        {

            if (text != null)
            {
                string Decrypt = CryptographyHelper.Md5Decrypt(Convert.ToString(text));
                return Decrypt;
            }
            else
            {
                return "NA";
            }
        }

        public static void setDBParameterPrefix()
        {
#if ORACLE
            dbParaPrefix = "";
#else
            dbParaPrefix = "@";
#endif
        }

        public static string HadleNullValue(string text)
        {

            if (text == " ")
            {
                return "NA";
            }
            else if (!string.IsNullOrEmpty(text))
            {
                return text;
            }
            else
            {
                return "NA";
            }
        }

        public static string getFormatedDate(string date)
        {
#if ORACLE || MSSQL


            return Convert.ToDateTime(date).ToString("dd-MM-yy");

#else
            return date;
#endif
        }

        public static string getFormatedDate_New(string date)
        {
#if ORACLE 
            return Convert.ToDateTime(date).ToString("dd-MMM-yy");
#endif

#if MSSQL
            return Convert.ToDateTime(date).ToString("yyyy-MM-dd");
#endif

#if MYSQL
            return date;


#else
            return date;
#endif
        }

        public static string GetPRHostName(int InfraId)
        {
            InfraObject Infra = NewFacade.GetInfraObjectById(Convert.ToInt32(InfraId));
            if (Infra != null)
            {
                Server svr = NewFacade.GetServerById(Infra != null ? Infra.PRServerId : 0);
                return (svr != null ? svr.HostName : "NA");
            }
            else
            {
                return "NA";
            }
        }

        public static string GetDRHostName(int InfraId)
        {
            InfraObject Infra = NewFacade.GetInfraObjectById(Convert.ToInt32(InfraId));
            if (Infra != null)
            {
                Server svr = NewFacade.GetServerById(Infra != null ? Infra.DRServerId : 0);
                return (svr != null ? svr.HostName : "NA");
            }
            else
            {
                return "NA";
            }
        }

        public static string GetParamNull(string data)
        {
            if (data != null)
            {
                if (data == "TRUE")
                    return string.Empty;
                else if (data == "FALSE")
                    return string.Empty;
                else
                    return data;
            }
            else
                return "NA";
        }

        public static string GetFormatedUserHostAddress(string address)
        {
            if (address != null && address != "NA")
            {
                //string retText;
                if (address.Contains("::"))
                {
                    //retText = address.Remove(':');
                    //retText = retText.Remove(':');
                    return address;
                }
                else
                    return address;
            }
            else
                return "NA";
        }

        public static string GetSql2000Time(string text)
        {
            if (text != null && text != "NA" && text != "")
            {
                string retText;
                if (text.Contains("AM"))
                {
                    retText = text;
                    return retText;
                }
                else if (text.Contains("PM"))
                {
                    retText = text;
                    return retText;
                }
                else
                {
                    retText = text + " PM";
                    return retText;
                }
            }
            else
                return "NA";
        }
        // Telerik Static Methods End.

//        public static string TelerikConnection()
//        {
//            string telCon = ConfigurationManager.ConnectionStrings["CPConnectionString"].ToString();

//#if ORACLE

//            return telCon;
//#elif MSSQL
//            return telCon;
//#else
//            /*
//            //return telCon;
//            //string cptelCon = telCon;
//            string cptelCon = "kYwA7mf9OKa7YPX1iNLk68laDGfCoT6fijUlAGD6nn2WfzxVJPGkWoDShU2kVxOeSRTTB3wfWpHVqZIwV9iLbw==";
//            cptelCon = CryptographyHelper.Md5Decrypt(cptelCon);
//            string cpConStr = CryptographyHelper.Md5Decrypt(telCon);
//            string[] cnStr = cpConStr.Split(';');
//            StringBuilder buillder = new StringBuilder(cptelCon);
//            buillder.Replace("server", cnStr[0]);
//            buillder.Replace("User Id", cnStr[1]);
//            buillder.Replace("password", cnStr[2]);
//            //buillder.Replace("uid", "User Id");
//            //buillder.Replace("pwd", "password");
//            cptelCon = Convert.ToString(buillder);
//            return CryptographyHelper.Md5Encrypt(cptelCon); */

//            System.Data.SqlClient.SqlConnectionStringBuilder connBuilder = new System.Data.SqlClient.SqlConnectionStringBuilder();
//            /*string cptelCon = "kYwA7mf9OKa7YPX1iNLk68laDGfCoT6fijUlAGD6nn2WfzxVJPGkWoDShU2kVxOeSRTTB3wfWpHO8cFBynUIrw==";*/

//            string cptelCon = "nwRFWXcrB35EcOhVM6YBzarxg/gNKOG4hSKiAN4dzy/OrOYEdJaKSzLoqVWI5Hl8ZDSq6BAFf/J5ITKaewBxRw==";
//            cptelCon = CryptographyHelper.Md5Decrypt(cptelCon);
//            string cpConStr = CryptographyHelper.Md5Decrypt(telCon);
//            StringBuilder buillder = new StringBuilder(cptelCon);
//            if (cpConStr.Contains("procedure bodies=false;"))
//                cpConStr = cpConStr.Replace("procedure bodies=false;", "");
//            connBuilder.ConnectionString = cpConStr;

//            buillder.Replace("server", "server = " + connBuilder.DataSource);
//            buillder.Replace("User Id", "User Id = " + connBuilder.UserID);
//            buillder.Replace("password", "password = " + connBuilder.Password);
//            buillder.Replace("database", "database = " + connBuilder.InitialCatalog.ToLower());

//            cptelCon = Convert.ToString(buillder);
//            return CryptographyHelper.Md5Encrypt(cptelCon);
//#endif
//        }



        public static string TelerikConnection()
        {
            string telCon = ConfigurationManager.ConnectionStrings["CPConnectionString"].ToString();

            string telcon1 = CryptographyHelper.Md5Decrypt(telCon);
            string stringuser = "";
            string strinpassword = "";
            string stringdirect = "";
            string stringhost = "";
            string stringsid = "";
            string stringpost = "";

            string[] splitted = telcon1.Split(';');
            string[] splitted1 = splitted[0].Split('=');
            string[] splitted2 = splitted[1].Split('=');
            string[] splitted3 = splitted[2].Split('=');
            string[] splitted4 = splitted[3].Split('=');
            string[] splitted5 = splitted[4].Split('=');
            string[] splitted6 = splitted[5].Split('=');
            if (splitted1 != null && splitted2 != null && splitted3 != null && splitted4 != null && splitted5 != null && splitted6 != null)
            {
                stringuser = splitted1[1].ToString();
                strinpassword = splitted2[1].ToString();
                stringdirect = splitted3[1].ToString();
                stringhost = splitted4[1].ToString();
                stringsid = splitted5[1].ToString();
                stringpost = splitted6[1].ToString();
            }

            telCon = string.Format("Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=" + stringhost + ")(PORT=" + stringpost + ")))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=" + stringsid + ")));User Id=" + stringuser + ";Password=" + strinpassword);

            telCon = CryptographyHelper.Md5Encrypt(telCon);
#if ORACLE
            return telCon;
#elif MSSQL
                            return telCon;
#else
            //return telCon;
            string cptelCon = telCon;
            //string cptelCon = "kYwA7mf9OKa7YPX1iNLk68laDGfCoT6fijUlAGD6nn2WfzxVJPGkWoDShU2kVxOeSRTTB3wfWpHVqZIwV9iLbw==";
            cptelCon = CryptographyHelper.Md5Decrypt(cptelCon);
            string cpConStr = CryptographyHelper.Md5Decrypt(telCon);
            //string[] cnStr = cpConStr.Split(';');
            StringBuilder buillder = new StringBuilder(cptelCon);
            //buillder.Replace("server", cnStr[0]);
            //buillder.Replace("User Id", cnStr[1]);
            //buillder.Replace("password", cnStr[2]);
            //buillder.Replace("uid", "User Id");
            //buillder.Replace("pwd", "password");
            cptelCon = Convert.ToString(buillder);
            return CryptographyHelper.Md5Encrypt(cptelCon);
#endif
        }

        public static string TelerikProvider()
        {
            string dataProvider = string.Empty;
#if ORACLE
           // return dataProvider = "EZXKi9lxHBQ3ZEH9gwO0XftsCuW3her3";
            //return dataProvider = "xgF7M/kdyDumVkmK+l0+xsuGVALQYlKe";  //For  4.5 Encryption/decryption

            return dataProvider = "EAGlLh86I0zHcYgqBJFBeIrJwqm2E1Mfv7+A6QxXWZM=";
#elif MSSQL

            // return dataProvider = "KiyTnI6V39FMKwtLJHCLkAZaz+KbTQVc";
            return dataProvider = "tvdA4go/NsXjYSCTkpuALYELfuAUcjfp";
#else
            //return dataProvider = "iwXg5xmRYweY6kcrP0crDbLhv+V2PVBy";
            return dataProvider = "Cno/k7Jsz+sUEw7D6IIj+Ok9pwGK4r3w";
#endif
        }

        public static string HandleNull(string text)
        {
            string remove = "";
            if (string.IsNullOrEmpty(text))
            {
                return "NA";
            }
            else
            {
                if (text.Contains("update"))
                {
                    remove = text.Substring(0, text.LastIndexOf("update") + 6);
                    text = remove + "d" + ".";
                }
                if (text.Contains("deleted"))
                {
                    remove = text.Substring(0, text.LastIndexOf("deleted") + 7);
                    text = remove + ".";
                }
                if (text.Contains("added"))
                {
                    remove = text.Substring(0, text.LastIndexOf("added") + 5);
                    text = remove + ".";
                }
                if (text.Contains("User InfraObject"))
                {
                    int test = text.IndexOf("'");
                    string str = text.Substring(test + 1);
                    string str1 = str.Remove(str.IndexOf("'"));
                    int userid = Convert.ToInt32(str1);

                    var loginname = NewFacade.GetUserById(userid);

                    if (loginname != null)
                    {
                        text = "The User InfraObject" + " '" + loginname.LoginName + "'was added.";
                    }
                    else
                    {
                        return text;
                    }
                }
                return text;
            }
            // Logger.DebugFormat("User Activity Report has been generated for<({0})>", loginname.);
        }

        public static void PopulateAccessmanagertype(ListControl lstType)
        {
            string[] enumNames = Enum.GetNames(typeof(AccessManagerType));
            lstType.Items.Clear();
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(AccessManagerType), item);
                ListItem listItem = new ListItem(item, value.ToString());
                lstType.Items.Add(listItem);
            }
            lstType.Items.Insert(0, new ListItem("Select", "0"));
        }

        public static void PopulateDashboard(ListControl lstType)
        {
            string[] enumNames = Enum.GetNames(typeof(SubDashboard));
            lstType.Items.Clear();
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(SubDashboard), item);
                ListItem listItem = new ListItem(item, value.ToString());
                lstType.Items.Add(listItem);
            }
        }

        public static void PopulateConfiguration(ListControl lstType)
        {
            string[] enumNames = Enum.GetNames(typeof(SubConfiguration));
            lstType.Items.Clear();
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(SubConfiguration), item);
                ListItem listItem = new ListItem(item, value.ToString());
                lstType.Items.Add(listItem);
            }
        }

        public static void PopulateView(ListControl lstType)
        {
            string[] enumNames = Enum.GetNames(typeof(SubView));
            lstType.Items.Clear();
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(SubView), item);
                ListItem listItem = new ListItem(item, value.ToString());
                lstType.Items.Add(listItem);
            }
        }

        public static void PopulateManage(ListControl lstType)
        {
            string[] enumNames = Enum.GetNames(typeof(SubManage));
            lstType.Items.Clear();
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(SubManage), item);
                ListItem listItem = new ListItem(item, value.ToString());
                lstType.Items.Add(listItem);
            }
        }

        public static void PopulateAlerts(ListControl lstType)
        {
            string[] enumNames = Enum.GetNames(typeof(SubAlerts));
            lstType.Items.Clear();
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(SubAlerts), item);
                ListItem listItem = new ListItem(item, value.ToString());
                lstType.Items.Add(listItem);
            }
        }

        public static void PopulateITOrchestration(ListControl lstType)
        {
            string[] enumNames = Enum.GetNames(typeof(SubITOrchestration));
            lstType.Items.Clear();
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(SubITOrchestration), item);
                ListItem listItem = new ListItem(item, value.ToString());
                lstType.Items.Add(listItem);
            }
        }

        public static void PopulateReports(ListControl lstType)
        {
            string[] enumNames = Enum.GetNames(typeof(SubReports));
            lstType.Items.Clear();
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(SubReports), item);
                ListItem listItem = new ListItem(item, value.ToString());
                lstType.Items.Add(listItem);
            }
        }

        public static void PopulateSSOProfile(ListControl lstProfile, bool addDefaultItem, int ssoTypeId)
        {
            lstProfile.Items.Clear();
            //var ssoprofileList = NewFacade.GetAllSinglesignOn();
            var ssoprofileList = NewFacade.GetSingleSignonBySSOTypeid(ssoTypeId);
            if (ssoprofileList == null) return;
            {
                var ssoProfileListByOrder = from a in ssoprofileList orderby a.ProfileName ascending select a;
                lstProfile.DataSource = ssoProfileListByOrder;
                lstProfile.DataTextField = "ProfileName";
                lstProfile.DataValueField = "ID";
                lstProfile.DataBind();
            }
            if (addDefaultItem)
            {
                lstProfile.AddDefaultItem(Constants.UIConstants.DropDownItemPleaseSelectSSOProfile);
            }
        }

        public static void CPSLScriptDropDownDataBind(ListControl lstScript, int InfraId)
        {
            var CPSL = NewFacade.GetAllCPSLScript(InfraId);
            if (CPSL != null)
            {
                var CPSLByOrder = from a in CPSL orderby a.CPSLName ascending select a;
                lstScript.DataSource = CPSLByOrder;
                lstScript.DataTextField = "CPSLName";
                lstScript.DataValueField = "Id";
                lstScript.DataBind();

            }
            lstScript.Items.Insert(0, new ListItem("--Select--", "0"));
            lstScript.Items.Insert(1, new ListItem("Create New", "1"));
        }

        public static void WorkFlowDropDownDataBind(ListControl lstWorkflow, int infraobjectId)
        {
            lstWorkflow.Items.Clear();
            var workFlowList = NewFacade.GetExistingWorkflowbyInfraobject(infraobjectId);
            if (workFlowList != null)
            {
                var workflowListByOrder = from a in workFlowList orderby a.Name ascending select a;

                lstWorkflow.DataSource = workflowListByOrder;
                lstWorkflow.DataTextField = "Name";
                lstWorkflow.DataValueField = "Id";
                lstWorkflow.DataBind();

            }
            lstWorkflow.Items.Insert(0, new ListItem("--Select Workflow--", "0")); ;
        }

        public static void WorkFlowDropDownDataBind(ListControl lstWorkflow, int infraobjectId, int chkval)
        {
            lstWorkflow.Items.Clear();
            var workFlowList = NewFacade.GetExistingWorkflowbyInfraobject(infraobjectId);
            var grpwrkflwlist = NewFacade.GetAllGroupWorkflows();

            if (workFlowList != null && grpwrkflwlist != null)
            {
                // var workflowListByOrder = from a in workFlowList orderby a.Name ascending select a;
                if (chkval == 1)
                {
                    var _workflowListByOrder = (from a in workFlowList
                                                join b in grpwrkflwlist on a.Id equals b.WorkflowId
                                                where b.ActionType == 7
                                                select a).ToList();
                    lstWorkflow.DataSource = _workflowListByOrder;
                    lstWorkflow.DataTextField = "Name";
                    lstWorkflow.DataValueField = "Id";
                    lstWorkflow.DataBind();

                }
                else if (chkval == 2)
                {

                    var _workflowListByOrder = from a in workFlowList
                                               join b in grpwrkflwlist on a.Id equals b.WorkflowId
                                               where b.ActionType == 8
                                               select a;
                    lstWorkflow.DataSource = _workflowListByOrder;
                    lstWorkflow.DataTextField = "Name";
                    lstWorkflow.DataValueField = "Id";
                    lstWorkflow.DataBind();


                }
                else
                {
                    var _workflowListByOrder = (from a in workFlowList
                                                join b in grpwrkflwlist on a.Id equals b.WorkflowId
                                                select a).ToList();
                    lstWorkflow.DataSource = _workflowListByOrder;
                    lstWorkflow.DataTextField = "Name";
                    lstWorkflow.DataValueField = "Id";
                    lstWorkflow.DataBind();
                }
            }

            lstWorkflow.Items.Insert(0, new ListItem("--Select--", "0"));

        }

        public static string NetAppDatalag(string Datalag)
        {

            string Lag = string.Empty;
            string inputRPO = string.Empty;
            //if (Datalag != null || Datalag != "" || Datalag != "0.0" || !Datalag.Contains('.') || !!Datalag.Contains("Lag"))
            if (Datalag != null && Datalag != "" && Datalag != "0.0" && !Datalag.Contains('.') && !Datalag.Contains("Lag"))
            {
                string[] datalag = Datalag.Split(':');

                double hrs = Convert.ToDouble(datalag[0]);
                if (hrs > 24)
                {
                    TimeSpan result = TimeSpan.FromHours(hrs);

                    inputRPO = result.Days + "." + result.Hours + ":" + datalag[1] + ":" + datalag[2];

                    Lag = inputRPO;
                }
                else
                    Lag = Datalag;
            }
            else
            {
                Lag = Datalag;
            }
            return Lag;
        }

        public static void PopulateVCenterProfiles(ListControl lstProfile, bool addDefaultItem)
        {
            lstProfile.Items.Clear();

            var VCenterProfileList = NewFacade.GetAllVCenterProfile();

            if (VCenterProfileList != null)
            {
                var VCenterProfileListByOrder = from a in VCenterProfileList orderby a.ProfileName ascending select a;

                lstProfile.DataSource = VCenterProfileListByOrder;
                lstProfile.DataTextField = "ProfileName";
                lstProfile.DataValueField = "Id";
                lstProfile.DataBind();
            }

            if (addDefaultItem)
            {
                lstProfile.AddDefaultItem("- Select Profile - ");
            }
        }

        public static void PopulateVCenterProfilesByType(ListControl lstProfile, bool addDefaultItem, string Type)
        {
            lstProfile.Items.Clear();

            var VCenterProfileList = NewFacade.GetAllVCenterProfileByType(Type);

            if (VCenterProfileList != null)
            {
                var VCenterProfileListByOrder = from a in VCenterProfileList orderby a.ProfileName ascending select a;

                lstProfile.DataSource = VCenterProfileListByOrder;
                lstProfile.DataTextField = "ProfileName";
                lstProfile.DataValueField = "Id";
                lstProfile.DataBind();
            }

            if (addDefaultItem)
            {
                lstProfile.AddDefaultItem(" -- Select Profile -- ");
            }
        }

        public static void PopulateVMServer(ListControl lstServer, bool addDefaultItem)
        {
            lstServer.Items.Clear();
            var serverList = NewFacade.GetAllServers();

            serverList = (from srvr in serverList
                          where srvr.IsVirtualGuestOS == 1
                          select srvr).ToList();

            if (serverList != null)
            {
                var serverListByOrder = from a in serverList orderby a.Name ascending select a;

                lstServer.DataSource = serverListByOrder;
                lstServer.DataTextField = "Name";
                lstServer.DataValueField = "Id";
                lstServer.DataBind();
            }
            if (addDefaultItem)
            {
                lstServer.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName);
            }
        }

        public static string GetHostNameByServerId(int ServerId)
        {
            Server svr = NewFacade.GetServerById(Convert.ToInt32(ServerId));
            if (svr != null)
            {
                return (svr != null ? svr.Name : "NA");
            }
            else
            {
                return "NA";
            }
        }

        public static string GetPRDBNameByServerId(int InfraId, int ServerId)
        {
            IList<DatabaseBase> prdatabase = NewFacade.GetDatabaseBasesByServerId(ServerId);
            var exchangeDAG = NewFacade.GetInfraObjectById(InfraId);
            string DBSID = null;
            if (prdatabase != null)
            {
                if (prdatabase != null)
                {
                    //if (prdatabase[0].Type.ToString() == "PRDatabase")
                    //{
                    if (prdatabase[0].DatabaseType.ToString() == "Oracle")
                    {
                        DatabaseOracle oracledb = NewFacade.GetDatabaseOracleByDatabaseBaseId(prdatabase[0].Id);
                        DBSID = oracledb != null ? oracledb.OracleSID : "NA";
                    }
                    else if (prdatabase[0].DatabaseType.ToString() == "OracleRac")
                    {
                        IList<DatabaseNodes> dbnode = NewFacade.GetAllDataBaseNodesByDatabaseId(prdatabase[0].Id);
                        Nodes nod = NewFacade.GetNodesById(dbnode[0].NodeId);
                        DBSID = nod != null ? nod.OracleSID : "NA";
                    }
                    else if (prdatabase[0].DatabaseType.ToString() == "Sql")
                    {
                        DatabaseSql sql = NewFacade.GetDatabaseSqlByDatabaseBaseId(prdatabase[0].Id);
                        DBSID = sql != null ? sql.DatabaseSID : "NA";
                    }
                    else if (prdatabase[0].DatabaseType.ToString() == "Exchange")
                    {
                        DatabaseExchange exchange = NewFacade.GetDatabaseExchangeByDatabaseBaseId(prdatabase[0].Id);
                        DBSID = exchange != null ? "NA" : "NA";
                    }
                    else if (prdatabase[0].DatabaseType.ToString() == "DB2")
                    {
                        DatabaseDB2 db2 = NewFacade.GetDatabaseDb2ByDatabaseBaseId(prdatabase[0].Id);
                        DBSID = db2 != null ? db2.DatabaseSID : "NA";
                    }
                    else if (prdatabase[0].DatabaseType.ToString() == "SQLNative2008")
                    {
                        DatabaseSqlNative2008 Sqldb = NewFacade.GetDatabaseMSSqlByDatabaseBaseId(prdatabase[0].Id);
                        DBSID = Sqldb != null ? Sqldb.DatabaseName : "NA";
                        //DBSID = "NA";
                    }
                    else if (prdatabase[0].DatabaseType.ToString() == "SyBase")
                    {
                        DataBaseSyBase SyBase = NewFacade.GetDatabaseSybaseByDatabaseBaseId(prdatabase[0].Id);
                        DBSID = SyBase != null ? SyBase.DatabaseSID : "NA";
                    }
                    else if (prdatabase[0].DatabaseType.ToString() == "Postgres9x")
                    {
                        DatabasePostgre9x postgredb = NewFacade.GetDatabasePostgre9xByDatabaseBaseId(prdatabase[0].Id);
                        DBSID = postgredb != null ? postgredb.DatabaseName : "NA";
                    }
                    else if (prdatabase[0].DatabaseType.ToString() == "PostgreSQL")
                    {
                        PostgreSql postgredb = NewFacade.GetDatabasePostgreSqlByDatabaseBaseId(prdatabase[0].Id);
                        DBSID = postgredb != null ? postgredb.DatabaseName : "NA";
                    }
                    else if (prdatabase[0].DatabaseType.ToString() == "MySQL")
                    {
                        DatabaseBase MySqlDB = NewFacade.GetDatabaseBaseById(prdatabase[0].Id);
                        DBSID = MySqlDB != null ? MySqlDB.Name : "NA";
                    }
                    else if (prdatabase[0].DatabaseType.ToString() == "SyBaseWithSrs")
                    {
                        DatabaseSybaseWithSrs maxdb = NewFacade.GetDatabaseSybaseWithSrsByDatabaseBaseId(prdatabase[0].Id);
                        DBSID = maxdb != null ? maxdb.DatabaseSID : "NA";
                    }
                    else if (prdatabase[0].DatabaseType.ToString() == "MaxDB")
                    {
                        DatabaseMaxDB maxdb = NewFacade.GetDatabaseMaxDBByDBBaseId(prdatabase[0].Id);
                        DBSID = maxdb != null ? maxdb.DatabaseSID : "NA";
                    }
                    //}
                    else
                        DBSID = "NA";
                }
            }
            else if (exchangeDAG != null)
            {
                if (exchangeDAG.RecoveryType == 20)
                {
                    IList<DataBaseExchangeDAG> exc = NewFacade.GetDatabaseExchangeDAGByServerId(exchangeDAG.PRServerId);
                    DBSID = exc != null ? exc[0].MailBoxDBName : "NA";
                }
            }
            else
            {
                return "NA";
            }
            if (DBSID != null)
                return DBSID;
            else
                return "NA";
        }

        public static string GetDRDBNameByServerId(int InfraId, int ServerId)
        {
            IList<DatabaseBase> drdatabase = NewFacade.GetDatabaseBasesByServerId(ServerId);
            var exchangeDAG = NewFacade.GetInfraObjectById(InfraId);
            string DBSID = null;
            if (drdatabase != null)
            {
                if (drdatabase != null)
                {
                    //if (drdatabase[0].Type.ToString() == "DRDatabase")
                    //{
                    if (drdatabase[0].DatabaseType.ToString() == "Oracle")
                    {
                        DatabaseOracle oracledb = NewFacade.GetDatabaseOracleByDatabaseBaseId(drdatabase[0].Id);
                        DBSID = oracledb != null ? oracledb.OracleSID : "NA";
                    }
                    else if (drdatabase[0].DatabaseType.ToString() == "OracleRac")
                    {
                        IList<DatabaseNodes> dbnode = NewFacade.GetAllDataBaseNodesByDatabaseId(drdatabase[0].Id);
                        Nodes nod = NewFacade.GetNodesById(dbnode[0].NodeId);
                        DBSID = nod != null ? nod.OracleSID : "NA";
                    }
                    else if (drdatabase[0].DatabaseType.ToString() == "Sql")
                    {
                        DatabaseSql sql = NewFacade.GetDatabaseSqlByDatabaseBaseId(drdatabase[0].Id);
                        if (sql != null)
                            DBSID = sql.DatabaseSID;
                        else
                            DBSID = "NA";
                    }
                    else if (drdatabase[0].DatabaseType.ToString() == "Exchange")
                    {
                        DatabaseExchange exchange = NewFacade.GetDatabaseExchangeByDatabaseBaseId(drdatabase[0].Id);
                        DBSID = exchange != null ? "NA" : "NA";
                    }
                    else if (drdatabase[0].DatabaseType.ToString() == "DB2")
                    {
                        DatabaseDB2 db2 = NewFacade.GetDatabaseDb2ByDatabaseBaseId(drdatabase[0].Id);
                        DBSID = db2 != null ? db2.DatabaseSID : "NA";
                    }
                    else if (drdatabase[0].DatabaseType.ToString() == "SQLNative2008")
                    {
                        DatabaseSqlNative2008 Sqldb = NewFacade.GetDatabaseMSSqlByDatabaseBaseId(drdatabase[0].Id);
                        DBSID = Sqldb != null ? Sqldb.DatabaseName : "NA";
                        //DBSID ="NA";
                    }
                    else if (drdatabase[0].DatabaseType.ToString() == "SyBase")
                    {
                        DataBaseSyBase SyBase = NewFacade.GetDatabaseSybaseByDatabaseBaseId(drdatabase[0].Id);
                        DBSID = SyBase != null ? SyBase.DatabaseSID : "NA";
                    }
                    else if (drdatabase[0].DatabaseType.ToString() == "Postgres9x")
                    {
                        DatabasePostgre9x postgredb = NewFacade.GetDatabasePostgre9xByDatabaseBaseId(drdatabase[0].Id);
                        DBSID = postgredb != null ? postgredb.DatabaseName : "NA";
                    }
                    else if (drdatabase[0].DatabaseType.ToString() == "PostgreSQL")
                    {
                        PostgreSql postgredb = NewFacade.GetDatabasePostgreSqlByDatabaseBaseId(drdatabase[0].Id);
                        DBSID = postgredb != null ? postgredb.DatabaseName : "NA";
                    }
                    else if (drdatabase[0].DatabaseType.ToString() == "MySQL")
                    {
                        DatabaseBase MySqlDB = NewFacade.GetDatabaseBaseById(drdatabase[0].Id);
                        DBSID = MySqlDB != null ? MySqlDB.Name : "NA";
                    }
                    else if (drdatabase[0].DatabaseType.ToString() == "SyBaseWithSrs")
                    {
                        DatabaseSybaseWithSrs maxdb = NewFacade.GetDatabaseSybaseWithSrsByDatabaseBaseId(drdatabase[0].Id);
                        DBSID = maxdb != null ? maxdb.DatabaseSID : "NA";
                    }
                    else if (drdatabase[0].DatabaseType.ToString() == "MaxDB")
                    {
                        DatabaseMaxDB maxdb = NewFacade.GetDatabaseMaxDBByDBBaseId(drdatabase[0].Id);
                        DBSID = maxdb != null ? maxdb.DatabaseSID : "NA";
                    }
                    // }
                    else
                        DBSID = "NA";
                }
            }
            else if (exchangeDAG != null)
            {
                if (exchangeDAG.RecoveryType == 20)
                {
                    IList<DataBaseExchangeDAG> exc = NewFacade.GetDatabaseExchangeDAGByServerId(exchangeDAG.DRServerId);
                    DBSID = exc != null ? exc[0].MailBoxDBName : "NA";
                }
            }
            else
            {
                return "NA";
            }
            if (DBSID != null)
                return DBSID;
            else
                return "NA";
        }

        public static string GetPRDBName_MSSQLByServerId(int InfraId, int ServerId)
        {
            IList<DatabaseBase> prdatabase = NewFacade.GetDatabaseBasesByServerId(ServerId);
            var exchangeDAG = NewFacade.GetInfraObjectById(InfraId);
            string PRDBName = null;
            if (prdatabase != null)
            {
                if (prdatabase != null)
                {
                    if (prdatabase[0].DatabaseType.ToString() == "SQLNative2008")
                    {
                        DatabaseSqlNative2008 Sqldb = NewFacade.GetDatabaseMSSqlByDatabaseBaseId(prdatabase[0].Id);
                        PRDBName = Sqldb != null ? Sqldb.DatabaseName : "NA";
                    }
                }
            }
            else
            {
                return "NA";
            }
            if (PRDBName != null)
                return PRDBName;
            else
                return "NA";
        }

        public static string GetDRDBName_MSSQLByServerId(int InfraId, int ServerId)
        {
            IList<DatabaseBase> drdatabase = NewFacade.GetDatabaseBasesByServerId(ServerId);
            string DRDBName = null;
            if (drdatabase != null)
            {
                if (drdatabase != null)
                {
                    if (drdatabase[0].DatabaseType.ToString() == "SQLNative2008")
                    {
                        DatabaseSqlNative2008 Sqldb = NewFacade.GetDatabaseMSSqlByDatabaseBaseId(drdatabase[0].Id);
                        DRDBName = Sqldb != null ? Sqldb.DatabaseName : "NA";
                    }
                }
            }
            else
            {
                return "NA";
            }
            if (DRDBName != null)
                return DRDBName;
            else
                return "NA";
        }

        public static string ConvertKilobytesToMegabytes(string kilobytes)
        {
            if (kilobytes != "")
            {
                double inKB = Convert.ToDouble(kilobytes);
                double inMB = inKB / 1024f;
                string sizeMB = Math.Round(inMB, 2).ToString();
                return sizeMB;
            }
            else
            {
                string nullSize = "NA";
                return nullSize;
            }
        }

        public static string putnewline(string str1, int len)
        {
            string str = string.Empty;
            // string str1 = "abcdefghijklmnopqrstuvwxyz";
            int q = str1.Length / len;
            int m = str1.Length % len;
            int pos = 0;
            for (int i = 0; i < q; i++)
            {
                string str2 = str1.Substring(pos, len);
                str = str + str2 + Environment.NewLine;
                pos = pos + len;
            }
            str = str + str1.Substring(pos, m);
            return str;
        }




        public static string convertSecToDatalag(string sec)
        {
            if (sec != "")
            {
                double t = Convert.ToDouble(sec);
                var datalag = string.Format("{0:00}:{1:00}:{2:00}", t / 3600, (t / 60) % 60, t % 60);
                return datalag;
            }
            else
            {
                string na = "NA";
                return na;
            }
        }

        //public static string Formatdate(string strdt)
        //{

        //    DateTime dt;
        //    string[] str = strdt.Contains('/') ? strdt.Split('/') : strdt.Split('-');
        //    string[] str1 = str[2].Split(' ');
        //    string str3 = str1[0] + '-' + str[0] + '-' + str[1] + ' ' + str1[1];
        //    DateTime.TryParse(str3, out dt);

        //    return dt.ToString("dd-MM-yyyy hh:mm:ss tt");
        //}

        //public static string Formatdate(string strdt, string frmdt)
        //{

        //    DateTime dt;
        //    string[] str = strdt.Contains('/') ? strdt.Split('/') : strdt.Split('-');
        //    string[] str1 = str[2].Split(' ');
        //    string str3 = str1[0] + '-' + str[0] + '-' + str[1] + ' ' + str1[1];
        //    DateTime.TryParse(str3, out dt);

        //    return dt.ToString(frmdt);
        //}

        public static string Formatdate(string strdt)
        {

            DateTime dt;
            string str3;
            string[] str = strdt.Contains('/') ? strdt.Split('/') : strdt.Split('-');
            string[] str1 = str[2].Split(' ');
            if (str1.Length < 2)
                str3 = str1[0] + '-' + str[0] + '-' + str[1];
            else
                str3 = str1[0] + '-' + str[0] + '-' + str[1] + ' ' + str1[1];
            DateTime.TryParse(str3, out dt);

            return dt.ToString("dd-MM-yyyy hh:mm:ss tt");
        }

        public static string FormatDate_String(string Date)
        {
            string strDate = string.Empty;

            try
            {
                strDate = Convert.ToDateTime(Date).ToString("MM-dd-yyyy HH:mm:ss");
            }
            catch (Exception)
            {
                try
                {
                    strDate = DateTime.Parse(Date, CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                }
                catch (Exception)
                {
                    try
                    {
                        strDate = DateTime.ParseExact(Date, "dd-MM-yyyy hh:mm:ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                    }
                    catch (Exception)
                    {
                        try
                        {
                            strDate = DateTime.ParseExact(Date, "dd-M-yyyy hh:mm:ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                        }
                        catch (Exception)
                        {
                            try
                            {
                                strDate = DateTime.ParseExact(Date, "dd-MM-yyyy HH:mm:ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                            }
                            catch (Exception)
                            {
                                try
                                {
                                    strDate = DateTime.ParseExact(Date, "dd-M-yyyy HH:mm:ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                                }
                                catch (Exception)
                                {
                                    try
                                    {
                                        strDate = DateTime.ParseExact(Date, "dd-M-yyyy HH.mm.ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                                        //string Date_Format = System.Configuration.ConfigurationManager.AppSettings["DateFormat_CP"];
                                        //strDate = DateTime.ParseExact(Date, Date_Format, CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                                    }
                                    catch (Exception)
                                    {
                                        try
                                        {
                                            strDate = DateTime.ParseExact(Date, "dd-MM-yyyy HH.mm.ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                                        }
                                        catch (Exception)
                                        {
                                            try
                                            {
                                                strDate = DateTime.ParseExact(Date, "dd-M-yyyy hh.mm.ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                                            }
                                            catch (Exception)
                                            {
                                                try
                                                {
                                                    strDate = DateTime.ParseExact(Date, "dd-MM-yyyy hh.mm.ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                                                }
                                                catch (Exception)
                                                {
                                                    try
                                                    {
                                                        strDate = DateTime.ParseExact(Date, "dd-MM-yyyy hh.mm.ss tt", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                                                    }
                                                    catch (Exception)
                                                    {
                                                        try
                                                        {
                                                            strDate = DateTime.ParseExact(Date, "dd-M-yyyy hh.mm.ss tt", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                                                        }
                                                        catch (Exception)
                                                        {
                                                            try
                                                            {
                                                                strDate = DateTime.ParseExact(Date, "yyyyMMddHHmmss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
                                                            }
                                                            catch (Exception ex)
                                                            {

                                                            }
                                                        }
                                                    }
                                                    //string date_format = system.configuration.configurationmanager.appsettings["dateformat_cp"];
                                                    //strdate = datetime.parseexact(date, date_format, cultureinfo.invariantculture).tostring("mm-dd-yyyy hh:mm:ss");
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return strDate;
        }

        public static string Formatdate(string strdt, string frmt)
        {

            DateTime dt;
            string str3;
            string[] str = strdt.Contains('/') ? strdt.Split('/') : strdt.Split('-');
            string[] str1 = str[2].Split(' ');
            if (str1.Length < 2)
                str3 = str1[0] + '-' + str[0] + '-' + str[1];
            else
                str3 = str1[0] + '-' + str[0] + '-' + str[1] + ' ' + str1[1];
            DateTime.TryParse(str3, out dt);

            return dt.ToString(frmt);
        }

        //public static string FormatDate_String(string Date)
        //{
        //    string strDate = string.Empty;

        //    try
        //    {
        //        strDate = Convert.ToDateTime(Date).ToString("MM-dd-yyyy HH:mm:ss");
        //    }
        //    catch (Exception)
        //    {
        //        try
        //        {
        //            strDate = DateTime.Parse(Date, CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //        }
        //        catch (Exception)
        //        {
        //            try
        //            {
        //                strDate = DateTime.ParseExact(Date, "dd-MM-yyyy hh:mm:ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //            }
        //            catch (Exception)
        //            {
        //                try
        //                {
        //                    strDate = DateTime.ParseExact(Date, "dd-M-yyyy hh:mm:ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //                }
        //                catch (Exception)
        //                {
        //                    try
        //                    {
        //                        strDate = DateTime.ParseExact(Date, "dd-MM-yyyy HH:mm:ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //                    }
        //                    catch (Exception)
        //                    {
        //                        try
        //                        {
        //                            strDate = DateTime.ParseExact(Date, "dd-M-yyyy HH:mm:ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //                        }
        //                        catch (Exception)
        //                        {
        //                            try
        //                            {
        //                                strDate = DateTime.ParseExact(Date, "dd-M-yyyy HH.mm.ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //                                //string Date_Format = System.Configuration.ConfigurationManager.AppSettings["DateFormat_CP"];
        //                                //strDate = DateTime.ParseExact(Date, Date_Format, CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //                            }
        //                            catch (Exception)
        //                            {
        //                                try
        //                                {
        //                                    strDate = DateTime.ParseExact(Date, "dd-MM-yyyy HH.mm.ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //                                }
        //                                catch (Exception)
        //                                {
        //                                    try
        //                                    {
        //                                        strDate = DateTime.ParseExact(Date, "dd-M-yyyy hh.mm.ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //                                    }
        //                                    catch (Exception)
        //                                    {
        //                                        try
        //                                        {
        //                                            strDate = DateTime.ParseExact(Date, "dd-MM-yyyy hh.mm.ss", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //                                        }
        //                                        catch (Exception)
        //                                        {
        //                                            try
        //                                            {
        //                                                strDate = DateTime.ParseExact(Date, "dd-MM-yyyy hh.mm.ss tt", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //                                            }
        //                                            catch (Exception)
        //                                            {
        //                                                try
        //                                                {
        //                                                    strDate = DateTime.ParseExact(Date, "dd-M-yyyy hh.mm.ss tt", CultureInfo.InvariantCulture).ToString("MM-dd-yyyy HH:mm:ss");
        //                                                }
        //                                                catch (Exception)
        //                                                {

        //                                                }
        //                                            }
        //                                            //string date_format = system.configuration.configurationmanager.appsettings["dateformat_cp"];
        //                                            //strdate = datetime.parseexact(date, date_format, cultureinfo.invariantculture).tostring("mm-dd-yyyy hh:mm:ss");
        //                                        }
        //                                    }
        //                                }
        //                            }
        //                        }
        //                    }
        //                }
        //            }
        //        }
        //    }
        //    return strDate;
        //}

        public static string ConvertBytesToMegabytes(string bytes)
        {
            if (bytes != "" && bytes != "NA")
            {
                double inByte = Convert.ToDouble(bytes);
                double inMB = (inByte / 1024f) / 1024;
                string sizeMB = Math.Round(inMB, 2).ToString();
                return sizeMB;
            }
            else
            {
                string nullSize = "NA";
                return nullSize;
            }

        }
        /*Report AODG*/
        public static string SplitFunction(string Seq, string Thread)
        {
            try
            {
                if (!string.IsNullOrEmpty(Seq) && !string.IsNullOrEmpty(Thread))
                {
                    string[] SeqArr = Seq.Split(',');
                    string[] ThreadArr = Thread.Split(',');

                    int length = SeqArr.Length;

                    string SeqThread = string.Empty;

                    for (int i = 0; i < length; i++)
                    {
                        SeqThread = SeqThread + SeqArr[i] + " Thread(#" + ThreadArr[i] + ")<BR/>";
                    }
                    return SeqThread;
                }
                else
                { return "NA"; }
            }
            catch (Exception ex)
            { return "NA"; }
        }

        public static string AppendValues(string Seq, string Thread)
        {
            try
            {
                string SeqThread = string.Empty;
                if (string.IsNullOrEmpty(Seq) && string.IsNullOrEmpty(Thread))
                {
                    return "NA";
                }
                else if (!string.IsNullOrEmpty(Seq) || !string.IsNullOrEmpty(Thread))
                {
                    Seq = !string.IsNullOrEmpty(Seq) ? Seq : "NA";
                    Thread = !string.IsNullOrEmpty(Thread) ? Thread : "NA";

                    SeqThread = SeqThread + Seq + " Thread(#" + Thread + ")";

                    return SeqThread;
                }
                else
                { return "NA"; }
            }
            catch (Exception ex)
            { return "NA"; }
        }


        /// <summary>
        /// For Report AODGNONODG oracle with datasync 11g and 12c
        /// Author : Hanumant Sawant 04/11/2017
        /// </summary>
        /// <param name="iDataLag"></param>
        /// <returns></returns>
        public static string AppendValues2(double Seq, int Thread)
        {
            try
            {
                string SeqThread = string.Empty;
                if (Seq == null || Seq.ToString() == "" && Thread == null || Thread == 0)
                {
                    return "NA";
                }
                else if (Seq != null || Seq.ToString() != "" && Thread != null || Thread != 0)
                {
                    //Seq = Seq != null || Seq != "" ? Seq : "NA";
                    Thread = Thread != null || Thread != 0 ? Thread : ("NA").ToInteger();

                    SeqThread = SeqThread + Seq + " Thread(#" + Thread + ")";

                    return SeqThread;
                }
                else
                { return "NA"; }
            }
            catch (Exception ex)
            { return "NA"; }
        }

        /// <summary>
        /// Remove -(minus) from datalag and add days
        /// Author : Hanumant Sawant 04/11/2017
        /// </summary>
        /// <param name="iDataLag"></param>
        /// <returns></returns>
        public static string Format_DataLag(string iDataLag)
        {
            try
            {
                if (!string.IsNullOrEmpty(iDataLag))
                {
                    if (iDataLag.Contains("-"))
                    {
                        iDataLag = iDataLag.Remove(iDataLag.IndexOf("-"), 1);
                        if (iDataLag.Length <= 8)
                        {
                            iDataLag = "00." + iDataLag;
                        }
                        return iDataLag;
                    }
                    else
                    {

                        if (iDataLag.Length <= 8)
                        {
                            iDataLag = "00." + iDataLag;
                        }
                        return iDataLag;

                    }
                }
                else
                {
                    return "00.00:00:00";
                }
            }
            catch (Exception ex)
            {
                return "NA";
            }
        }

        public static void PopulateAlertType(ListControl lstddlAlertType, bool addDefaultItem, string type)
        {
            lstddlAlertType.Items.Clear();
            //if (businessServiceId > 0)
            //{
            var functionList = NewFacade.GetAlertTypeBySeverity(type);
            if (functionList != null)
            {
                lstddlAlertType.DataSource = functionList;
                lstddlAlertType.DataTextField = "TYPE";
                //lstddlAlertType.DataValueField = "Id";
                lstddlAlertType.DataBind();
            }
            //  }
            if (addDefaultItem)
                lstddlAlertType.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectType);
        }

        public static void PopulateProfiles(ListControl lstType, int UserID)
        {
            //string[] enumNames = Enum.GetNames(typeof(SubConfiguration));
            // var enumNames = NewFacade.GetAllParallelProfile();
            var enumNames = NewFacade.GetAttachParallelProfileByUserId(UserID);

            lstType.Items.Clear();
            if (enumNames.Count > 0)
            {
                lstType.DataSource = enumNames;
                lstType.DataTextField = "ProfileName";
                lstType.DataValueField = "ID";
                lstType.DataBind();
                lstType.Items.Insert(0, new ListItem("ALL", "0"));
            }
        }

        /*Report AODG*/
        #region Data Security
        /// <summary>
        /// Genrate th Hash Using Guid and 64 encode
        /// Author : Niteen M. 02/10/2016
        /// </summary>
        /// <param name="strPass"></param>
        /// <returns></returns>
        public static string getHashKeyByString(string strPass, string strguid) //pras
        {
            string strResult = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(strPass))
                {
                    return string.Empty;
                }
                strPass = Base64Encode(strPass);
                string strGuid = strguid;// hdfStaticGuid.Value;
                string[] strKeyArr = strGuid.Split('-');// hdfId.Value.Split('-');
                int j = 0;
                for (int i = 0; i < strPass.Length; i++)
                { 
                    if (i % 5 == 0)
                        j = 0;
                    strKeyArr[j] += strPass[i];
                    j++;
                }
                foreach (var data in strKeyArr)
                {
                    strResult += data + "-";
                }
            }
            catch (IndexOutOfRangeException indexOutOf)
            {
                log.Info("getHashKeyByString", "Error While Genrate Hash with Guid" + indexOutOf.Message);
            }
            return strResult.Remove(strResult.Length - 1, 1);
        }

        /// <summary>
        /// get original encrypt string from Hash
        /// Author : Niteen M. 02/10/2016
        /// </summary>
        /// <param name="strHash"></param>
        /// <returns></returns>
        public static string getOriginalEncryData(string strHash, string guid) //pras
        {
            if (string.IsNullOrEmpty(strHash))
                return string.Empty;
            //miru
            if (!strHash.Contains('-'))
                if (Regex.Matches(strHash, "-").Count == 0)
                    return strHash;
            //miru
            string strGuid = guid;// hdfGId.Value;
            string s = string.Empty;
            string strResult = string.Empty;
            try
            {
                string[] guidArr = strGuid.Split('-');
                string[] hashArr = strHash.Split('-');
                string[] data = new string[guidArr.Length];
                for (int i = 0; i < guidArr.Length; i++)
                {
                    // strResult += hashArr[i].Substring(guidArr[i].Length, (hashArr[i].Length - guidArr[i].Length));
                    data[i] = hashArr[i].Substring(guidArr[i].Length, (hashArr[i].Length - guidArr[i].Length));
                }
                int index = data.Select((x, i) => new { Length = x.Length, Index = i }).OrderBy(x => x.Length).Last().Index;
                int q = data[index].Length;
                int t = 0;
                for (int a = 0; a < 5; ++a)
                {
                    if (string.IsNullOrEmpty(data[0]) && string.IsNullOrEmpty(data[1]) && string.IsNullOrEmpty(data[2]) && string.IsNullOrEmpty(data[3]) && string.IsNullOrEmpty(data[4]))
                        return Decodeval(strResult);
                    if (t == 5)
                        a = t = 0;
                    if (data[a].Length > 0)
                    {
                        strResult += data[a][0];
                        data[a] = data[a].Remove(0, 1);
                    }
                    if (a == 4)
                        a = 0;
                    t++;
                }
            }
            catch (Exception indexOutOf)
            {
                log.Info("getHashKeyByString", "Error While Genrate Encrypted data from Hash" + indexOutOf.Message);
            }
            return Decodeval(strResult);
        }


        public static string _AppendValues(string text)
        {

            if (text == " ")
            {
                return "NA";
            }
            else if (!string.IsNullOrEmpty(text))
            {
                return text;
            }
            else
            {
                return "NA";
            }
        }

        //public static string _AppendValuess(int text)
        //{

        //    if (text ==null  )
        //    {
        //        return "NA";
        //    }
        //    else if (text!=null)
        //    {
        //        return text.ToString();
        //    }
        //    else
        //    {
        //        return "NA";
        //    }
        //}


        /// <summary>
        /// convert plaintext string to 64 encode
        /// Author Niteen M. 02/10/2016
        /// </summary>
        /// <param name="plainText"></param>
        /// <returns></returns>
        public static string Base64Encode(string plainText)
        {
            string actual = string.Empty;
            try
            {
                var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
                var input = System.Convert.ToBase64String(plainTextBytes);
                for (int i = 0; i < input.Length; i++)
                {
                    actual = actual + input[i + 1] + input[i];
                    i++;
                }
            }
            catch (Exception ex)
            {
                log.Info("getHashKeyByString", "Error While Genrate Encrypted data from Hash" + ex.Message);
            }
            return actual;
        }


        public static string Decodeval(string input)
        {
            var decode = "";
            if (!string.IsNullOrEmpty(input))
            {
                string actual = string.Empty;
                for (int i = 0; i < input.Length; i++)
                {
                    actual = actual + input[i + 1] + input[i];
                    i++;
                }
                var base64EncodedBytes = System.Convert.FromBase64String(actual);
                decode = System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
            }
            return decode;

        }
        #endregion Data Security

        #region eBDRPostgresDB

        public static string GetOsIcon(string OsName)
        {
            string Icon = "";

            if (OsName.ToUpper().Contains("WINDOWS"))
            {
                Icon = "icon-Windows";
            }
            else if (OsName.ToUpper().Contains("AIX"))
            {
                Icon = "icon-aix";
            }
            else if (OsName.ToUpper().Contains("RED HAT") || OsName.ToUpper().Contains("UBUNTU") || OsName.ToUpper().Contains("LINUX") || OsName.ToUpper().Contains("RHEL") || OsName.ToUpper().Contains("CENTOS") || OsName.ToUpper().Contains("ASIAN"))
            {
                Icon = "icon-linux";
            }
            else if (OsName.ToUpper().Contains("HPUX"))
            {
                Icon = "icon-hpux";
            }
            else if (OsName.ToUpper().Contains("SOLARIS"))
            {
                Icon = "icon-solaris";
            }
            else if (OsName.ToUpper().Contains("DOS"))
            {
                Icon = "icon-ms-dos";
            }
            //_logger.Info("End Getting OS Icon : " + Icon);
            return Icon;
        }

        public static string GetPostgresConnectionString(int DatabaseId, int ServerID)
        {
            string ConnectionString = "SERVER=*************;Port=5432;Database=eBDR;User name=********;Password=********";
            Server _Server = NewFacade.GetServerById(ServerID);
            DatabasePostgre9x _DatabasePostgre9x = NewFacade.GetDatabasePostgre9xByDatabaseBaseId(DatabaseId);
            string Server = CryptographyHelper.Md5Decrypt(_Server.IPAddress);
            string Port = "5432";
            string Database = _DatabasePostgre9x.DatabaseName;
            string UserName = CryptographyHelper.Md5Decrypt(_DatabasePostgre9x.UserName);
            string Password = CryptographyHelper.Md5Decrypt(_DatabasePostgre9x.Password);
            if (_DatabasePostgre9x != null)
            {
                ConnectionString = "SERVER=" + Server + ";Port=" + Port + ";Database=" + Database + ";User name=" + UserName + ";Password=" + Password + "";
            }
            return ConnectionString;
        }
        #endregion


        public static Type getReplicationTypefromVal(int value)
        {
            string replicationtypVal = string.Empty;
            switch (value)
            {
                case (int)ReplicationType.OracleWithDataSync:

                    return typeof(OracleJobList);

                case (int)ReplicationType.EMCSRDFMysqlFullDB:
                    return typeof(EMCSRDFMysqlFullDB);

                case (int)ReplicationType.OracleDataGuard:
                    return typeof(JobList1);

                case (int)ReplicationType.SQLNative2008:

                    return typeof(MSSqlNative2008);
                case (int)ReplicationType.EnterPriseDB:

                    return typeof(Enterprisedb);
                case (int)ReplicationType.EMCSRDFMSSQLFullDB:

                    return typeof(EMCSRDFMSSQLFullDB);

                case (int)ReplicationType.Postgres9X:

                    return typeof(Postgre9XJob);

                case (int)ReplicationType.MSSCR:

                    return typeof(MonintorExchangeJob);

                case (int)ReplicationType.MSSQLServerNative:

                    return typeof(MSSqlNativeLogjob);

                case (int)ReplicationType.DB2HADR:

                    return typeof(MonitorHADRJob);

                case (int)ReplicationType.EMCSRDFOracleFullDB:

                    return typeof(EMCSRDF_OracleFullDB);

                case (int)ReplicationType.SRMVMware:

                    return typeof(SRMVMwareMonitorJob);

                case (int)ReplicationType.SVCGlobalMirrorOracleFullDBRac:

                    return typeof(SVCGlobalMirrorOracleFullDBRac);

                case (int)ReplicationType.NetAppSnapMirrorPostgresFullDB:

                    return typeof(NetAppSnapMirrorPostgresFullDB);

                case (int)ReplicationType.HitachiURMSSQLFullDB:

                    return typeof(NetAppSnapMirrorPostgresFullDB);

                case (int)ReplicationType.VMWareWithSVC:

                    return typeof(VMWareWithSVC);

                case (int)ReplicationType.DB2IBMGLobalmirror:

                    return typeof(DB2IBMGlobalMirror);

                case (int)ReplicationType.SVCMSSQLFullDB:

                    return typeof(SVCMSSQLFullDB);

                case (int)ReplicationType.MSSQLDBMirroring:

                    return typeof(MSSQLDBMirroring);

                case (int)ReplicationType.OracleFullDBSVCGlobalMirror:

                    return typeof(OracleFullDBSVCGlobalMirror);

                case (int)ReplicationType.MySqlNativeLogShipping:

                    return typeof(MySqlNativeLogShipping);

                case (int)ReplicationType.EMCSRDFOracleRacFullDB:

                    return typeof(EmcsrfdOracleFullDBRac);

                case (int)ReplicationType.HitachiOracleFullDBRac:

                    return typeof(HitachiOracleFullDBRac);

                case (int)ReplicationType.SyBaseWithDataSync:

                    return typeof(SYBASEDataBase);

                case (int)ReplicationType.MSSQLDoubleTakeFullDB:

                    return typeof(MSSQLDoubleTakeFullDB);

                case (int)ReplicationType.DB2HADR9X:


                    return typeof(DB2HADR9X);

                case (int)ReplicationType.MySQLNative:


                    return typeof(MySqlNativeJob);

                case (int)ReplicationType.SqlServerDataSync:

                    return typeof(Sql2000FastCopy);

                case (int)ReplicationType.OracleFullDBIBMGlobalMirror:

                    return typeof(OracleFullDbGlobalMirror);

                case (int)ReplicationType.NetAppSnapMirror:

                    return typeof(NetAppSnapMirror);

                case (int)ReplicationType.VMWareSnapMirror:


                    return typeof(VMWareSnapmirror);

                case (int)ReplicationType.MSExchangeDAG:

                    return typeof(MSExchangeDAG);

                case (int)ReplicationType.OracleFullDBNetAppSnapMirror:

                    return typeof(OracleFullDB_NetAppSnapMirror);

                case (int)ReplicationType.MSSqlNetAppSnapMirror:
                    return typeof(MSSqlNetAppSnapMirror);

                case (int)ReplicationType.HITACHIUROracleFullDB:

                    return typeof(HITACHIUROracleFullDB);


                case (int)ReplicationType.VMWareHitachiUR:

                    return typeof(VMWareHitachiUR);

                case (int)ReplicationType.MSSQLDataBaseMirror:

                    return typeof(MSSQLDataBaseMirror);
                case (int)ReplicationType.MySQLGlobalMirrorFullDB:

                    return typeof(MySQLGlobalMirrorFullDBlist);
                case (int)ReplicationType.EC2S3DataSync:

                    return typeof(EC2S3DataSynclist);

                case (int)ReplicationType.ApplicationDoubleTake:

                    return typeof(ApplicationDoubleTakelist);

                case (int)ReplicationType.MIMIX:

                    return typeof(MIMIXlist);

                case (int)ReplicationType.HyperV:

                    return typeof(HyperVlist);

                case (int)ReplicationType.AppHitachiUr:

                    return typeof(AppHitachiUrlist);
                case (int)ReplicationType.GlobalMirrorMSSQLFullDB:

                    return typeof(GlobalMirrorMssqlFullDBlist);
                case (int)ReplicationType.MaxDBWithDataSync:

                    return typeof(MaxDBWithDataSynclist);
                case (int)ReplicationType.SybaseWithSRS:

                    return typeof(SybaseWithSRSlist);

                case (int)ReplicationType.ApplicationeBDR:

                    return typeof(ApplicationeBDRlist);
                case (int)ReplicationType.DRNET:

                    return typeof(DRNETlist);
                case (int)ReplicationType.TPCR:

                    return typeof(TPCRlist);

                case (int)ReplicationType.MSSQLAlwaysOn:

                    return typeof(MSSQLAlwaysOnlist);
                case (int)ReplicationType.RecoverPoint:

                    return typeof(RecoverPointlist);
                case (int)ReplicationType.RecoverPointOracleFULLDB:

                    return typeof(RecoverPointOracleFULLDBlist);

                case (int)ReplicationType.RecoverPointMSSQLFULLDB:

                    return typeof(RecoverPointMSSQLFULLDBlist);
                case (int)ReplicationType.HP3PARMSSQLFULLDB:

                    return typeof(HP3PARMSSQLFULLDBlist);

                case (int)ReplicationType.HP3ParwithApplication:

                    return typeof(HP3ParwithApplicationlist);
                case (int)ReplicationType.HP3ParwithPostgressMSSQL:

                    return typeof(HP3ParwithPostgressMSSQLlist);

                case (int)ReplicationType.EMCSRDFSTARORACLEFULLDB:

                    return typeof(EMCSRDFSTARORACLEFULLDBlist);
                case (int)ReplicationType.EMCSRDFSTARAPPLICATION:

                    return typeof(EMCSRDFSTARAPPLICATIONlist);

                case (int)ReplicationType.EMCSRDF:

                    return typeof(EMCSRDFAPPLICATIONlist);

                case (int)ReplicationType.EMCSRDFMSQLFULLDBSTAR:

                    return typeof(EMCSRDFMSQLFULLDBSTARlist);

                case (int)ReplicationType.HP3ParwithESXI:

                    return typeof(HP3ParwithESXIlist);

                case (int)ReplicationType.ZFSOracleFULLDB:

                    return typeof(ZFSOracleFULLDBlist);

                case (int)ReplicationType.ZFSApplication:

                    return typeof(ZFSApplicationlist);

                case (int)ReplicationType.ZFSWithDB2:

                    return typeof(ZFSWithDB2list);
                case (int)ReplicationType.ZFSMaxFULLDB:

                    return typeof(ZFSMaxFULLDBlist);
                case (int)ReplicationType.EMCISilon:

                    return typeof(EMCISilonlist);
                case (int)ReplicationType.EmcMirrorViewOracleFullDB:

                    return typeof(EmcMirrorViewOracleFullDBlist);

                case (int)ReplicationType.EmcMirrorViewSybaseFullDB:

                    return typeof(EmcMirrorViewSybaseFullDBlist);

                case (int)ReplicationType.EmcMirrorViewApp:

                    return typeof(EmcMirrorViewApplist);

                case (int)ReplicationType.RoboCopy:

                    return typeof(RoboCopylist);

                case (int)ReplicationType.DB2FullDBSVC:

                    return typeof(DB2FullDBSVClist);
                case (int)ReplicationType.PostgressFullDBSVC:

                    return typeof(PostgressFullDBSVClist);
                case (int)ReplicationType.HP3ParwithMongoFullDB:

                    return typeof(HP3ParwithMongoFullDBlist);
                case (int)ReplicationType.EMCSRDFDB2FullDB:

                    return typeof(EMCSRDFDB2FullDBlist);

                case (int)ReplicationType.DB2FullDBEMCRecoveryPoint:

                    return typeof(DB2FullDBEMCRecoveryPointlist);

                case (int)ReplicationType.EMCSTARDB2FullDB:

                    return typeof(EMCSTARDB2FullDBlist);

                case (int)ReplicationType.EMCSTARMYSQLFullDB:

                    return typeof(EMCSTARMYSQLFullDBlist);
                case (int)ReplicationType.HitachiUrDB2FullDB:

                    return typeof(HitachiUrDB2FullDBlist);
                case (int)ReplicationType.HitachiUrMySqlFullDB:

                    return typeof(HitachiUrMySqlFullDBlist);
                case (int)ReplicationType.SAPHANADBReplication:

                    return typeof(SAPHANADBReplicationlist);

                case (int)ReplicationType.VirtualeBDR:

                    return typeof(VirtualeBDRlist);
                case (int)ReplicationType.GoldenGateRepli:

                    return typeof(GoldenGateReplilist);
                case (int)ReplicationType.RSync:

                    return typeof(RSynclist);

                case (int)ReplicationType.HP3PARORACLEFULLDB:

                    return typeof(HP3PARORACLEFULLDBlist);

                case (int)ReplicationType.ApplicationWithNoReplication:

                    return typeof(AppliNoRepli);
                case (int)ReplicationType.DataSync:

                    return typeof(DataSync);

                case (int)ReplicationType.Postgres10:

                    return typeof(Postgre9XJob);

                case (int)ReplicationType.RecoveryAzureSite:

                    return typeof(AzureSite);
            }
            return null;
        }


        public static string GetDRDBNamesByServerId(int InfraId)
        {
            var infrainfo = NewFacade.GetInfraObjectById(InfraId);
            if (infrainfo != null)
            {

                if (infrainfo.RecoveryType != (int)ReplicationType.VeeamReplication)
                {
                    IList<DatabaseBase> drdatabase = NewFacade.GetDatabaseBasesByServerId(infrainfo.DRServerId);
                    var exchangeDAG = NewFacade.GetInfraObjectById(InfraId);
                    string DBSID = null;
                    if (drdatabase != null)
                    {
                        if (drdatabase != null)
                        {
                            //if (drdatabase[0].Type.ToString() == "DRDatabase")
                            //{
                            if (drdatabase[0].DatabaseType.ToString() == "Oracle")
                            {
                                DatabaseOracle oracledb = NewFacade.GetDatabaseOracleByDatabaseBaseId(drdatabase[0].Id);
                                DBSID = oracledb != null ? oracledb.OracleSID : "NA";
                            }
                            else if (drdatabase[0].DatabaseType.ToString() == "OracleRac")
                            {
                                IList<DatabaseNodes> dbnode = NewFacade.GetAllDataBaseNodesByDatabaseId(drdatabase[0].Id);
                                Nodes nod = NewFacade.GetNodesById(dbnode[0].NodeId);
                                DBSID = nod != null ? nod.OracleSID : "NA";
                            }
                            else if (drdatabase[0].DatabaseType.ToString() == "Sql")
                            {
                                DatabaseSql sql = NewFacade.GetDatabaseSqlByDatabaseBaseId(drdatabase[0].Id);
                                if (sql != null)
                                    DBSID = sql.DatabaseSID;
                                else
                                    DBSID = "NA";
                            }
                            else if (drdatabase[0].DatabaseType.ToString() == "Exchange")
                            {
                                DatabaseExchange exchange = NewFacade.GetDatabaseExchangeByDatabaseBaseId(drdatabase[0].Id);
                                DBSID = exchange != null ? "NA" : "NA";
                            }
                            else if (drdatabase[0].DatabaseType.ToString() == "DB2")
                            {
                                DatabaseDB2 db2 = NewFacade.GetDatabaseDb2ByDatabaseBaseId(drdatabase[0].Id);
                                DBSID = db2 != null ? db2.DatabaseSID : "NA";
                            }
                            else if (drdatabase[0].DatabaseType.ToString() == "SQLNative2008")
                            {
                                DatabaseSqlNative2008 Sqldb = NewFacade.GetDatabaseMSSqlByDatabaseBaseId(drdatabase[0].Id);
                                DBSID = Sqldb != null ? Sqldb.DatabaseName : "NA";
                                //DBSID ="NA";
                            }
                            else if (drdatabase[0].DatabaseType.ToString() == "SyBase")
                            {
                                DataBaseSyBase SyBase = NewFacade.GetDatabaseSybaseByDatabaseBaseId(drdatabase[0].Id);
                                DBSID = SyBase != null ? SyBase.DatabaseSID : "NA";
                            }
                            else if (drdatabase[0].DatabaseType.ToString() == "Postgres9x")
                            {
                                DatabasePostgre9x postgredb = NewFacade.GetDatabasePostgre9xByDatabaseBaseId(drdatabase[0].Id);
                                DBSID = postgredb != null ? postgredb.DatabaseName : "NA";
                            }
                            else if (drdatabase[0].DatabaseType.ToString() == "PostgreSQL")
                            {
                                PostgreSql postgredb = NewFacade.GetDatabasePostgreSqlByDatabaseBaseId(drdatabase[0].Id);
                                DBSID = postgredb != null ? postgredb.DatabaseName : "NA";
                            }
                            else if (drdatabase[0].DatabaseType.ToString() == "MySQL")
                            {
                                DatabaseBase MySqlDB = NewFacade.GetDatabaseBaseById(drdatabase[0].Id);
                                DBSID = MySqlDB != null ? MySqlDB.Name : "NA";
                            }
                            else if (drdatabase[0].DatabaseType.ToString() == "SyBaseWithSrs")
                            {
                                DatabaseSybaseWithSrs maxdb = NewFacade.GetDatabaseSybaseWithSrsByDatabaseBaseId(drdatabase[0].Id);
                                DBSID = maxdb != null ? maxdb.DatabaseSID : "NA";
                            }
                            else if (drdatabase[0].DatabaseType.ToString() == "MaxDB")
                            {
                                DatabaseMaxDB maxdb = NewFacade.GetDatabaseMaxDBByDBBaseId(drdatabase[0].Id);
                                DBSID = maxdb != null ? maxdb.DatabaseSID : "NA";
                            }
                            // }
                            else
                                DBSID = "NA";
                        }
                    }
                    else if (exchangeDAG != null)
                    {
                        if (exchangeDAG.RecoveryType == 20)
                        {
                            IList<DataBaseExchangeDAG> exc = NewFacade.GetDatabaseExchangeDAGByServerId(exchangeDAG.DRServerId);
                            DBSID = exc != null ? exc[0].MailBoxDBName : "NA";
                        }
                    }
                    else
                    {
                        return "NA";
                    }
                    if (DBSID != null)
                        return DBSID;
                    else
                        return "NA";

                }
                else
                {

                    return "NA";
                }
            }
            else
            {

                return "NA";
            }
        }

        public static string GetHostNameBysServerId(int InfraId)
        {
            var infrainfo = NewFacade.GetInfraObjectById(InfraId);

            if (infrainfo != null)
            {
                if (infrainfo.RecoveryType != (int)ReplicationType.VeeamReplication)
                {
                    Server svr = NewFacade.GetServerById(Convert.ToInt32(infrainfo.DRServerId));

                    if (svr != null)
                    {
                        return (svr != null ? svr.Name : "NA");
                    }
                    else
                    {
                        return "NA";
                    }

                }
                else
                {

                    return "NA";
                }

            }
            else
            {

                return "NA";
            }

        }

        public static void PopulateAuthenticationType(ListControl lstType)
        {

            lstType.Items.Clear();
            lstType.Items.Add(new ListItem("-Select Authentication Type-", "-Select Authentication Type-"));
            lstType.Items.Add(new ListItem("Local Authentication", "FormAuthentication"));
            lstType.Items.Add(new ListItem("Active Directory", "AD"));


        }

        public static string HandleDate(DateTime dte)
        {
            try
            {
                if (dte != null)
                {
                    string date = dte.ToString();
                    if (date == "1/1/0001 12:00:00 AM")
                        return "NA";

                    return date;
                }
                return "NA";
            }
            catch (Exception ex)
            {
                return "NA";
            }
        }

        public static int heightandwidth(int height, int width)
        {
            int datapagerheight = 0;

            try
            {
                if (height > 0)
                {
                    if (height > 700 && height < 800)
                    {

                        datapagerheight = 13;
                    }
                    else if (height > 800 && height < 970)
                    {

                        datapagerheight = 16;
                    }
                    else if (height > 970 && height < 1100)
                    {

                        datapagerheight = 15;
                    }
                    else if (height > 1100 && height <= 1200)
                    {

                        datapagerheight = 16;
                    }
                    else
                    {
                        datapagerheight = 10;
                    }
                }
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            return datapagerheight;
        }

        #region WebReq Encrp/Decry

        public static string EncryptStringAES(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
                return plainText;

            var keybytes = Encoding.UTF8.GetBytes("8080808080808080");
            var iv = Encoding.UTF8.GetBytes("8080808080808080");

            var encryoFromJavascript = EncryptStringToBytes(plainText, keybytes, iv);
            return Convert.ToBase64String(encryoFromJavascript);
        }
        private static byte[] EncryptStringToBytes(string plainText, byte[] key, byte[] iv)
        {
            
            byte[] encrypted;
            using (var rijAlg = new RijndaelManaged())
            {
                rijAlg.Mode = CipherMode.CBC;
                rijAlg.Padding = PaddingMode.PKCS7;
                rijAlg.FeedbackSize = 128;

                rijAlg.Key = key;
                rijAlg.IV = iv;

                var encryptor = rijAlg.CreateEncryptor(rijAlg.Key, rijAlg.IV);

                using (var msEncrypt = new MemoryStream())
                {
                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (var swEncrypt = new StreamWriter(csEncrypt))
                        {
                            //Write all data to the stream.  
                            swEncrypt.Write(plainText);
                        }
                        encrypted = msEncrypt.ToArray();
                    }
                }
            }
            return encrypted;
        }

        //public static string DecryptStringAES(string cipherText)
        //{
        //    var keybytes = Encoding.UTF8.GetBytes("8080808080808080");
        //    var iv = Encoding.UTF8.GetBytes("8080808080808080");

        //    var encrypted = Convert.FromBase64String(cipherText);
        //    var decriptedFromJavascript = DecryptStringFromBytes(encrypted, keybytes, iv);
        //    return string.Format(decriptedFromJavascript);
        //}
        //private static string DecryptStringFromBytes(byte[] cipherText, byte[] key, byte[] iv)
        //{
        //    // Check arguments.  
        //    if (cipherText == null || cipherText.Length <= 0)
        //    {
        //        throw new ArgumentNullException("cipherText");
        //    }
        //    if (key == null || key.Length <= 0)
        //    {
        //        throw new ArgumentNullException("key");
        //    }
        //    if (iv == null || iv.Length <= 0)
        //    {
        //        throw new ArgumentNullException("key");
        //    }

        //    // Declare the string used to hold  
        //    // the decrypted text.  
        //    string plaintext = null;

        //    // Create an RijndaelManaged object  
        //    // with the specified key and IV.  
        //    using (var rijAlg = new RijndaelManaged())
        //    {
        //        //Settings  
        //        rijAlg.Mode = CipherMode.CBC;
        //        rijAlg.Padding = PaddingMode.PKCS7;
        //        rijAlg.FeedbackSize = 128;

        //        rijAlg.Key = key;
        //        rijAlg.IV = iv;

        //        // Create a decrytor to perform the stream transform.  
        //        var decryptor = rijAlg.CreateDecryptor(rijAlg.Key, rijAlg.IV);

        //        try
        //        {
        //            // Create the streams used for decryption.  
        //            using (var msDecrypt = new MemoryStream(cipherText))
        //            {
        //                using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
        //                {

        //                    using (var srDecrypt = new StreamReader(csDecrypt))
        //                    {
        //                        // Read the decrypted bytes from the decrypting stream  
        //                        // and place them in a string.  
        //                        plaintext = srDecrypt.ReadToEnd();

        //                    }

        //                }
        //            }
        //        }
        //        catch
        //        {
        //            plaintext = "keyError";
        //        }
        //    }

        //    return plaintext;
        //}

        #endregion WebReq Encrp/Decry

        public static string Convertbool(string value1)
        {
            string value2 = string.Empty;

            if (value1 == "1")
            {
                value2 = "Yes";
            }
            else
            {
                value2 = "No";
            }
            return value2;
        }

        public static string Convertboolvalue(string value1)
        {
            string value2 = string.Empty;

            if (value1 == "1")
            {
                value2 = "Is_Clustered[Yes]";
            }
            else if (value1 == "0")
            {
                value2 = "Is_Clustered[No]";
            }
            else
            {
                value2 = "NA";
            }
            return value2;
        }


        public static string GetFormatedUserHostName(string address)
        {
            if (address != null)
            {
                return address;
            }
            else
            {
                return "NA";
            }
        }
    }
}
