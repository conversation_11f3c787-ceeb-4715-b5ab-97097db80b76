﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ZetroSiteReplication", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ZetroSiteReplication : BaseEntity
    {
        #region Member Variables

        public ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public int Id { get; set; }

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string PRVPGName { get; set; }

        [DataMember]
        public string DRVPGName { get; set; }

        [DataMember]
        public string PRVPGSiteType { get; set; }

        [DataMember]
        public string DRVPGSiteType { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }


        #endregion Properties
    }
   
}
