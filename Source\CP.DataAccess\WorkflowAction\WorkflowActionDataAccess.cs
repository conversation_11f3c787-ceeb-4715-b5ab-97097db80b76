﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;

namespace CP.DataAccess
{
    internal sealed class WorkflowActionDataAccess : BaseDataAccess, IWorkflowActionDataAccess
    {
        #region Constructors

        public WorkflowActionDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<WorkflowAction> CreateEntityBuilder<WorkflowAction>()
        {
            return (new WorkflowActionBuilder()) as IEntityBuilder<WorkflowAction>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="WorkflowAction" /> WorkflowAction table.
        /// </summary>
        /// <param name="workflowAction">WorkflowAction</param>
        /// <returns>WorkflowAction</returns>
        /// <author>Kiran Ghadge</author>
        WorkflowAction IWorkflowActionDataAccess.Add(WorkflowAction workflowAction)
        {
            try
            {
                const string sp = "WorkflowAction_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iName", DbType.String, Helper.CryptographyHelper.Md5Encrypt(workflowAction.Name));
                    Database.AddInParameter(cmd, "iDescription", DbType.String, Helper.CryptographyHelper.Md5Encrypt(workflowAction.Description));
                    Database.AddInParameter(cmd, "iType", DbType.Int32, workflowAction.Type);
                    Database.AddInParameter(cmd, "iServerId", DbType.Int32, workflowAction.ServerId);
                    Database.AddInParameter(cmd, "iReplicationId", DbType.Int32, workflowAction.ReplicationId);
                    Database.AddInParameter(cmd, "iActionType", DbType.Int32, workflowAction.ActionType);
                    Database.AddInParameter(cmd, "iGroupId", DbType.Int32, workflowAction.GroupId);
                    Database.AddInParameter(cmd, "iDatabaseId", DbType.Int32, workflowAction.DatabaseId);
                    Database.AddInParameter(cmd, "iExceptPassword", DbType.String, workflowAction.ExceptPassword);
                    Database.AddInParameter(cmd, "iSessionAddRemove", DbType.Int32, workflowAction.SessionAddRemove);
                    Database.AddInParameter(cmd, "isessionName", DbType.String, workflowAction.Session);
                    Database.AddInParameter(cmd, "iLuns", DbType.String, workflowAction.Luns);
                    Database.AddInParameter(cmd, "iMountPoint", DbType.String, workflowAction.MountPoint);
                    Database.AddInParameter(cmd, "iVG", DbType.String, workflowAction.VG);
                    Database.AddInParameter(cmd, "iSwitchOverSession", DbType.String, workflowAction.SwitchOverSession);
                    Database.AddInParameter(cmd, "iCommand", DbType.String, workflowAction.Command);
                    Database.AddInParameter(cmd, "iPrDr", DbType.String, workflowAction.PrDr);
                    Database.AddInParameter(cmd, "iStandByControlFile", DbType.String, workflowAction.StandByControlFile);
                    Database.AddInParameter(cmd, "iSCPFile", DbType.String, workflowAction.File);
                    Database.AddInParameter(cmd, "iControlFile", DbType.String, workflowAction.ControlFile);
                    Database.AddInParameter(cmd, "iTargetSession", DbType.String, workflowAction.TargetServer);
                    Database.AddInParameter(cmd, "iScriptFile", DbType.String, workflowAction.ScriptFile);
                    Database.AddInParameter(cmd, "iTempFile", DbType.String, workflowAction.TempFile);
                    Database.AddInParameter(cmd, "iJobQueue", DbType.String, workflowAction.JobQueue);
                    Database.AddInParameter(cmd, "iFastCopyReplication", DbType.String, workflowAction.Fastcopy);
                    Database.AddInParameter(cmd, "iListner", DbType.String, workflowAction.Listner);
                    Database.AddInParameter(cmd, "iDNSServer", DbType.Int32, workflowAction.DNSServer);
                    Database.AddInParameter(cmd, "iExistingHost", DbType.String, workflowAction.ExistingHost);
                    Database.AddInParameter(cmd, "iExistingIp", DbType.String, workflowAction.ExistingIp);
                    Database.AddInParameter(cmd, "iNewHost", DbType.String, workflowAction.NewHost);
                    Database.AddInParameter(cmd, "iNewIp", DbType.String, workflowAction.NewIp);
                    Database.AddInParameter(cmd, "iDomainName", DbType.String, workflowAction.DomainName);
                    Database.AddInParameter(cmd, "iLocalMountPoints", DbType.String, workflowAction.LocalMountPoints);
                    Database.AddInParameter(cmd, "iRemoteMountPoints", DbType.String, workflowAction.RemoteMountPoints);
                    Database.AddInParameter(cmd, "iHostName", DbType.String, workflowAction.HostName);
                    Database.AddInParameter(cmd, "iIsUseSudo", DbType.Int32, workflowAction.IsUseSudo);
                    Database.AddInParameter(cmd, "iCheckOutput", DbType.String, workflowAction.CheckOutput);
                    Database.AddInParameter(cmd, "iIsReturn", DbType.Int32, workflowAction.IsReturn);
                    Database.AddInParameter(cmd, "iFastCopyPath", DbType.String, workflowAction.FastCopyPath);
                    Database.AddInParameter(cmd, "iSourceFile", DbType.String, workflowAction.SourceFile);
                    Database.AddInParameter(cmd, "iSourceFolder", DbType.String, workflowAction.SourceFolder);
                    Database.AddInParameter(cmd, "iTargetFolder", DbType.String, workflowAction.TargetFolder);
                    Database.AddInParameter(cmd, "iDiskGroup", DbType.String, workflowAction.DiskGroup);
                    Database.AddInParameter(cmd, "iDiskName", DbType.String, workflowAction.DiskName);
                    Database.AddInParameter(cmd, "iRsyncPath", DbType.String, workflowAction.RsyncPath);
                    Database.AddInParameter(cmd, "iTargetFile", DbType.String, workflowAction.TargetFile);
                    Database.AddInParameter(cmd, "iMachineName", DbType.String, workflowAction.MachineName);
                    Database.AddInParameter(cmd, "iSnapShotName", DbType.String, workflowAction.SnapShotName);
                    Database.AddInParameter(cmd, "iTimeOut", DbType.String, workflowAction.TimeOut);
                    Database.AddInParameter(cmd, "iVirtualMachine", DbType.String, workflowAction.VirtualMachine);
                    Database.AddInParameter(cmd, "iDestinationPath", DbType.String, workflowAction.DestinationPath);
                    Database.AddInParameter(cmd, "iTargetMachine", DbType.String, workflowAction.TargetMachine);
                    Database.AddInParameter(cmd, "iVmPath", DbType.String, workflowAction.VmPath);
                    Database.AddInParameter(cmd, "iOriginalText", DbType.String, workflowAction.OriginalText);
                    Database.AddInParameter(cmd, "iNewText", DbType.String, workflowAction.NewText);
                    Database.AddInParameter(cmd, "iRouterConfiguration", DbType.String,
                        workflowAction.RouterConfiguration);
                    Database.AddInParameter(cmd, "iInterfacePassword", DbType.String, workflowAction.InterfacePassword);
                    Database.AddInParameter(cmd, "iDeviceGroup", DbType.String, workflowAction.DeviceGroup);
                    Database.AddInParameter(cmd, "iFileSystem", DbType.String, workflowAction.FileSystem);
                    Database.AddInParameter(cmd, "iWaitTime", DbType.String, workflowAction.WaitTime);
                    Database.AddInParameter(cmd, "iApplicationName", DbType.String, workflowAction.ApplicationName);
                    Database.AddInParameter(cmd, "iMapFile", DbType.String, workflowAction.MapFile);
                    Database.AddInParameter(cmd, "iTask", DbType.String, workflowAction.Task);
                    Database.AddInParameter(cmd, "iFileSystemMountPoint", DbType.String,
                        workflowAction.FileSystemMountPoint);
                    Database.AddInParameter(cmd, "iWorkFlowId", DbType.Int32, workflowAction.WorkFlowId);
                    Database.AddInParameter(cmd, "iWorkFlowActionId", DbType.String, workflowAction.WorkFlowActionId);
                    Database.AddInParameter(cmd, "iDependencyType", DbType.Int32, workflowAction.DependencyType);
                    Database.AddInParameter(cmd, "iWFTime", DbType.String, workflowAction.Time);
                    Database.AddInParameter(cmd, "iTargetDatabase", DbType.Int32, workflowAction.TargetDatabase);
                    Database.AddInParameter(cmd, "iBackUpFile", DbType.String, workflowAction.BackUpFile);
                    Database.AddInParameter(cmd, "iTraceFile", DbType.String, workflowAction.TraceFile);
                    Database.AddInParameter(cmd, "iExpression", DbType.Int32, workflowAction.Expression);
                    Database.AddInParameter(cmd, "iProcessCount", DbType.Int32, workflowAction.ProcessCount);
                    Database.AddInParameter(cmd, "iRTO", DbType.String, workflowAction.RTO);
                    Database.AddInParameter(cmd, "iHDisc", DbType.String, workflowAction.HDisc);
                    Database.AddInParameter(cmd, "iHitachihorcomInstance", DbType.String, workflowAction.HitachihorcomInstance);
                    Database.AddInParameter(cmd, "iRestorePoint", DbType.String, workflowAction.RestorePoint);
                    Database.AddInParameter(cmd, "iAlertText", DbType.String, workflowAction.AlertText);
                    Database.AddInParameter(cmd, "iAlertModeType", DbType.String, workflowAction.AlertModeType);
                    Database.AddInParameter(cmd, "iProcessFlow", DbType.String, workflowAction.ProcessFlow);
                    Database.AddInParameter(cmd, "iVolName", DbType.String, workflowAction.VolName);
                    Database.AddInParameter(cmd, "iDrive", DbType.String, workflowAction.Drive);
                    Database.AddInParameter(cmd, "iSubnetMask", DbType.String, workflowAction.SubnetMask);
                    Database.AddInParameter(cmd, "iGateWay", DbType.String, workflowAction.GateWay);
                    Database.AddInParameter(cmd, "iPrimaryDNS", DbType.String, workflowAction.PrimaryDNS);
                    Database.AddInParameter(cmd, "iSecoundaryDNS", DbType.String, workflowAction.SecoundaryDNS);
                    Database.AddInParameter(cmd, "iServiceName", DbType.String, workflowAction.ServiceName);
                    Database.AddInParameter(cmd, "iappuser", DbType.String, workflowAction.Appuser);
                    Database.AddInParameter(cmd, "iprocessId", DbType.String, workflowAction.ProcessID);
                    Database.AddInParameter(cmd, "ishellprompt", DbType.String, workflowAction.Shellprompt);
                    Database.AddInParameter(cmd, "idscsliserver", DbType.Int32, workflowAction.DSCSLISERVER);
                    Database.AddInParameter(cmd, "ihmcserver", DbType.Int32, workflowAction.hmcserver);
                    Database.AddInParameter(cmd, "iLSSID", DbType.String, workflowAction.LSSID);
                    Database.AddInParameter(cmd, "iRelationshipId", DbType.String, workflowAction.RelationshipId);
                    Database.AddInParameter(cmd, "iCheckState", DbType.String, workflowAction.CheckState);
                    Database.AddInParameter(cmd, "iRecoveryPlan", DbType.String, workflowAction.RecoveryPlan);
                    Database.AddInParameter(cmd, "iClusterName", DbType.String, workflowAction.ClusterName);
                    Database.AddInParameter(cmd, "iClusterGroupResource", DbType.String, workflowAction.ClusterGroupResource);

                    Database.AddInParameter(cmd, "iEmailSuccess", DbType.String, workflowAction.EmailSuccess);
                    Database.AddInParameter(cmd, "iEmailFail", DbType.String, workflowAction.EmailFail);
                    Database.AddInParameter(cmd, "iSmsSuccess", DbType.String, workflowAction.SmsSuccess);
                    Database.AddInParameter(cmd, "iSmsFail", DbType.String, workflowAction.SmsFail);
                    Database.AddInParameter(cmd, "iAlertMechanismType", DbType.String, workflowAction.AlertMechanismType);
                    Database.AddInParameter(cmd, "iScriptBlock", DbType.String, workflowAction.ScriptBlock);
                    Database.AddInParameter(cmd, "iURL", DbType.String, workflowAction.URL);
                    Database.AddInParameter(cmd, "iHTMLContents", DbType.String, workflowAction.HTMLContents);
                    Database.AddInParameter(cmd, "iZoneName", DbType.String, workflowAction.ZoneName);
                    Database.AddInParameter(cmd, "iCellNo", DbType.String, workflowAction.CellNo);
                    Database.AddInParameter(cmd, "iEmailId", DbType.String, workflowAction.EmailId);
                    Database.AddInParameter(cmd, "iResource", DbType.String, workflowAction.Resource);
                    Database.AddInParameter(cmd, "iAlertUsers", DbType.String, workflowAction.AlertUsers);
                    Database.AddInParameter(cmd, "iCreatorId", DbType.Int32, workflowAction.CreatorId);
                    Database.AddInParameter(cmd, "iVMIsClustered", DbType.String, workflowAction.VMIsClustered);
                    Database.AddInParameter(cmd, "iControllerType", DbType.String, workflowAction.ControllerType);
                    Database.AddInParameter(cmd, "iControllerLocation", DbType.String, workflowAction.ControllerLocation);
                    Database.AddInParameter(cmd, "iDefineValue", DbType.String, workflowAction.DefineValue);
                    Database.AddInParameter(cmd, "iSharingOption", DbType.String, workflowAction.SharingOption);
                    Database.AddInParameter(cmd, "iClusterSharedVolumeStatus", DbType.String, workflowAction.ClusterSharedVolumeStatus);

                    Database.AddInParameter(cmd, "iInfraId", DbType.Int32, workflowAction.InfraobjectId);
                    Database.AddInParameter(cmd, "iEbdrStatus", DbType.Int32, workflowAction.EBDRStatusId);
                    Database.AddInParameter(cmd, "iIsWLST", DbType.Int32, workflowAction.IsWLST);
                    Database.AddInParameter(cmd, "iIsLock", DbType.Int32, workflowAction.IsLock);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        workflowAction = reader.Read()
                            ? CreateEntityBuilder<WorkflowAction>().BuildEntity(reader, workflowAction)
                            : null;
                    }
                }
                return workflowAction;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting WorkflowAction Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Update <see cref="WorkflowAction" /> into WorkflowAction table.
        /// </summary>
        /// <param name="workflowAction">WorkflowAction</param>
        /// <returns>WorkflowAction</returns>
        /// <author>Kiran Ghadge</author>
        WorkflowAction IWorkflowActionDataAccess.Update(WorkflowAction workflowAction)
        {
            const string sp = "WorkflowAction_Update";

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iid", DbType.Int32, workflowAction.Id);
                    Database.AddInParameter(cmd, "iName", DbType.String,
                        Helper.CryptographyHelper.Md5Encrypt(workflowAction.Name));
                    Database.AddInParameter(cmd, "iDescription", DbType.String,
                        Helper.CryptographyHelper.Md5Encrypt(workflowAction.Description));
                    Database.AddInParameter(cmd, "iType", DbType.Int32, workflowAction.Type);
                    Database.AddInParameter(cmd, "iServerId", DbType.Int32, workflowAction.ServerId);
                    Database.AddInParameter(cmd, "iReplicationId", DbType.Int32, workflowAction.ReplicationId);
                    Database.AddInParameter(cmd, "iActionType", DbType.Int32, workflowAction.ActionType);
                    Database.AddInParameter(cmd, "iGroupId", DbType.Int32, workflowAction.GroupId);
                    Database.AddInParameter(cmd, "iDatabaseId", DbType.Int32, workflowAction.DatabaseId);
                    Database.AddInParameter(cmd, "iExceptPassword", DbType.String, workflowAction.ExceptPassword);

                    Database.AddInParameter(cmd, "iSessionAddRemove", DbType.Int32, workflowAction.SessionAddRemove);
                    Database.AddInParameter(cmd, "isessionName", DbType.String, workflowAction.Session);
                    Database.AddInParameter(cmd, "iLuns", DbType.String, workflowAction.Luns);
                    Database.AddInParameter(cmd, "iMountPoint", DbType.String, workflowAction.MountPoint);
                    Database.AddInParameter(cmd, "iVG", DbType.String, workflowAction.VG);
                    Database.AddInParameter(cmd, "iSwitchOverSession", DbType.String, workflowAction.SwitchOverSession);
                    Database.AddInParameter(cmd, "iCommand", DbType.String, workflowAction.Command);
                    Database.AddInParameter(cmd, "iPrDr", DbType.Int32, workflowAction.PrDr);

                    Database.AddInParameter(cmd, "iStandByControlFile", DbType.String, workflowAction.StandByControlFile);
                    Database.AddInParameter(cmd, "iSCPFile", DbType.String, workflowAction.File);
                    Database.AddInParameter(cmd, "iControlFile", DbType.String, workflowAction.ControlFile);
                    Database.AddInParameter(cmd, "iTargetSession", DbType.Int32, workflowAction.TargetServer);
                    Database.AddInParameter(cmd, "iScriptFile", DbType.String, workflowAction.ScriptFile);
                    Database.AddInParameter(cmd, "iTempFile", DbType.String, workflowAction.TempFile);
                    Database.AddInParameter(cmd, "iJobQueue", DbType.String, workflowAction.JobQueue);
                    Database.AddInParameter(cmd, "iFastCopyReplication", DbType.String, workflowAction.Fastcopy);
                    Database.AddInParameter(cmd, "iListner", DbType.String, workflowAction.Listner);
                    Database.AddInParameter(cmd, "iDNSServer", DbType.Int32, workflowAction.DNSServer);
                    Database.AddInParameter(cmd, "iExistingHost", DbType.String, workflowAction.ExistingHost);
                    Database.AddInParameter(cmd, "iExistingIp", DbType.String, workflowAction.ExistingIp);
                    Database.AddInParameter(cmd, "iNewHost", DbType.String, workflowAction.NewHost);
                    Database.AddInParameter(cmd, "iNewIp", DbType.String, workflowAction.NewIp);

                    //Database.AddInParameter(cmd, "iPRServer", DbType.Int32, workflowAction.PRServer);
                    //Database.AddInParameter(cmd, "iDRServer", DbType.Int32, workflowAction.DRServer);

                    Database.AddInParameter(cmd, "iDomainName", DbType.String, workflowAction.DomainName);
                    Database.AddInParameter(cmd, "iLocalMountPoints", DbType.String, workflowAction.LocalMountPoints);
                    Database.AddInParameter(cmd, "iRemoteMountPoints", DbType.String, workflowAction.RemoteMountPoints);
                    Database.AddInParameter(cmd, "iHostName", DbType.String, workflowAction.HostName);
                    Database.AddInParameter(cmd, "iIsUseSudo", DbType.Int32, workflowAction.IsUseSudo);
                    Database.AddInParameter(cmd, "iCheckOutput", DbType.String, workflowAction.CheckOutput);
                    Database.AddInParameter(cmd, "iIsReturn", DbType.Int32, workflowAction.IsReturn);

                    //03/07/2012

                    Database.AddInParameter(cmd, "iFastCopyPath", DbType.String, workflowAction.FastCopyPath);
                    Database.AddInParameter(cmd, "iSourceFile", DbType.String, workflowAction.SourceFile);
                    Database.AddInParameter(cmd, "iSourceFolder", DbType.String, workflowAction.SourceFolder);
                    Database.AddInParameter(cmd, "iTargetFolder", DbType.String, workflowAction.TargetFolder);
                    //25/07/2012
                    Database.AddInParameter(cmd, "iDiskGroup", DbType.String, workflowAction.DiskGroup);
                    Database.AddInParameter(cmd, "iDiskName", DbType.String, workflowAction.DiskName);
                    //Database.AddInParameter(cmd, "iPrStorageImageId", DbType.String, workflowAction.PrStorageImageId);
                    // Database.AddInParameter(cmd, "iDrStorageImageId", DbType.Int32, workflowAction.DrStorageImageId);
                    //01/08/2012
                    Database.AddInParameter(cmd, "iRsyncPath", DbType.String, workflowAction.RsyncPath);
                    Database.AddInParameter(cmd, "iTargetFile", DbType.String, workflowAction.TargetFile);
                    Database.AddInParameter(cmd, "iMachineName", DbType.String, workflowAction.MachineName);
                    Database.AddInParameter(cmd, "iSnapShotName", DbType.String, workflowAction.SnapShotName);
                    Database.AddInParameter(cmd, "iTimeOut", DbType.String, workflowAction.TimeOut);
                    Database.AddInParameter(cmd, "iVirtualMachine", DbType.String, workflowAction.VirtualMachine);
                    Database.AddInParameter(cmd, "iDestinationPath", DbType.String, workflowAction.DestinationPath);
                    Database.AddInParameter(cmd, "iTargetMachine", DbType.String, workflowAction.TargetMachine);
                    Database.AddInParameter(cmd, "iVmPath", DbType.String, workflowAction.VmPath);
                    Database.AddInParameter(cmd, "iOriginalText", DbType.String, workflowAction.OriginalText);
                    Database.AddInParameter(cmd, "iNewText", DbType.String, workflowAction.NewText);
                    Database.AddInParameter(cmd, "iRouterConfiguration", DbType.String,
                        workflowAction.RouterConfiguration);
                    Database.AddInParameter(cmd, "iInterfacePassword", DbType.String, workflowAction.InterfacePassword);
                    //Database.AddInParameter(cmd, "iExectionMode", DbType.Int32, workflowAction.ExectionMode);
                    Database.AddInParameter(cmd, "iDeviceGroup", DbType.String, workflowAction.DeviceGroup);
                    Database.AddInParameter(cmd, "iFileSystem", DbType.String, workflowAction.FileSystem);
                    Database.AddInParameter(cmd, "iWaitTime", DbType.String, workflowAction.WaitTime);
                    Database.AddInParameter(cmd, "iApplicationName", DbType.String, workflowAction.ApplicationName);
                    Database.AddInParameter(cmd, "iMapFile", DbType.String, workflowAction.MapFile);
                    Database.AddInParameter(cmd, "iTask", DbType.String, workflowAction.Task);
                    Database.AddInParameter(cmd, "iFileSystemMountPoint", DbType.String,
                        workflowAction.FileSystemMountPoint);
                    Database.AddInParameter(cmd, "iWorkFlowId", DbType.Int32, workflowAction.WorkFlowId);
                    Database.AddInParameter(cmd, "iWorkFlowActionId", DbType.String, workflowAction.WorkFlowActionId);
                    Database.AddInParameter(cmd, "iDependencyType", DbType.Int32, workflowAction.DependencyType);
                    Database.AddInParameter(cmd, "iWFTime", DbType.String, workflowAction.Time);
                    Database.AddInParameter(cmd, "iTargetDatabase", DbType.Int32, workflowAction.TargetDatabase);
                    Database.AddInParameter(cmd, "iBackUpFile", DbType.String, workflowAction.BackUpFile);
                    Database.AddInParameter(cmd, "iTraceFile", DbType.String, workflowAction.TraceFile);
                    Database.AddInParameter(cmd, "iExpression", DbType.Int32, workflowAction.Expression);
                    Database.AddInParameter(cmd, "iProcessCount", DbType.Int32, workflowAction.ProcessCount);
                    Database.AddInParameter(cmd, "iRTO", DbType.String, workflowAction.RTO);
                    Database.AddInParameter(cmd, "iHDisc", DbType.String, workflowAction.HDisc);
                    Database.AddInParameter(cmd, "iHitachihorcomInstance", DbType.String,
                        workflowAction.HitachihorcomInstance);
                    Database.AddInParameter(cmd, "iRestorePoint", DbType.String, workflowAction.RestorePoint);
                    Database.AddInParameter(cmd, "iAlertText", DbType.String, workflowAction.AlertText);
                    Database.AddInParameter(cmd, "iAlertModeType", DbType.String, workflowAction.AlertModeType);
                    Database.AddInParameter(cmd, "iProcessFlow", DbType.String, workflowAction.ProcessFlow);
                    Database.AddInParameter(cmd, "iVolName", DbType.String, workflowAction.VolName);
                    Database.AddInParameter(cmd, "iDrive", DbType.String, workflowAction.Drive);
                    Database.AddInParameter(cmd, "iSubnetMask", DbType.String, workflowAction.SubnetMask);
                    Database.AddInParameter(cmd, "iGateWay", DbType.String, workflowAction.GateWay);
                    Database.AddInParameter(cmd, "iPrimaryDNS", DbType.String, workflowAction.PrimaryDNS);
                    Database.AddInParameter(cmd, "iSecoundaryDNS", DbType.String, workflowAction.SecoundaryDNS);
                    Database.AddInParameter(cmd, "iServiceName", DbType.String, workflowAction.ServiceName);
                    Database.AddInParameter(cmd, "iappuser", DbType.String, workflowAction.Appuser);
                    Database.AddInParameter(cmd, "iprocessId", DbType.String, workflowAction.ProcessID);
                    Database.AddInParameter(cmd, "ishellprompt", DbType.String, workflowAction.Shellprompt);
                    Database.AddInParameter(cmd, "idscsliserver", DbType.Int32, workflowAction.DSCSLISERVER);
                    Database.AddInParameter(cmd, "ihmcserver", DbType.Int32, workflowAction.hmcserver);
                    Database.AddInParameter(cmd, "iLSSID", DbType.String, workflowAction.LSSID);
                    Database.AddInParameter(cmd, "iRelationshipId", DbType.String, workflowAction.RelationshipId);
                    Database.AddInParameter(cmd, "iCheckState", DbType.String, workflowAction.CheckState);

                    Database.AddInParameter(cmd, "iRecoveryPlan", DbType.String, workflowAction.RecoveryPlan);
                    Database.AddInParameter(cmd, "iClusterName", DbType.String, workflowAction.ClusterName);
                    Database.AddInParameter(cmd, "iClusterGroupResource", DbType.String, workflowAction.ClusterGroupResource);

                    Database.AddInParameter(cmd, "iEmailSuccess", DbType.String, workflowAction.EmailSuccess);
                    Database.AddInParameter(cmd, "iEmailFail", DbType.String, workflowAction.EmailFail);
                    Database.AddInParameter(cmd, "iSmsSuccess", DbType.String, workflowAction.SmsSuccess);
                    Database.AddInParameter(cmd, "iSmsFail", DbType.String, workflowAction.SmsFail);
                    Database.AddInParameter(cmd, "iAlertMechanismType", DbType.String, workflowAction.AlertMechanismType);
                    Database.AddInParameter(cmd, "iScriptBlock", DbType.String, workflowAction.ScriptBlock);
                    Database.AddInParameter(cmd, "iURL", DbType.String, workflowAction.URL);
                    Database.AddInParameter(cmd, "iHTMLContents", DbType.String, workflowAction.HTMLContents);
                    Database.AddInParameter(cmd, "iZoneName", DbType.String, workflowAction.ZoneName);
                    Database.AddInParameter(cmd, "iCellNo", DbType.String, workflowAction.CellNo);
                    Database.AddInParameter(cmd, "iEmailId", DbType.String, workflowAction.EmailId);
                    Database.AddInParameter(cmd, "iResource", DbType.String, workflowAction.Resource);
                    Database.AddInParameter(cmd, "iAlertUsers", DbType.String, workflowAction.AlertUsers);
                    Database.AddInParameter(cmd, "iupdatorId", DbType.Int32, workflowAction.UpdatorId);
                    Database.AddInParameter(cmd, "iVMIsClustered", DbType.String, workflowAction.VMIsClustered);
                    Database.AddInParameter(cmd, "iControllerType", DbType.String, workflowAction.ControllerType);
                    Database.AddInParameter(cmd, "iControllerLocation", DbType.String, workflowAction.ControllerLocation);
                    Database.AddInParameter(cmd, "iDefineValue", DbType.String, workflowAction.DefineValue);
                    Database.AddInParameter(cmd, "iSharingOption", DbType.String, workflowAction.SharingOption);
                    Database.AddInParameter(cmd, "iClusterSharedVolumeStatus", DbType.String, workflowAction.ClusterSharedVolumeStatus);


                    Database.AddInParameter(cmd, "iInfraId", DbType.Int32, workflowAction.InfraobjectId);
                    Database.AddInParameter(cmd, "iEbdrStatus", DbType.Int32, workflowAction.EBDRStatusId);
                    Database.AddInParameter(cmd, "iIsWLST", DbType.Int32, workflowAction.IsWLST);

                    Database.AddInParameter(cmd, Dbstring + "iVersion", DbType.Double, workflowAction.Version);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        workflowAction = reader.Read()
                            ? CreateEntityBuilder<WorkflowAction>().BuildEntity(reader, workflowAction)
                            : null;
                    }
                }
                return workflowAction;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating WorkflowAction Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="WorkflowAction" /> From WorkflowAction table by Id.
        /// </summary>
        /// <param name="id">Id of the WorkflowAction</param>
        /// <returns>WorkflowAction</returns>
        /// <author>Kiran Ghadge</author>
        WorkflowAction IWorkflowActionDataAccess.GetById(int id)
        {
            try
            {
                const string sp = "WorkflowAction_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iid", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<WorkflowAction>()).BuildEntity(reader, new WorkflowAction());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
            //return workflowAction;
        }

        /// <summary>
        ///     GetAll <see cref="WorkflowAction" /> From WorkflowAction table.
        /// </summary>
        /// <returns>WorkflowAction</returns>
        /// <author>Kiran Ghadge</author>
        IList<WorkflowAction> IWorkflowActionDataAccess.GetAll()
        {
            try
            {
                const string sp = "WorkflowAction_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<WorkflowAction>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<WorkflowAction> IWorkflowActionDataAccess.GetWorkflowActionsByServerId(int serverId)
        {
            try
            {
                const string sp = "WorkflowActions_GetbyServerId";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, serverId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTIONSCur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<WorkflowAction>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetWorkflowActionsByServerId()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<WorkflowAction> IWorkflowActionDataAccess.GetWorkflowActionsByInfraobjectId(int infraobjectId)
        {
            try
            {
                const string sp = "WORKFLOWACTIONS_GETBYINFRATID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, infraobjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<WorkflowAction>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetWorkflowActionsByServerId()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<WorkflowAction> IWorkflowActionDataAccess.GetWorkflowActionsByDatabaseId(int databaseId)
        {
            try
            {
                const string sp = "WORKFLOWACTION_GETBYDATABASEID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseId", DbType.Int32, databaseId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<WorkflowAction>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetWorkflowActionsByDatabaseId()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Update <see cref="WorkflowAction" /> into WorkflowAction table by Id and exceptionCondition.
        /// </summary>
        /// <param name="id">Id of the WorkflowAction</param>
        /// <param name="exceptionCondition">ExceptionCondition of the WorkflowAction</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IWorkflowActionDataAccess.UpdateException(int id, int exceptionCondition)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "WorkflowAction_UpdateException";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iExceptionCondition", DbType.Int32, exceptionCondition);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting WorkflowAction Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="WorkflowAction" /> From WorkflowAction table by Id.
        /// </summary>
        /// <param name="id">Id of the WorkflowAction</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IWorkflowActionDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "WorkflowAction_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

#if ORACLE
                    if (returnCode == -1)
                        return true;
#endif
                    return returnCode > 0;

                    //  return returnCode < 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting WorkflowAction Entry : " + id + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Check <see cref="WorkflowAction" /> into WorkflowAction table by Name.
        /// </summary>
        /// <param name="name">Name of the WorkflowAction</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IWorkflowActionDataAccess.IsExistByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "WorkflowAction_IsExistByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);

                    Database.ExecuteNonQuery(cmd);

                    //int rowCount = Convert.ToInt32(myscalar);
                    // if (rowCount > 0)
                    //     return true;
                    // else
                    //     return false;

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException("An unexpected error has occurred while deleting this Action.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.IsExistByName (" + name +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        bool IWorkflowActionDataAccess.UpdateUserNameAndPassword(int actionId, string userName, string password)
        {
            try
            {
                if (actionId < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "WFAction_UpdateUserAndPass";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, actionId);
                    Database.AddInParameter(cmd, Dbstring + "iUserName", DbType.AnsiString, userName);
                    Database.AddInParameter(cmd, Dbstring + "iPassowrd", DbType.AnsiString, password);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While updating  WorkflowAction username and password : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        IList<WorkflowAction> IWorkflowActionDataAccess.GetWorkflowactionsByUserId(bool _superadmin, int userid)
        {
            try
            {
                const string sp = "GetWorkflowActionsByuserRole";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iuserRole", DbType.Boolean, _superadmin);
                    Database.AddInParameter(cmd, Dbstring + "iuserId", DbType.Int32, userid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTIONSCur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<WorkflowAction>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetWorkflowActionsByServerId()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<WorkflowAction> IWorkflowActionDataAccess.GetAllActionByWorkFlowName(string workFlowName)
        {
            try
            {
                if (workFlowName == string.Empty)
                {
                    throw new ArgumentNullException("workFlowName");
                }

                const string sp = "GetActionByWorkFlowName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, workFlowName);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<WorkflowAction>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetAllActionByWorkFlowName()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        WorkflowAction IWorkflowActionDataAccess.UpdateisLock(int workflowActionid)
        {
            const string sp = "UpdateWorkflowActionLock";

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iActionID", DbType.Int32, workflowActionid);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<WorkflowAction>()).BuildEntity(reader, new WorkflowAction())
                            : null;
                    }
                }
                //return workflowAction;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating WorkflowAction islock Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        IList<WorkflowAction> IWorkflowActionDataAccess.GetAllNameAndIds()
        {
            try
            {
                const string sp = "WorkflowActionGetAll_NmAndId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return GetActionsNameAndIDS(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetAllNameAndIds" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<WorkflowAction> GetActionsNameAndIDS(IDataReader reader)
        {

            IList<WorkflowAction> workflowActionList = new List<WorkflowAction>();

            while (reader.Read())
            {
                var workflowAction = new WorkflowAction();
                workflowAction.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);

                workflowAction.Name = Convert.IsDBNull(reader["Name"])
                    ? string.Empty
                    : CryptographyHelper.Md5Decrypt(reader["Name"].ToString());
                workflowAction.Type = Convert.IsDBNull(reader["Type"]) ? 0 : Convert.ToInt32((reader["Type"].ToString()));

                workflowActionList.Add(workflowAction);
            }

            //actionValues = actionValues.TrimStart(',');

            return workflowActionList;
        }

        IList<WorkflowAction> IWorkflowActionDataAccess.GetAllWorkflowActionsByType(int TypeId)
        {
            try
            {
                const string sp = "WorkflowAction_GetAllByType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.Int32, TypeId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        IList<WorkflowAction> workflowActionNewList = new List<WorkflowAction>();

                        while (reader.Read())
                        {
                            var workflowAction = new WorkflowAction();
                            workflowAction.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            workflowAction.Name = Convert.IsDBNull(reader["Name"])
               ? string.Empty
               : CryptographyHelper.Md5Decrypt(reader["Name"].ToString());
                            workflowActionNewList.Add(workflowAction);
                        }

                        return workflowActionNewList;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetAllWorkflowActionsByType" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<WorkflowAction> IWorkflowActionDataAccess.GetAllActionName()
        {
            try
            {
                const string sp = "WorkflowAction_GetAllName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //return CreateEntityBuilder<WorkflowAction>().BuildEntities(reader);

                        IList<WorkflowAction> workflowActionNewList = new List<WorkflowAction>();

                        while (reader.Read())
                        {
                            var workflowAction = new WorkflowAction();
                            workflowAction.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            workflowAction.Name = Convert.IsDBNull(reader["Name"])
               ? string.Empty
               : CryptographyHelper.Md5Decrypt(reader["Name"].ToString());
                            workflowActionNewList.Add(workflowAction);
                        }

                        return workflowActionNewList;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.Get All Action Name" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


      

      

        #region WorkflowActionHistory

        bool IWorkflowActionDataAccess.AddWorkflowActionHistory(WorkflowAction workflowAction)
        {
            try
            {
                int returnCode = 0;
                const string sp = "WFACTIONHISTORY_CREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iName", DbType.String, Helper.CryptographyHelper.Md5Encrypt(workflowAction.Name));
                    Database.AddInParameter(cmd, "iDescription", DbType.String, Helper.CryptographyHelper.Md5Encrypt(workflowAction.Description));
                    Database.AddInParameter(cmd, "iType", DbType.Int32, workflowAction.Type);
                    Database.AddInParameter(cmd, "iServerId", DbType.Int32, workflowAction.ServerId);
                    Database.AddInParameter(cmd, "iReplicationId", DbType.Int32, workflowAction.ReplicationId);
                    Database.AddInParameter(cmd, "iActionType", DbType.Int32, workflowAction.ActionType);
                    Database.AddInParameter(cmd, "iGroupId", DbType.Int32, workflowAction.GroupId);
                    Database.AddInParameter(cmd, "iDatabaseId", DbType.Int32, workflowAction.DatabaseId);
                    Database.AddInParameter(cmd, "iExceptPassword", DbType.String, workflowAction.ExceptPassword);
                    Database.AddInParameter(cmd, "iSessionAddRemove", DbType.Int32, workflowAction.SessionAddRemove);
                    Database.AddInParameter(cmd, "isessionName", DbType.String, workflowAction.Session);
                    Database.AddInParameter(cmd, "iLuns", DbType.String, workflowAction.Luns);
                    Database.AddInParameter(cmd, "iMountPoint", DbType.String, workflowAction.MountPoint);
                    Database.AddInParameter(cmd, "iVG", DbType.String, workflowAction.VG);
                    Database.AddInParameter(cmd, "iSwitchOverSession", DbType.String, workflowAction.SwitchOverSession);
                    Database.AddInParameter(cmd, "iCommand", DbType.String, workflowAction.Command);
                    Database.AddInParameter(cmd, "iPrDr", DbType.String, workflowAction.PrDr);
                    Database.AddInParameter(cmd, "iStandByControlFile", DbType.String, workflowAction.StandByControlFile);
                    Database.AddInParameter(cmd, "iSCPFile", DbType.String, workflowAction.File);
                    Database.AddInParameter(cmd, "iControlFile", DbType.String, workflowAction.ControlFile);
                    Database.AddInParameter(cmd, "iTargetSession", DbType.String, workflowAction.TargetServer);
                    Database.AddInParameter(cmd, "iScriptFile", DbType.String, workflowAction.ScriptFile);
                    Database.AddInParameter(cmd, "iTempFile", DbType.String, workflowAction.TempFile);
                    Database.AddInParameter(cmd, "iJobQueue", DbType.String, workflowAction.JobQueue);
                    Database.AddInParameter(cmd, "iFastCopyReplication", DbType.String, workflowAction.Fastcopy);
                    Database.AddInParameter(cmd, "iListner", DbType.String, workflowAction.Listner);
                    Database.AddInParameter(cmd, "iDNSServer", DbType.Int32, workflowAction.DNSServer);
                    Database.AddInParameter(cmd, "iExistingHost", DbType.String, workflowAction.ExistingHost);
                    Database.AddInParameter(cmd, "iExistingIp", DbType.String, workflowAction.ExistingIp);
                    Database.AddInParameter(cmd, "iNewHost", DbType.String, workflowAction.NewHost);
                    Database.AddInParameter(cmd, "iNewIp", DbType.String, workflowAction.NewIp);
                    Database.AddInParameter(cmd, "iDomainName", DbType.String, workflowAction.DomainName);
                    Database.AddInParameter(cmd, "iLocalMountPoints", DbType.String, workflowAction.LocalMountPoints);
                    Database.AddInParameter(cmd, "iRemoteMountPoints", DbType.String, workflowAction.RemoteMountPoints);
                    Database.AddInParameter(cmd, "iHostName", DbType.String, workflowAction.HostName);
                    Database.AddInParameter(cmd, "iIsUseSudo", DbType.Int32, workflowAction.IsUseSudo);
                    Database.AddInParameter(cmd, "iCheckOutput", DbType.String, workflowAction.CheckOutput);
                    Database.AddInParameter(cmd, "iIsReturn", DbType.Int32, workflowAction.IsReturn);
                    Database.AddInParameter(cmd, "iFastCopyPath", DbType.String, workflowAction.FastCopyPath);
                    Database.AddInParameter(cmd, "iSourceFile", DbType.String, workflowAction.SourceFile);
                    Database.AddInParameter(cmd, "iSourceFolder", DbType.String, workflowAction.SourceFolder);
                    Database.AddInParameter(cmd, "iTargetFolder", DbType.String, workflowAction.TargetFolder);
                    Database.AddInParameter(cmd, "iDiskGroup", DbType.String, workflowAction.DiskGroup);
                    Database.AddInParameter(cmd, "iDiskName", DbType.String, workflowAction.DiskName);
                    Database.AddInParameter(cmd, "iRsyncPath", DbType.String, workflowAction.RsyncPath);
                    Database.AddInParameter(cmd, "iTargetFile", DbType.String, workflowAction.TargetFile);
                    Database.AddInParameter(cmd, "iMachineName", DbType.String, workflowAction.MachineName);
                    Database.AddInParameter(cmd, "iSnapShotName", DbType.String, workflowAction.SnapShotName);
                    Database.AddInParameter(cmd, "iTimeOut", DbType.String, workflowAction.TimeOut);
                    Database.AddInParameter(cmd, "iVirtualMachine", DbType.String, workflowAction.VirtualMachine);
                    Database.AddInParameter(cmd, "iDestinationPath", DbType.String, workflowAction.DestinationPath);
                    Database.AddInParameter(cmd, "iTargetMachine", DbType.String, workflowAction.TargetMachine);
                    Database.AddInParameter(cmd, "iVmPath", DbType.String, workflowAction.VmPath);
                    Database.AddInParameter(cmd, "iOriginalText", DbType.String, workflowAction.OriginalText);
                    Database.AddInParameter(cmd, "iNewText", DbType.String, workflowAction.NewText);
                    Database.AddInParameter(cmd, "iRouterConfiguration", DbType.String,
                        workflowAction.RouterConfiguration);
                    Database.AddInParameter(cmd, "iInterfacePassword", DbType.String, workflowAction.InterfacePassword);
                    Database.AddInParameter(cmd, "iDeviceGroup", DbType.String, workflowAction.DeviceGroup);
                    Database.AddInParameter(cmd, "iFileSystem", DbType.String, workflowAction.FileSystem);
                    Database.AddInParameter(cmd, "iWaitTime", DbType.String, workflowAction.WaitTime);
                    Database.AddInParameter(cmd, "iApplicationName", DbType.String, workflowAction.ApplicationName);
                    Database.AddInParameter(cmd, "iMapFile", DbType.String, workflowAction.MapFile);
                    Database.AddInParameter(cmd, "iTask", DbType.String, workflowAction.Task);
                    Database.AddInParameter(cmd, "iFileSystemMountPoint", DbType.String,
                        workflowAction.FileSystemMountPoint);
                    Database.AddInParameter(cmd, "iWorkFlowId", DbType.Int32, workflowAction.WorkFlowId);
                    Database.AddInParameter(cmd, "iWorkFlowActionId", DbType.String, workflowAction.WorkFlowActionId);
                    Database.AddInParameter(cmd, "iDependencyType", DbType.Int32, workflowAction.DependencyType);
                    Database.AddInParameter(cmd, "iWFTime", DbType.String, workflowAction.Time);
                    Database.AddInParameter(cmd, "iTargetDatabase", DbType.Int32, workflowAction.TargetDatabase);
                    Database.AddInParameter(cmd, "iBackUpFile", DbType.String, workflowAction.BackUpFile);
                    Database.AddInParameter(cmd, "iTraceFile", DbType.String, workflowAction.TraceFile);
                    Database.AddInParameter(cmd, "iExpression", DbType.Int32, workflowAction.Expression);
                    Database.AddInParameter(cmd, "iProcessCount", DbType.Int32, workflowAction.ProcessCount);
                    Database.AddInParameter(cmd, "iRTO", DbType.String, workflowAction.RTO);
                    Database.AddInParameter(cmd, "iHDisc", DbType.String, workflowAction.HDisc);
                    Database.AddInParameter(cmd, "iHitachihorcomInstance", DbType.String, workflowAction.HitachihorcomInstance);
                    Database.AddInParameter(cmd, "iRestorePoint", DbType.String, workflowAction.RestorePoint);
                    Database.AddInParameter(cmd, "iAlertText", DbType.String, workflowAction.AlertText);
                    Database.AddInParameter(cmd, "iAlertModeType", DbType.String, workflowAction.AlertModeType);
                    Database.AddInParameter(cmd, "iProcessFlow", DbType.String, workflowAction.ProcessFlow);
                    Database.AddInParameter(cmd, "iVolName", DbType.String, workflowAction.VolName);
                    Database.AddInParameter(cmd, "iDrive", DbType.String, workflowAction.Drive);
                    Database.AddInParameter(cmd, "iSubnetMask", DbType.String, workflowAction.SubnetMask);
                    Database.AddInParameter(cmd, "iGateWay", DbType.String, workflowAction.GateWay);
                    Database.AddInParameter(cmd, "iPrimaryDNS", DbType.String, workflowAction.PrimaryDNS);
                    Database.AddInParameter(cmd, "iSecoundaryDNS", DbType.String, workflowAction.SecoundaryDNS);
                    Database.AddInParameter(cmd, "iServiceName", DbType.String, workflowAction.ServiceName);
                    Database.AddInParameter(cmd, "iappuser", DbType.String, workflowAction.Appuser);
                    Database.AddInParameter(cmd, "iprocessId", DbType.String, workflowAction.ProcessID);
                    Database.AddInParameter(cmd, "ishellprompt", DbType.String, workflowAction.Shellprompt);
                    Database.AddInParameter(cmd, "idscsliserver", DbType.Int32, workflowAction.DSCSLISERVER);
                    Database.AddInParameter(cmd, "ihmcserver", DbType.Int32, workflowAction.hmcserver);
                    Database.AddInParameter(cmd, "iLSSID", DbType.String, workflowAction.LSSID);
                    Database.AddInParameter(cmd, "iRelationshipId", DbType.String, workflowAction.RelationshipId);
                    Database.AddInParameter(cmd, "iCheckState", DbType.String, workflowAction.CheckState);
                    Database.AddInParameter(cmd, "iRecoveryPlan", DbType.String, workflowAction.RecoveryPlan);
                    Database.AddInParameter(cmd, "iClusterName", DbType.String, workflowAction.ClusterName);
                    Database.AddInParameter(cmd, "iClusterGroupResource", DbType.String, workflowAction.ClusterGroupResource);

                    Database.AddInParameter(cmd, "iEmailSuccess", DbType.String, workflowAction.EmailSuccess);
                    Database.AddInParameter(cmd, "iEmailFail", DbType.String, workflowAction.EmailFail);
                    Database.AddInParameter(cmd, "iSmsSuccess", DbType.String, workflowAction.SmsSuccess);
                    Database.AddInParameter(cmd, "iSmsFail", DbType.String, workflowAction.SmsFail);
                    Database.AddInParameter(cmd, "iAlertMechanismType", DbType.String, workflowAction.AlertMechanismType);
                    Database.AddInParameter(cmd, "iScriptBlock", DbType.String, workflowAction.ScriptBlock);
                    Database.AddInParameter(cmd, "iURL", DbType.String, workflowAction.URL);
                    Database.AddInParameter(cmd, "iHTMLContents", DbType.String, workflowAction.HTMLContents);
                    Database.AddInParameter(cmd, "iZoneName", DbType.String, workflowAction.ZoneName);
                    Database.AddInParameter(cmd, "iCellNo", DbType.String, workflowAction.CellNo);
                    Database.AddInParameter(cmd, "iEmailId", DbType.String, workflowAction.EmailId);
                    Database.AddInParameter(cmd, "iResource", DbType.String, workflowAction.Resource);
                    Database.AddInParameter(cmd, "iAlertUsers", DbType.String, workflowAction.AlertUsers);
                    Database.AddInParameter(cmd, "iCreatorId", DbType.Int32, workflowAction.CreatorId);
                    Database.AddInParameter(cmd, "iVMIsClustered", DbType.String, workflowAction.VMIsClustered);
                    Database.AddInParameter(cmd, "iControllerType", DbType.String, workflowAction.ControllerType);
                    Database.AddInParameter(cmd, "iControllerLocation", DbType.String, workflowAction.ControllerLocation);
                    Database.AddInParameter(cmd, "iDefineValue", DbType.String, workflowAction.DefineValue);
                    Database.AddInParameter(cmd, "iSharingOption", DbType.String, workflowAction.SharingOption);
                    Database.AddInParameter(cmd, "iClusterSharedVolumeStatus", DbType.String, workflowAction.ClusterSharedVolumeStatus);
                    Database.AddInParameter(cmd, "iInfraId", DbType.Int32, workflowAction.InfraobjectId);
                    Database.AddInParameter(cmd, "iEbdrStatus", DbType.Int32, workflowAction.EBDRStatusId);
                    Database.AddInParameter(cmd, "iIsWLST", DbType.Int32, workflowAction.IsWLST);

                    //History
                    Database.AddInParameter(cmd, "iVersion", DbType.Double, workflowAction.Version);
                    Database.AddInParameter(cmd, "iUser", DbType.String, workflowAction.User);
                    Database.AddInParameter(cmd, "iReason", DbType.String, workflowAction.Reason);
                    Database.AddInParameter(cmd, "iWFActionId", DbType.Int32, workflowAction.WFActionId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return true;
                        }
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting AddWorkflowActionHistory Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        WorkflowAction IWorkflowActionDataAccess.GetWorkflowHistoryByVersion(int actionid, double version)
        {
            try
            {
                const string sp = "GETACTIONHISTORYBYVERSION";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iActionid", DbType.Int32, actionid);
                    Database.AddInParameter(cmd, "iversion", DbType.Double, version);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<WorkflowAction>()).BuildEntity(reader, new WorkflowAction());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetWorkflowHistoryByVersion(" + actionid + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
            //return workflowAction;
        }

        bool IWorkflowActionDataAccess.AddWorkflowRestoreAction(WorkflowAction workflowAction)
        {
            try
            {
                int returnCode = 0;
                const string sp = "WORKFLOWRESTOREACTION_CREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iName", DbType.String, Helper.CryptographyHelper.Md5Encrypt(workflowAction.Name));
                    Database.AddInParameter(cmd, "iDescription", DbType.String, Helper.CryptographyHelper.Md5Encrypt(workflowAction.Description));
                    Database.AddInParameter(cmd, "iType", DbType.Int32, workflowAction.Type);
                    Database.AddInParameter(cmd, "iServerId", DbType.Int32, workflowAction.ServerId);
                    Database.AddInParameter(cmd, "iReplicationId", DbType.Int32, workflowAction.ReplicationId);
                    Database.AddInParameter(cmd, "iActionType", DbType.Int32, workflowAction.ActionType);
                    Database.AddInParameter(cmd, "iGroupId", DbType.Int32, workflowAction.GroupId);
                    Database.AddInParameter(cmd, "iDatabaseId", DbType.Int32, workflowAction.DatabaseId);
                    Database.AddInParameter(cmd, "iExceptPassword", DbType.String, workflowAction.ExceptPassword);
                    Database.AddInParameter(cmd, "iSessionAddRemove", DbType.Int32, workflowAction.SessionAddRemove);
                    Database.AddInParameter(cmd, "isessionName", DbType.String, workflowAction.Session);
                    Database.AddInParameter(cmd, "iLuns", DbType.String, workflowAction.Luns);
                    Database.AddInParameter(cmd, "iMountPoint", DbType.String, workflowAction.MountPoint);
                    Database.AddInParameter(cmd, "iVG", DbType.String, workflowAction.VG);
                    Database.AddInParameter(cmd, "iSwitchOverSession", DbType.String, workflowAction.SwitchOverSession);
                    Database.AddInParameter(cmd, "iCommand", DbType.String, workflowAction.Command);
                    Database.AddInParameter(cmd, "iPrDr", DbType.String, workflowAction.PrDr);
                    Database.AddInParameter(cmd, "iStandByControlFile", DbType.String, workflowAction.StandByControlFile);
                    Database.AddInParameter(cmd, "iSCPFile", DbType.String, workflowAction.File);
                    Database.AddInParameter(cmd, "iControlFile", DbType.String, workflowAction.ControlFile);
                    Database.AddInParameter(cmd, "iTargetSession", DbType.String, workflowAction.TargetServer);
                    Database.AddInParameter(cmd, "iScriptFile", DbType.String, workflowAction.ScriptFile);
                    Database.AddInParameter(cmd, "iTempFile", DbType.String, workflowAction.TempFile);
                    Database.AddInParameter(cmd, "iJobQueue", DbType.String, workflowAction.JobQueue);
                    Database.AddInParameter(cmd, "iFastCopyReplication", DbType.String, workflowAction.Fastcopy);
                    Database.AddInParameter(cmd, "iListner", DbType.String, workflowAction.Listner);
                    Database.AddInParameter(cmd, "iDNSServer", DbType.Int32, workflowAction.DNSServer);
                    Database.AddInParameter(cmd, "iExistingHost", DbType.String, workflowAction.ExistingHost);
                    Database.AddInParameter(cmd, "iExistingIp", DbType.String, workflowAction.ExistingIp);
                    Database.AddInParameter(cmd, "iNewHost", DbType.String, workflowAction.NewHost);
                    Database.AddInParameter(cmd, "iNewIp", DbType.String, workflowAction.NewIp);
                    Database.AddInParameter(cmd, "iDomainName", DbType.String, workflowAction.DomainName);
                    Database.AddInParameter(cmd, "iLocalMountPoints", DbType.String, workflowAction.LocalMountPoints);
                    Database.AddInParameter(cmd, "iRemoteMountPoints", DbType.String, workflowAction.RemoteMountPoints);
                    Database.AddInParameter(cmd, "iHostName", DbType.String, workflowAction.HostName);
                    Database.AddInParameter(cmd, "iIsUseSudo", DbType.Int32, workflowAction.IsUseSudo);
                    Database.AddInParameter(cmd, "iCheckOutput", DbType.String, workflowAction.CheckOutput);
                    Database.AddInParameter(cmd, "iIsReturn", DbType.Int32, workflowAction.IsReturn);
                    Database.AddInParameter(cmd, "iFastCopyPath", DbType.String, workflowAction.FastCopyPath);
                    Database.AddInParameter(cmd, "iSourceFile", DbType.String, workflowAction.SourceFile);
                    Database.AddInParameter(cmd, "iSourceFolder", DbType.String, workflowAction.SourceFolder);
                    Database.AddInParameter(cmd, "iTargetFolder", DbType.String, workflowAction.TargetFolder);
                    Database.AddInParameter(cmd, "iDiskGroup", DbType.String, workflowAction.DiskGroup);
                    Database.AddInParameter(cmd, "iDiskName", DbType.String, workflowAction.DiskName);
                    Database.AddInParameter(cmd, "iRsyncPath", DbType.String, workflowAction.RsyncPath);
                    Database.AddInParameter(cmd, "iTargetFile", DbType.String, workflowAction.TargetFile);
                    Database.AddInParameter(cmd, "iMachineName", DbType.String, workflowAction.MachineName);
                    Database.AddInParameter(cmd, "iSnapShotName", DbType.String, workflowAction.SnapShotName);
                    Database.AddInParameter(cmd, "iTimeOut", DbType.String, workflowAction.TimeOut);
                    Database.AddInParameter(cmd, "iVirtualMachine", DbType.String, workflowAction.VirtualMachine);
                    Database.AddInParameter(cmd, "iDestinationPath", DbType.String, workflowAction.DestinationPath);
                    Database.AddInParameter(cmd, "iTargetMachine", DbType.String, workflowAction.TargetMachine);
                    Database.AddInParameter(cmd, "iVmPath", DbType.String, workflowAction.VmPath);
                    Database.AddInParameter(cmd, "iOriginalText", DbType.String, workflowAction.OriginalText);
                    Database.AddInParameter(cmd, "iNewText", DbType.String, workflowAction.NewText);
                    Database.AddInParameter(cmd, "iRouterConfiguration", DbType.String,
                        workflowAction.RouterConfiguration);
                    Database.AddInParameter(cmd, "iInterfacePassword", DbType.String, workflowAction.InterfacePassword);
                    Database.AddInParameter(cmd, "iDeviceGroup", DbType.String, workflowAction.DeviceGroup);
                    Database.AddInParameter(cmd, "iFileSystem", DbType.String, workflowAction.FileSystem);
                    Database.AddInParameter(cmd, "iWaitTime", DbType.String, workflowAction.WaitTime);
                    Database.AddInParameter(cmd, "iApplicationName", DbType.String, workflowAction.ApplicationName);
                    Database.AddInParameter(cmd, "iMapFile", DbType.String, workflowAction.MapFile);
                    Database.AddInParameter(cmd, "iTask", DbType.String, workflowAction.Task);
                    Database.AddInParameter(cmd, "iFileSystemMountPoint", DbType.String,
                        workflowAction.FileSystemMountPoint);
                    Database.AddInParameter(cmd, "iWorkFlowId", DbType.Int32, workflowAction.WorkFlowId);
                    Database.AddInParameter(cmd, "iWorkFlowActionId", DbType.String, workflowAction.WorkFlowActionId);
                    Database.AddInParameter(cmd, "iDependencyType", DbType.Int32, workflowAction.DependencyType);
                    Database.AddInParameter(cmd, "iWFTime", DbType.String, workflowAction.Time);
                    Database.AddInParameter(cmd, "iTargetDatabase", DbType.Int32, workflowAction.TargetDatabase);
                    Database.AddInParameter(cmd, "iBackUpFile", DbType.String, workflowAction.BackUpFile);
                    Database.AddInParameter(cmd, "iTraceFile", DbType.String, workflowAction.TraceFile);
                    Database.AddInParameter(cmd, "iExpression", DbType.Int32, workflowAction.Expression);
                    Database.AddInParameter(cmd, "iProcessCount", DbType.Int32, workflowAction.ProcessCount);
                    Database.AddInParameter(cmd, "iRTO", DbType.String, workflowAction.RTO);
                    Database.AddInParameter(cmd, "iHDisc", DbType.String, workflowAction.HDisc);
                    Database.AddInParameter(cmd, "iHitachihorcomInstance", DbType.String, workflowAction.HitachihorcomInstance);
                    Database.AddInParameter(cmd, "iRestorePoint", DbType.String, workflowAction.RestorePoint);
                    Database.AddInParameter(cmd, "iAlertText", DbType.String, workflowAction.AlertText);
                    Database.AddInParameter(cmd, "iAlertModeType", DbType.String, workflowAction.AlertModeType);
                    Database.AddInParameter(cmd, "iProcessFlow", DbType.String, workflowAction.ProcessFlow);
                    Database.AddInParameter(cmd, "iVolName", DbType.String, workflowAction.VolName);
                    Database.AddInParameter(cmd, "iDrive", DbType.String, workflowAction.Drive);
                    Database.AddInParameter(cmd, "iSubnetMask", DbType.String, workflowAction.SubnetMask);
                    Database.AddInParameter(cmd, "iGateWay", DbType.String, workflowAction.GateWay);
                    Database.AddInParameter(cmd, "iPrimaryDNS", DbType.String, workflowAction.PrimaryDNS);
                    Database.AddInParameter(cmd, "iSecoundaryDNS", DbType.String, workflowAction.SecoundaryDNS);
                    Database.AddInParameter(cmd, "iServiceName", DbType.String, workflowAction.ServiceName);
                    Database.AddInParameter(cmd, "iappuser", DbType.String, workflowAction.Appuser);
                    Database.AddInParameter(cmd, "iprocessId", DbType.String, workflowAction.ProcessID);
                    Database.AddInParameter(cmd, "ishellprompt", DbType.String, workflowAction.Shellprompt);
                    Database.AddInParameter(cmd, "idscsliserver", DbType.Int32, workflowAction.DSCSLISERVER);
                    Database.AddInParameter(cmd, "ihmcserver", DbType.Int32, workflowAction.hmcserver);
                    Database.AddInParameter(cmd, "iLSSID", DbType.String, workflowAction.LSSID);
                    Database.AddInParameter(cmd, "iRelationshipId", DbType.String, workflowAction.RelationshipId);
                    Database.AddInParameter(cmd, "iCheckState", DbType.String, workflowAction.CheckState);
                    Database.AddInParameter(cmd, "iRecoveryPlan", DbType.String, workflowAction.RecoveryPlan);
                    Database.AddInParameter(cmd, "iClusterName", DbType.String, workflowAction.ClusterName);
                    Database.AddInParameter(cmd, "iClusterGroupResource", DbType.String, workflowAction.ClusterGroupResource);

                    Database.AddInParameter(cmd, "iEmailSuccess", DbType.String, workflowAction.EmailSuccess);
                    Database.AddInParameter(cmd, "iEmailFail", DbType.String, workflowAction.EmailFail);
                    Database.AddInParameter(cmd, "iSmsSuccess", DbType.String, workflowAction.SmsSuccess);
                    Database.AddInParameter(cmd, "iSmsFail", DbType.String, workflowAction.SmsFail);
                    Database.AddInParameter(cmd, "iAlertMechanismType", DbType.String, workflowAction.AlertMechanismType);
                    Database.AddInParameter(cmd, "iScriptBlock", DbType.String, workflowAction.ScriptBlock);
                    Database.AddInParameter(cmd, "iURL", DbType.String, workflowAction.URL);
                    Database.AddInParameter(cmd, "iHTMLContents", DbType.String, workflowAction.HTMLContents);
                    Database.AddInParameter(cmd, "iZoneName", DbType.String, workflowAction.ZoneName);
                    Database.AddInParameter(cmd, "iCellNo", DbType.String, workflowAction.CellNo);
                    Database.AddInParameter(cmd, "iEmailId", DbType.String, workflowAction.EmailId);
                    Database.AddInParameter(cmd, "iResource", DbType.String, workflowAction.Resource);
                    Database.AddInParameter(cmd, "iAlertUsers", DbType.String, workflowAction.AlertUsers);
                    Database.AddInParameter(cmd, "iCreatorId", DbType.Int32, workflowAction.CreatorId);
                    Database.AddInParameter(cmd, "iVMIsClustered", DbType.String, workflowAction.VMIsClustered);
                    Database.AddInParameter(cmd, "iControllerType", DbType.String, workflowAction.ControllerType);
                    Database.AddInParameter(cmd, "iControllerLocation", DbType.String, workflowAction.ControllerLocation);
                    Database.AddInParameter(cmd, "iDefineValue", DbType.String, workflowAction.DefineValue);
                    Database.AddInParameter(cmd, "iSharingOption", DbType.String, workflowAction.SharingOption);
                    Database.AddInParameter(cmd, "iClusterSharedVolumeStatus", DbType.String, workflowAction.ClusterSharedVolumeStatus);
                    Database.AddInParameter(cmd, "iInfraId", DbType.Int32, workflowAction.InfraobjectId);
                    Database.AddInParameter(cmd, "iEbdrStatus", DbType.Int32, workflowAction.EBDRStatusId);
                    Database.AddInParameter(cmd, "iIsWLST", DbType.Int32, workflowAction.IsWLST);

                    //History
                    Database.AddInParameter(cmd, "iVersion", DbType.Double, workflowAction.Version);
                    Database.AddInParameter(cmd, "iUser", DbType.String, workflowAction.User);
                    Database.AddInParameter(cmd, "iReason", DbType.String, workflowAction.Reason);
                    Database.AddInParameter(cmd, "iWFActionId", DbType.Int32, workflowAction.WFActionId);

                    //For Restore
                    Database.AddInParameter(cmd, "iWFID", DbType.Int32, workflowAction.WorkFlowId);
                    Database.AddInParameter(cmd, "iWFVERSION", DbType.Double, workflowAction.WorkflowVersion);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return true;
                        }
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting WORKFLOWRESTOREACTION_CREATE Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        IList<WorkflowAction> IWorkflowActionDataAccess.GetAllActionsByWFId(int WorkflowID)
        {
            try
            {
                const string sp = "GETALLACTIONSBYWORKFLOWID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iWFID", DbType.Int32, WorkflowID);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<WorkflowAction>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<WorkflowAction> IWorkflowActionDataAccess.GetWFRestoreActionByIdAndVersion(int WorkflowId, double WorkflowVersion)
        {
            try
            {
                const string sp = "GetWorkflowRestoreActions";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iWorkflowId", DbType.Int32, WorkflowId);
                    Database.AddInParameter(cmd, "iWFVersion", DbType.Double, WorkflowVersion);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BindWorkflowRestoreActions(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetWorkflowHistoryByVersion(" + WorkflowId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
            //return workflowAction;
        }


        private IList<WorkflowAction> BindWorkflowRestoreActions(IDataReader reader)
        {

            IList<WorkflowAction> WorkflowActionList = new List<WorkflowAction>();

            while (reader.Read())
            {
                WorkflowAction workflowAction = new WorkflowAction();

                workflowAction.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);

                //Restore Workflow
                workflowAction.WorkFlowId = Convert.IsDBNull(reader["WFID"]) ? 0 : Convert.ToInt32(reader["WFID"]);
                workflowAction.WorkflowVersion = Convert.IsDBNull(reader["WFVERSION"]) ? 1.0 : Convert.ToDouble(reader["WFVERSION"]);
                workflowAction.WFActionId = Convert.IsDBNull(reader["WFACTIONID"]) ? 0 : Convert.ToInt32(reader["WFACTIONID"]);

                workflowAction.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                workflowAction.Name = Convert.IsDBNull(reader["Name"])
                    ? string.Empty
                    : CryptographyHelper.Md5Decrypt(reader["Name"].ToString());
                workflowAction.Description = Convert.IsDBNull(reader["Description"])
                    ? string.Empty
                    : CryptographyHelper.Md5Decrypt(reader["Description"].ToString());
                workflowAction.Type = Convert.IsDBNull(reader["Type"]) ? 0 : Convert.ToInt32(reader["Type"]);
                workflowAction.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                workflowAction.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                    ? 0
                    : Convert.ToInt32(reader["ReplicationId"]);
                workflowAction.ActionType = Convert.IsDBNull(reader["ActionType"])
                    ? 0
                    : Convert.ToInt32(reader["ActionType"]);
                workflowAction.GroupId = Convert.IsDBNull(reader["GroupId"]) ? 0 : Convert.ToInt32(reader["GroupId"]);
                workflowAction.DatabaseId = Convert.IsDBNull(reader["DatabaseId"])
                    ? 0
                    : Convert.ToInt32(reader["DatabaseId"]);
                workflowAction.ExceptPassword = Convert.IsDBNull(reader["ExceptPassword"])
                    ? string.Empty
                    : Convert.ToString(reader["ExceptPassword"]);
                workflowAction.SessionAddRemove = Convert.IsDBNull(reader["SessionAddRemove"])
                    ? 0
                    : Convert.ToInt32(reader["SessionAddRemove"]);
                workflowAction.Session = Convert.IsDBNull(reader["SessionName"])
                    ? string.Empty
                    : Convert.ToString(reader["SessionName"]);
                workflowAction.Luns = Convert.IsDBNull(reader["Luns"]) ? string.Empty : Convert.ToString(reader["Luns"]);
                workflowAction.MountPoint = Convert.IsDBNull(reader["MountPoint"])
                    ? string.Empty
                    : Convert.ToString(reader["MountPoint"]);
                workflowAction.VG = Convert.IsDBNull(reader["VG"]) ? string.Empty : Convert.ToString(reader["VG"]);
                workflowAction.SwitchOverSession = Convert.IsDBNull(reader["SwitchOverSession"])
                    ? string.Empty
                    : Convert.ToString(reader["SwitchOverSession"]);
                workflowAction.Command = Convert.IsDBNull(reader["Command"])
                    ? string.Empty
                    : Convert.ToString(reader["Command"]);
                workflowAction.PrDr = Convert.IsDBNull(reader["PrDr"]) ? 0 : Convert.ToInt32(reader["PrDr"]);

                workflowAction.StandByControlFile = Convert.IsDBNull(reader["StandByControlFile"])
                    ? string.Empty
                    : Convert.ToString(reader["StandByControlFile"]);
                workflowAction.File = Convert.IsDBNull(reader["SCPFile"])
                    ? string.Empty
                    : Convert.ToString(reader["SCPFile"]);
                workflowAction.ControlFile = Convert.IsDBNull(reader["ControlFile"])
                    ? string.Empty
                    : Convert.ToString(reader["ControlFile"]);
                workflowAction.TargetServer = Convert.IsDBNull(reader["TargetSession"])
                    ? 0
                    : Convert.ToInt32(reader["TargetSession"]);
                workflowAction.ScriptFile = Convert.IsDBNull(reader["ScriptFile"])
                    ? string.Empty
                    : Convert.ToString(reader["ScriptFile"]);

                workflowAction.TempFile = Convert.IsDBNull(reader["TempFile"])
                    ? string.Empty
                    : Convert.ToString(reader["TempFile"]);
                workflowAction.JobQueue = Convert.IsDBNull(reader["JobQueue"])
                    ? string.Empty
                    : Convert.ToString(reader["JobQueue"]);
                workflowAction.Fastcopy = Convert.IsDBNull(reader["FastCopyReplication"])
                    ? 0
                    : Convert.ToInt32(reader["FastCopyReplication"]);
                workflowAction.Listner = Convert.IsDBNull(reader["Listner"])
                    ? string.Empty
                    : Convert.ToString(reader["Listner"]);

                workflowAction.DNSServer = Convert.IsDBNull(reader["DNSServer"]) ? 0 : Convert.ToInt32(reader["DNSServer"]);
                workflowAction.ExistingHost = Convert.IsDBNull(reader["ExistingHost"])
                    ? string.Empty
                    : Convert.ToString(reader["ExistingHost"]);
                workflowAction.ExistingIp = Convert.IsDBNull(reader["ExistingIp"])
                    ? string.Empty
                    : Convert.ToString(reader["ExistingIp"]);
                workflowAction.NewHost = Convert.IsDBNull(reader["NewHost"])
                    ? string.Empty
                    : Convert.ToString(reader["NewHost"]);
                workflowAction.NewIp = Convert.IsDBNull(reader["NewIp"]) ? string.Empty : Convert.ToString(reader["NewIp"]);
                workflowAction.DomainName = Convert.IsDBNull(reader["DomainName"])
                    ? string.Empty
                    : Convert.ToString(reader["DomainName"]);
                ////26062012
                workflowAction.LocalMountPoints = Convert.IsDBNull(reader["LocalMountPoints"])
                    ? string.Empty
                    : Convert.ToString(reader["LocalMountPoints"]);
                workflowAction.RemoteMountPoints = Convert.IsDBNull(reader["RemoteMountPoints"])
                    ? string.Empty
                    : Convert.ToString(reader["RemoteMountPoints"]);
                workflowAction.HostName = Convert.IsDBNull(reader["HostName"])
                    ? string.Empty
                    : Convert.ToString(reader["HostName"]);
                ////02/07/2012
                workflowAction.IsUseSudo = Convert.IsDBNull(reader["IsUseSudo"]) ? 0 : Convert.ToInt32(reader["IsUseSudo"]);
                workflowAction.CheckOutput = Convert.IsDBNull(reader["CheckOutput"])
                    ? string.Empty
                    : Convert.ToString(reader["CheckOutput"]);
                workflowAction.IsReturn = Convert.IsDBNull(reader["IsReturn"]) ? 0 : Convert.ToInt32(reader["IsReturn"]);
                ////03/07/2012
                workflowAction.FastCopyPath = Convert.IsDBNull(reader["FastCopyPath"])
                    ? string.Empty
                    : Convert.ToString(reader["FastCopyPath"]);
                workflowAction.SourceFile = Convert.IsDBNull(reader["SourceFile"])
                    ? string.Empty
                    : Convert.ToString(reader["SourceFile"]);
                workflowAction.SourceFolder = Convert.IsDBNull(reader["SourceFolder"])
                    ? string.Empty
                    : Convert.ToString(reader["SourceFolder"]);
                workflowAction.TargetFolder = Convert.IsDBNull(reader["TargetFolder"])
                    ? string.Empty
                    : Convert.ToString(reader["TargetFolder"]);
                ////25/07/2012
                workflowAction.DiskGroup = Convert.IsDBNull(reader["DiskGroup"])
                    ? string.Empty
                    : Convert.ToString(reader["DiskGroup"]);
                workflowAction.DiskName = Convert.IsDBNull(reader["DiskName"])
                    ? string.Empty
                    : Convert.ToString(reader["DiskName"]);
                //workflowAction.PrStorageImageId = reader.IsDBNull(FLD_PRSTORAGEIMAGEID) ? string.Empty : reader.GetString(FLD_PRSTORAGEIMAGEID);
                //workflowAction.DrStorageImageId = reader.IsDBNull(FLD_DRSTORAGEIMAGEID) ? 0 : reader.GetInt32(FLD_DRSTORAGEIMAGEID);
                ////31/07/2012
                workflowAction.RsyncPath = Convert.IsDBNull(reader["RsyncPath"])
                    ? string.Empty
                    : Convert.ToString(reader["RsyncPath"]);
                workflowAction.TargetFile = Convert.IsDBNull(reader["TargetFile"])
                    ? string.Empty
                    : Convert.ToString(reader["TargetFile"]);
                workflowAction.MachineName = Convert.IsDBNull(reader["MachineName"])
                    ? string.Empty
                    : Convert.ToString(reader["MachineName"]);
                workflowAction.SnapShotName = Convert.IsDBNull(reader["SnapShotName"])
                    ? string.Empty
                    : Convert.ToString(reader["SnapShotName"]);
                workflowAction.TimeOut = Convert.IsDBNull(reader["TimeOut"])
                    ? string.Empty
                    : Convert.ToString(reader["TimeOut"]);
                workflowAction.VirtualMachine = Convert.IsDBNull(reader["VirtualMachine"])
                    ? string.Empty
                    : Convert.ToString(reader["VirtualMachine"]);
                ////17/08/2012
                workflowAction.DestinationPath = Convert.IsDBNull(reader["DestinationPath"])
                    ? string.Empty
                    : Convert.ToString(reader["DestinationPath"]);
                //workflowAction.SnapMirrorVolume = reader.IsDBNull(FLD_SNAPMIRRORVOLUME) ? string.Empty : reader.GetString(FLD_SNAPMIRRORVOLUME);
                workflowAction.TargetMachine = Convert.IsDBNull(reader["TargetMachine"])
                    ? string.Empty
                    : Convert.ToString(reader["TargetMachine"]);

                workflowAction.VmPath = Convert.IsDBNull(reader["VmPath"])
                    ? string.Empty
                    : Convert.ToString(reader["VmPath"]);
                workflowAction.OriginalText = Convert.IsDBNull(reader["OriginalText"])
                    ? string.Empty
                    : Convert.ToString(reader["OriginalText"]);
                workflowAction.NewText = Convert.IsDBNull(reader["NewText"])
                    ? string.Empty
                    : Convert.ToString(reader["NewText"]);

                //16/11/2012
                workflowAction.RouterConfiguration = Convert.IsDBNull(reader["RouterConfiguration"])
                    ? string.Empty
                    : Convert.ToString(reader["RouterConfiguration"]);
                //20/11/2012
                workflowAction.InterfacePassword = Convert.IsDBNull(reader["InterfacePassword"])
                    ? string.Empty
                    : Convert.ToString(reader["InterfacePassword"]);
                //26/11/2012
                workflowAction.DeviceGroup = Convert.IsDBNull(reader["DeviceGroup"])
                    ? string.Empty
                    : Convert.ToString(reader["DeviceGroup"]);
                //27/11/2012
                workflowAction.FileSystem = Convert.IsDBNull(reader["FileSystem"])
                    ? string.Empty
                    : Convert.ToString(reader["FileSystem"]);
                //27/11/2012
                workflowAction.WaitTime = Convert.IsDBNull(reader["WaitTime"])
                    ? string.Empty
                    : Convert.ToString(reader["WaitTime"]);
                //08/12/2012
                workflowAction.ApplicationName = Convert.IsDBNull(reader["ApplicationName"])
                    ? string.Empty
                    : Convert.ToString(reader["ApplicationName"]);
                //09/12/2012
                workflowAction.MapFile = Convert.IsDBNull(reader["MapFile"])
                    ? string.Empty
                    : Convert.ToString(reader["MapFile"]);
                workflowAction.Task = Convert.IsDBNull(reader["Task"]) ? string.Empty : Convert.ToString(reader["Task"]);
                //12/12/2012
                workflowAction.FileSystemMountPoint = Convert.IsDBNull(reader["FileSystemMountPoint"])
                    ? string.Empty
                    : Convert.ToString(reader["FileSystemMountPoint"]);
                //03/01/2013
                workflowAction.WorkFlowId = Convert.IsDBNull(reader["WorkFlowId"])
                    ? 0
                    : Convert.ToInt32(reader["WorkFlowId"]);
                //workflowAction.WorkFlowActionId = Convert.IsDBNull(reader["WorkFlowActionId"])
                //    ? 0
                //    : Convert.ToInt32(reader["WorkFlowActionId"]);
                workflowAction.WorkFlowActionId = Convert.IsDBNull(reader["WorkFlowActionId"]) ? string.Empty : Convert.ToString(reader["WorkFlowActionId"]);

                workflowAction.DependencyType = Convert.IsDBNull(reader["DependencyType"])
                    ? 0
                    : Convert.ToInt32(reader["DependencyType"]);
                workflowAction.Time = Convert.IsDBNull(reader["WFTime"]) ? string.Empty : Convert.ToString(reader["WFTime"]);
                //24/01/2013
                workflowAction.TargetDatabase = Convert.IsDBNull(reader["TargetDatabase"])
                    ? 0
                    : Convert.ToInt32(reader["TargetDatabase"]);
                //01/04/2013
                workflowAction.BackUpFile = Convert.IsDBNull(reader["BackUpFile"])
                    ? string.Empty
                    : Convert.ToString(reader["BackUpFile"]);
                workflowAction.TraceFile = Convert.IsDBNull(reader["TraceFile"])
                    ? string.Empty
                    : Convert.ToString(reader["TraceFile"]);
                //15/04/2013
                workflowAction.Expression = Convert.IsDBNull(reader["Expression"])
                    ? 0
                    : Convert.ToInt32(reader["Expression"]);
                workflowAction.ProcessCount = Convert.IsDBNull(reader["ProcessCount"])
                    ? 0
                    : Convert.ToInt32(reader["ProcessCount"]);
                workflowAction.RTO = Convert.IsDBNull(reader["RTO"]) ? string.Empty : Convert.ToString(reader["RTO"]);
                //12/06/2013
                workflowAction.HDisc = Convert.IsDBNull(reader["HDisc"]) ? string.Empty : Convert.ToString(reader["HDisc"]);
                workflowAction.HitachihorcomInstance = Convert.IsDBNull(reader["HitachihorcomInstance"])
                    ? string.Empty
                    : Convert.ToString(reader["HitachihorcomInstance"]);
                //19/10/2013
                workflowAction.RestorePoint = Convert.IsDBNull(reader["RestorePoint"])
                    ? string.Empty
                    : Convert.ToString(reader["RestorePoint"]);
                //06/11/2013
                workflowAction.AlertText = Convert.IsDBNull(reader["AlertText"])
                    ? string.Empty
                    : Convert.ToString(reader["AlertText"]);
                workflowAction.AlertModeType = Convert.IsDBNull(reader["AlertModeType"])
                    ? 0
                    : Convert.ToInt32(reader["AlertModeType"]);
                workflowAction.ProcessFlow = Convert.IsDBNull(reader["ProcessFlow"])
                    ? string.Empty
                    : Convert.ToString(reader["ProcessFlow"]);
                //07/11/2013
                workflowAction.VolName = Convert.IsDBNull(reader["VolName"])
                    ? string.Empty
                    : Convert.ToString(reader["VolName"]);
                workflowAction.Drive = Convert.IsDBNull(reader["Drive"]) ? string.Empty : Convert.ToString(reader["Drive"]);
                //04/12/2013
                workflowAction.SubnetMask = Convert.IsDBNull(reader["SubnetMask"])
                    ? string.Empty
                    : Convert.ToString(reader["SubnetMask"]);
                workflowAction.GateWay = Convert.IsDBNull(reader["GateWay"])
                    ? string.Empty
                    : Convert.ToString(reader["GateWay"]);
                workflowAction.PrimaryDNS = Convert.IsDBNull(reader["PrimaryDNS"])
                    ? string.Empty
                    : Convert.ToString(reader["PrimaryDNS"]);
                workflowAction.SecoundaryDNS = Convert.IsDBNull(reader["SecoundaryDNS"])
                    ? string.Empty
                    : Convert.ToString(reader["SecoundaryDNS"]);
                workflowAction.ServiceName = Convert.IsDBNull(reader["ServiceName"])
                    ? string.Empty
                    : Convert.ToString(reader["ServiceName"]);
                workflowAction.Appuser = Convert.IsDBNull(reader["Appuser"]) ?
                     string.Empty
                    : Convert.ToString(reader["Appuser"]);
                workflowAction.ProcessID = Convert.IsDBNull(reader["ProcessId"])
                    ? string.Empty
                    : Convert.ToString(reader["ProcessId"]);
                workflowAction.Shellprompt = Convert.IsDBNull(reader["Shellprompt"])
                    ? string.Empty
                    : Convert.ToString(reader["Shellprompt"]);


                workflowAction.DSCSLISERVER = Convert.IsDBNull(reader["DSCSLISERVER"]) ? 0 : Convert.ToInt32(reader["DSCSLISERVER"]);
                workflowAction.hmcserver = Convert.IsDBNull(reader["hmcserver"]) ? 0 : Convert.ToInt32(reader["hmcserver"]);
                workflowAction.LSSID = Convert.IsDBNull(reader["LSSID"]) ? string.Empty : Convert.ToString(reader["LSSID"]);
                workflowAction.CreatorId = Convert.IsDBNull(reader["CreatorId"])
                    ? 0
                    : Convert.ToInt32(reader["CreatorId"]);
                workflowAction.RelationshipId = Convert.IsDBNull(reader["RelationshipId"]) ? string.Empty : Convert.ToString(reader["RelationshipId"]);
                workflowAction.CheckState = Convert.IsDBNull(reader["CheckState"]) ? string.Empty : Convert.ToString(reader["CheckState"]);

                workflowAction.RecoveryPlan = Convert.IsDBNull(reader["RecoveryPlan"]) ? string.Empty : Convert.ToString(reader["RecoveryPlan"]);
                workflowAction.ClusterName = Convert.IsDBNull(reader["ClusterName"]) ? string.Empty : Convert.ToString(reader["ClusterName"]);
                workflowAction.ClusterGroupResource = Convert.IsDBNull(reader["ClusterGroupResource"]) ? string.Empty : Convert.ToString(reader["ClusterGroupResource"]);

                workflowAction.EmailSuccess = Convert.IsDBNull(reader["EmailSuccess"]) ? string.Empty : Convert.ToString(reader["EmailSuccess"]);
                workflowAction.EmailFail = Convert.IsDBNull(reader["EmailFail"]) ? string.Empty : Convert.ToString(reader["EmailFail"]);
                workflowAction.SmsSuccess = Convert.IsDBNull(reader["SmsSuccess"]) ? string.Empty : Convert.ToString(reader["SmsSuccess"]);
                workflowAction.SmsFail = Convert.IsDBNull(reader["SmsFail"]) ? string.Empty : Convert.ToString(reader["SmsFail"]);
                workflowAction.AlertMechanismType = Convert.IsDBNull(reader["AlertMechanismType"]) ? string.Empty : Convert.ToString(reader["AlertMechanismType"]);
                workflowAction.ScriptBlock = Convert.IsDBNull(reader["ScriptBlock"]) ? string.Empty : Convert.ToString(reader["ScriptBlock"]);
                workflowAction.URL = Convert.IsDBNull(reader["URL"]) ? string.Empty : Convert.ToString(reader["URL"]);
                workflowAction.HTMLContents = Convert.IsDBNull(reader["HTMLContents"]) ? string.Empty : Convert.ToString(reader["HTMLContents"]);
                workflowAction.ZoneName = Convert.IsDBNull(reader["ZoneName"]) ? string.Empty : Convert.ToString(reader["ZoneName"]);
                workflowAction.CellNo = Convert.IsDBNull(reader["CellNo"]) ? string.Empty : Convert.ToString(reader["CellNo"]);
                workflowAction.EmailId = Convert.IsDBNull(reader["EmailId"]) ? string.Empty : Convert.ToString(reader["EmailId"]);
                workflowAction.Resource = Convert.IsDBNull(reader["ClusterResource"]) ? string.Empty : Convert.ToString(reader["ClusterResource"]);
                workflowAction.AlertUsers = Convert.IsDBNull(reader["AlertUsers"]) ? string.Empty : Convert.ToString(reader["AlertUsers"]);
                workflowAction.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                    ? DateTime.MinValue
                    : Convert.ToDateTime(reader["CreateDate"].ToString());
                workflowAction.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
                workflowAction.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                    ? DateTime.MinValue
                    : Convert.ToDateTime(reader["UpdateDate"].ToString());
                //09.11.2017
                workflowAction.VMIsClustered = Convert.IsDBNull(reader["VMIsClustered"])
                  ? string.Empty
                  : Convert.ToString(reader["VMIsClustered"]);
                workflowAction.ControllerType = Convert.IsDBNull(reader["ControllerType"]) ? string.Empty : Convert.ToString(reader["ControllerType"]);
                workflowAction.ControllerLocation = Convert.IsDBNull(reader["ControllerLocation"]) ? string.Empty : Convert.ToString(reader["ControllerLocation"]);
                workflowAction.DefineValue = Convert.IsDBNull(reader["DefineValue"]) ? string.Empty : Convert.ToString(reader["DefineValue"]);
                workflowAction.SharingOption = Convert.IsDBNull(reader["SharingOption"]) ? string.Empty : Convert.ToString(reader["SharingOption"]);
                workflowAction.ClusterSharedVolumeStatus = Convert.IsDBNull(reader["ClusterSharedVolumeStatus"]) ? string.Empty : Convert.ToString(reader["ClusterSharedVolumeStatus"]);

                workflowAction.InfraobjectId = Convert.IsDBNull(reader["InfraobjectId"]) ? 0 : Convert.ToInt32(reader["InfraobjectId"]);
                workflowAction.EBDRStatusId = Convert.IsDBNull(reader["EBDRStatusId"]) ? 0 : Convert.ToInt32(reader["EBDRStatusId"]);
                workflowAction.IsWLST = Convert.IsDBNull(reader["IsWLST"]) ? 0 : Convert.ToInt32(reader["IsWLST"]);

                workflowAction.Version = Convert.IsDBNull(reader["Version"]) ? 1.0 : Convert.ToDouble(reader["Version"]);

                WorkflowActionList.Add(workflowAction);
            }
            return WorkflowActionList;
        }

        #endregion WorkflowActionHistory

        #endregion Methods
    }
}