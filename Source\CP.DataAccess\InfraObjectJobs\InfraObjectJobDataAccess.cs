﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class InfraObjectJobDataAccess : BaseDataAccess, IInfraObjectJobDataAccess
    {
        #region Constructors

        public InfraObjectJobDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<InfraObjectJob> CreateEntityBuilder<InfraObjectJob>()
        {
            return (new InfraObjectJobBuilder()) as IEntityBuilder<InfraObjectJob>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="InfraObjectJob" /> into infraObject_job table.
        /// </summary>
        /// <param name="InfraObjectJob">Pass InfraObject job detail</param>
        /// <returns>InfraObjectJob</returns>
        /// <author><PERSON><PERSON><PERSON></author>

        InfraObjectJob IInfraObjectJobDataAccess.Add(InfraObjectJob InfraObjectJob)
        {
            try
            {
                const string sp = "InfraObjectJob_Create";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, InfraObjectJob.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, InfraObjectJob.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring + "iStorageImageId", DbType.String, InfraObjectJob.StorageImageId);
                    Database.AddInParameter(cmd, Dbstring + "iJobId", DbType.Int32, InfraObjectJob.JobId);
                    Database.AddInParameter(cmd, Dbstring + "iTriggerName", DbType.String, InfraObjectJob.TriggerName);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpression", DbType.String, InfraObjectJob.CronExpression);
                    Database.AddInParameter(cmd, Dbstring + "iCronTime", DbType.String, InfraObjectJob.CronTime);
                    Database.AddInParameter(cmd, Dbstring + "iNextFireTime", DbType.String, InfraObjectJob.NextFireTime);
                    Database.AddInParameter(cmd, Dbstring + "iLastFireTime", DbType.String, InfraObjectJob.LastFireTime);
                    Database.AddInParameter(cmd, Dbstring + "iJobStatus", DbType.String, InfraObjectJob.JobStatus);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjType", DbType.Int32, InfraObjectJob.InfraObjType);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, InfraObjectJob.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpressionChange", DbType.Int32, InfraObjectJob.CronExpressionChange);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        InfraObjectJob = reader.Read() ? CreateEntityBuilder<InfraObjectJob>().BuildEntity(reader, InfraObjectJob) : null;
                    }
                    return InfraObjectJob;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting InfraObjectJob Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="InfraObjectJob" /> from infraObject_job table by id.
        /// </summary>
        /// <param name="id">Pass id</param>
        /// <returns>InfraObjectJob</returns>
        /// <author>Shivraj Mujumale</author>
        InfraObjectJob IInfraObjectJobDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObjectJob_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<InfraObjectJob>()).BuildEntity(reader, new InfraObjectJob())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        InfraObjectJob IInfraObjectJobDataAccess.GetInfraObjectByJobType(int id, string job)
        {
            try
            {
                //if (id < 1)
                //{
                //    throw new ArgumentNullException("id");
                //}

                const string sp = "INFRAOBJJOB_GETBYJOBPARABYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iJob", DbType.String, job);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<InfraObjectJob>()).BuildEntity(reader, new InfraObjectJob())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        InfraObjectJob IInfraObjectJobDataAccess.GetInfraObjectBusinessByJobType(int id, string job)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "INFRAOBJJOB_GETBYBUSIJOBTYPE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iBusinessId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.String, job);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<InfraObjectJob>()).BuildEntity(reader, new InfraObjectJob())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        InfraObjectJob IInfraObjectJobDataAccess.GetInfraObjectByInfraId(int id)
        {
            try
            {
                //if (id < 1)
                //{
                //    throw new ArgumentNullException("id");
                //}

                const string sp = "INFRAOBJECTJOB_GETBYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<InfraObjectJob>()).BuildEntity(reader, new InfraObjectJob())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Update <see cref="InfraObjectJob" /> into infraObject_job table.
        /// </summary>
        /// <param name="InfraObjectJob">Pass InfraObjectJob detail</param>
        /// <returns>InfraObjectJob</returns>
        /// <author>Shivraj Mujumale</author>
        /// <Modified> Kuntesh Thakker - 10-05-2014 </Modified>
        InfraObjectJob IInfraObjectJobDataAccess.Update(InfraObjectJob InfraObjectJob)
        {
            try
            {
                const string sp = "InfraObjectJob_Update";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, InfraObjectJob.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, InfraObjectJob.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring + "iStorageImageId", DbType.String, InfraObjectJob.StorageImageId);
                    Database.AddInParameter(cmd, Dbstring + "iJobId", DbType.String, InfraObjectJob.JobId);
                    Database.AddInParameter(cmd, Dbstring + "iTriggerName", DbType.String, InfraObjectJob.TriggerName);
                    //Database.AddInParameter(cmd, Dbstring + "iCronExpression", DbType.String, InfraObjectJob.CronExpression);
                    Database.AddInParameter(cmd, Dbstring + "iNextFireTime", DbType.String, InfraObjectJob.NextFireTime);
                    Database.AddInParameter(cmd, Dbstring + "iLastFireTime", DbType.String, InfraObjectJob.LastFireTime);
                    Database.AddInParameter(cmd, Dbstring + "iJobStatus", DbType.String, InfraObjectJob.JobStatus);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, InfraObjectJob.UpdatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        InfraObjectJob = reader.Read() ? CreateEntityBuilder<InfraObjectJob>().BuildEntity(reader, InfraObjectJob) : null;
                    }

                    return InfraObjectJob;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Infra Object job Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     InfraObjectJob_GetAll <see cref="InfraObjectJob" /> from infraObject_job table.
        /// </summary>
        /// <returns>InfraObjectJobs List</returns>
        /// <author>Pratik T</author>
        /// <Modified> Kuntesh Thakker - 10-05-2014 </Modified>
        IList<InfraObjectJob> IInfraObjectJobDataAccess.GetAll()
        {
            try
            {
                const string sp = "InfraObjectJob_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObjectJob>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }




        /// <summary>
        ///     InfraObjectJob_GetByGroupId <see cref="InfraObjectJob" /> from infraObject_job table.
        /// </summary>
        /// <returns>InfraObjectJobs List</returns>
        /// <param name="infraObjectId">infraObjectId</param>
        /// <author>Pratik T</author>
        /// <Modified> Kuntesh Thakker - 10-05-2014 </Modified>
        IList<InfraObjectJob> IInfraObjectJobDataAccess.GetByInfraObjectId(int infraObjectId)
        {
            try
            {
                if (infraObjectId < 1)
                {
                    throw new ArgumentNullException("infraObjectId");
                }

                const string sp = "INFRAOBJECTJOB_GETBYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObjectJob>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetByInfraObjectId" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<InfraObjectJob> IInfraObjectJobDataAccess.GetInfraObjectJobByTime(int repid)
        {
            try
            {
                //if (repid < 1)
                //{
                //    throw new ArgumentNullException("repid");
                //}

                const string sp = "INFRAOBJJOB_GETBYINFRATIMEID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iRepid", DbType.Int32, repid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObjectJob>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetByInfraObjectId" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     InfraObjectJob_GetByJobId <see cref="InfraObjectJob" /> from infraObject_job table.
        /// </summary>
        /// <returns>InfraObjectJobs List</returns>
        /// <param name="infraObjectId">infraObjectId</param>
        /// <author>Pratik T</author>
        /// <Modified> Kuntesh Thakker - 10-05-2014 </Modified>
        IList<InfraObjectJob> IInfraObjectJobDataAccess.GetByJobId(int jobId)
        {
            try
            {
                if (jobId < 1)
                {
                    throw new ArgumentNullException("jobId");
                }

                const string sp = "InfraObjectJob_GetByJobId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iJobId", DbType.Int32, jobId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObjectJob>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ICompanyProfileDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     InfraObjectJob_GetByBusinessServiceId <see cref="InfraObjectJob list" /> from infraObject_job table.
        /// </summary>
        /// <returns>InfraObjectJobs List</returns>
        /// <param name="businessserviceId">businessserviceId</param>
        /// <author>Pratik T</author>
        /// <Modified> Kuntesh Thakker - 10-05-2014 </Modified>
        IList<InfraObjectJob> IInfraObjectJobDataAccess.GetByBusinessServiceId(int businessserviceId)
        {
            try
            {
                if (businessserviceId < 1)
                {
                    throw new ArgumentNullException("businessserviceId");
                }

                const string sp = "INFRAOBJECTJOB_GETBYBUSSVICEID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, businessserviceId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObjectJob>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetByBusinessServiceId" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        IList<InfraObjectJob> IInfraObjectJobDataAccess.GetInfraObjectByBusinessId(int infratype)
        {
            try
            {
                if (infratype < 1)
                {
                    throw new ArgumentNullException("infratype");
                }

                const string sp = "InfraObjectJob_GetByInfraType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.Int32, infratype);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObjectJob>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetByBusinessServiceId" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     InfraObjectJob_GetByStorageImageId <see cref="InfraObjectJob list" /> from infraObject_job table.
        /// </summary>
        /// <returns>InfraObjectJobs List</returns>
        /// <param name="storageImageId">storageImageId</param>
        /// <author>Pratik T</author>
        /// <Modified> Kuntesh Thakker - 10-05-2014 </Modified>
        IList<InfraObjectJob> IInfraObjectJobDataAccess.GetByStorageImageId(string storageImageId)
        {
            try
            {
                const string sp = "INFRAOBJECTJOB_GETBYSTRGEIMGID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iStorageImageId", DbType.AnsiString, storageImageId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObjectJob>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetByStorageImageId" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     InfraObjectJob_GetByCatagorey <see cref="InfraObjectJob list" /> from infraObject_job table.
        /// </summary>
        /// <returns>InfraObjectJobs List</returns>
        /// <param name="category">category</param>
        /// <author>Pratik T</author>
        /// <Modified> Kuntesh Thakker - 10-05-2014 </Modified>
        IList<InfraObjectJob> IInfraObjectJobDataAccess.GetByCategory(string category)
        {
            try
            {
                const string sp = "InfraObjectJob_GetByCategory";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iCategory", DbType.AnsiString, category);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObjectJob>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetInfraObjectJobsByCatagorey" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="InfraObject" /> from infraObject_job table by jobId.
        /// </summary>
        /// <param name="id">Pass jobId</param>
        /// <returns>bool</returns>
        /// <author>Pratik T</author>
        /// <Modified> Kuntesh Thakker - 10-05-2014 </Modified>
        bool IInfraObjectJobDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "InfraObjectJob_DeleteById";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting InfraObjectJob Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        bool IInfraObjectJobDataAccess.DelteByInfraObjectId(InfraObjectJob InfraObjectJob)
        {
            try
            {
                const string sp = "INFRAOBJECTJOB_DeleteInfraId";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iJobId", DbType.Int32, InfraObjectJob.JobId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, InfraObjectJob.InfraObjectId);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting DatabaseNodes Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        bool IInfraObjectJobDataAccess.DeleteByBusinessServiceId(InfraObjectJob InfraObjectJob)
        {
            try
            {
                const string sp = "INFRAOBJJOB_DELBYBUSISERVICEID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iJobId", DbType.Int32, InfraObjectJob.JobId);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, InfraObjectJob.BusinessServiceId);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting DatabaseNodes Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        bool IInfraObjectJobDataAccess.DeleteByStorageImageId(InfraObjectJob InfraObjectJob)
        {
            try
            {
                const string sp = "INFRAOBJECTJOB_DelByStorgImgId";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iJobId", DbType.Int32, InfraObjectJob.JobId);
                    Database.AddInParameter(cmd, Dbstring + "iStorageImageId", DbType.Int32, InfraObjectJob.StorageImageId);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting DatabaseNodes Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     update <see cref="InfraObjectJob" /> into infraObject_job table by jobId
        /// </summary>
        /// <param name="InfraObjectJob">InfraObjectJob</param>
        /// <returns>bool</returns>
        /// <author>Pratik T</author>
        /// <Modified> Kuntesh Thakker - 10-05-2014 </Modified>
        bool IInfraObjectJobDataAccess.UpdateById(InfraObjectJob InfraObjectJob)
        {
            try
            {
                if (InfraObjectJob.Id < 1)
                {
                    throw new ArgumentNullException("InfraObjectJob");
                }

                const string sp = "INFRAOBJECTJOB_UPTECRONEXPBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, InfraObjectJob.Id);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpression", DbType.String, InfraObjectJob.CronExpression);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, InfraObjectJob.UpdatorId);

                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating InfraObjectJob Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        /// <summary>
        ///     update <see cref="InfraObjectJob" /> into infraObject_job table by id
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="cronExp">id</param>
        /// <param name="cronTime">id</param>
        /// <returns>bool</returns>
        /// <author>Pratik T</author>
        ///  <Modified> Kuntesh Thakker - 10-05-2014 </Modified>
        bool IInfraObjectJobDataAccess.FillCronById(int id, string cronExp, string cronTime, int changecorn, int jobid)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObjectJob_FillCron";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpression", DbType.String, cronExp);
                    Database.AddInParameter(cmd, Dbstring + "iCronTime", DbType.String, cronTime);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpressionChange", DbType.Int32, changecorn);
                    Database.AddInParameter(cmd, Dbstring + "iJobId", DbType.Int32, jobid);

                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating InfraObjectJob Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        bool IInfraObjectJobDataAccess.FillBusinessJobCronById(int businessid, string cronExp, string cronTime, int changecorn, int jobid)
        {
            try
            {
                if (businessid < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "INFRAOBJOB_BUSINESSFILLCRON";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iBusinessId", DbType.Int32, businessid);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpression", DbType.String, cronExp);
                    Database.AddInParameter(cmd, Dbstring + "iCronTime", DbType.String, cronTime);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpressionChange", DbType.Int32, changecorn);
                    Database.AddInParameter(cmd, Dbstring + "iJobId", DbType.Int32, jobid);

                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating InfraObjectJob Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        bool IInfraObjectJobDataAccess.FillInfraObjectJobAllCronById(int id, string cronExp, string cronTime, int changecorn)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObjectJob_AllFillCron";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpression", DbType.String, cronExp);
                    Database.AddInParameter(cmd, Dbstring + "iCronTime", DbType.String, cronTime);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpressionChange", DbType.Int32, changecorn);

                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating InfraObjectJob Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        bool IInfraObjectJobDataAccess.FillCronByInfraId(int id, string cronExp, string cronTime, int changecorn)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObjectJob_InfraFillCron";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpression", DbType.String, cronExp);
                    Database.AddInParameter(cmd, Dbstring + "iCronTime", DbType.String, cronTime);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpressionChange", DbType.Int32, changecorn);

                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating InfraObjectJob Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="InfraObject" /> from infraObject_job table by jobId and infrajobid.
        /// </summary>
        /// <param name="id">Pass jobId,Pass infraobjectid </param>
        /// <returns>bool</returns>
        /// <author>Pratik T</author>
        /// <Modified>Surendra Reddy 09-06-2014 </Modified>
        bool IInfraObjectJobDataAccess.DeleteByInfraIdandJobId(int infraObjectId, int jobId)
        {
            try
            {
                if (infraObjectId < 1 && jobId < 1)
                {
                    throw new ArgumentNullException("jobId");
                }
                const string sp = "InfraObjectJob_DeleteByObjJob";

                //const string sp = "INFRAOBJECTJOB_DeleteInfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, jobId);

                    int returnCode = Database.ExecuteNonQuery(cmd);


                    return returnCode > 0;

                }


            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting InfraObjectJob Entry : " + jobId + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        bool IInfraObjectJobDataAccess.DeleteJobByInfraObjectIdandJobId(string jobname, int jobId)
        {
            try
            {

                const string sp = "INFRAJOBDELETE_BYJOBIDANDNAME";

                //const string sp = "INFRAOBJECTJOB_DeleteInfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.String, jobname);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, jobId);

                    int returnCode = Database.ExecuteNonQuery(cmd);


                    return returnCode > 0;

                }


            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting InfraObjectJob Entry : " + jobId + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }
           
                
        bool IInfraObjectJobDataAccess.ChangeStatusById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "InfraObjectJob_ChangeStatus";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While changing InfraObjectJob status Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }

        }

        bool IInfraObjectJobDataAccess.FillDRReadyJobCronById(int jobid, string cronExp, string cronTime, int changecorn)
        {
            try
            {
                if (jobid < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObjectJob_FillCronByJob";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iJobId", DbType.Int32, jobid);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpression", DbType.String, cronExp);
                    Database.AddInParameter(cmd, Dbstring + "iCronTime", DbType.String, cronTime);
                    Database.AddInParameter(cmd, Dbstring + "iCronExpressionChange", DbType.Int32, changecorn);


                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating InfraObjectJob Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }
        bool IInfraObjectJobDataAccess.DeleteInfraObjectJob(int jobId)
        {
            try
            {

                const string sp = "INFRAJOB_DELETEBYJOBIDD";

                //const string sp = "INFRAOBJECTJOB_DeleteInfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                   //  Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.String, jobname);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, jobId);

                    int returnCode = Database.ExecuteNonQuery(cmd);


                    return returnCode > 0;

                }


            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting InfraObjectJob Entry : " + jobId + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        bool IInfraObjectJobDataAccess.DeleteInfraJob(int jobId)
        {
            try
            {

                const string sp = "INFRAOBJ_JOB_DELETEBYID";

                //const string sp = "INFRAOBJECTJOB_DeleteInfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    //    Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.String, jobname);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, jobId);

                    int returnCode = Database.ExecuteNonQuery(cmd);


                    return returnCode > 0;

                }


            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting InfraObjectJob Entry : " + jobId + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }


        IList<InfraObjectJob> IInfraObjectJobDataAccess.GetAllJobs()
        {
            IList<InfraObjectJob> infrajob = new List<InfraObjectJob>();
            try
            {
                const string sp = "InfraObjectJob_GetAllJob";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {

                            var Job = new InfraObjectJob
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]),
                                BusinessServiceId = Convert.IsDBNull(reader["BusinessServiceId"])
                                    ? 0
                                    : Convert.ToInt32(reader["BusinessServiceId"]),
                                StorageImageId = Convert.IsDBNull(reader["StorageImageId"])
                                    ? string.Empty
                                    : Convert.ToString(reader["StorageImageId"]),
                                JobId = Convert.IsDBNull(reader["JobId"]) ? 0 : Convert.ToInt32(reader["JobId"]),
                                TriggerName = Convert.IsDBNull(reader["TriggerName"])
                                     ? string.Empty
                                     : Convert.ToString(reader["TriggerName"]),
                                CronExpression = Convert.IsDBNull(reader["CronExpression"])
                                    ? string.Empty
                                    : Convert.ToString(reader["CronExpression"]),
                                CronTime = Convert.IsDBNull(reader["CronTime"])
                                    ? string.Empty
                                    : Convert.ToString(reader["CronTime"]),
                                NextFireTime = Convert.IsDBNull(reader["NextFireTime"])
                                     ? string.Empty
                                     : Convert.ToString(reader["NextFireTime"]),
                                LastFireTime = Convert.IsDBNull(reader["LastFireTime"])
                                    ? string.Empty
                                    : Convert.ToString(reader["LastFireTime"]),
                                JobStatus = Convert.IsDBNull(reader["JobStatus"])
                                     ? string.Empty
                                     : Convert.ToString(reader["JobStatus"]),
                                IsEnabled = Convert.IsDBNull(reader["IsEnabled"]) ? 0 : Convert.ToInt32(reader["IsEnabled"]),
                                InfraObjType = Convert.IsDBNull(reader["InfraObjType"]) ? 0 : Convert.ToInt32(reader["InfraObjType"]),
                                IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]),
                                CronExpressionChange = Convert.IsDBNull(reader["CronExpressionChange"]) ? 0 : Convert.ToInt32(reader["CronExpressionChange"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"])
                                    ? DateTime.MinValue
                                    : Convert.ToDateTime(reader["CreateDate"]),
                                UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]),
                                UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                                    ? DateTime.MinValue
                                    : Convert.ToDateTime(reader["UpdateDate"]),
                                RecoveryType = Convert.IsDBNull(reader["REPLICATIONTYPE"]) ? 0 : Convert.ToInt32(reader["REPLICATIONTYPE"]),
                                NAME = Convert.IsDBNull(reader["NAME"])
                                     ? string.Empty
                                     : Convert.ToString(reader["NAME"]),

                            };

                            infrajob.Add(Job);

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
            return infrajob;
        }


        IList<InfraObjectJob> IInfraObjectJobDataAccess.GetInfraObjectJobBy_Name(string name)
        {
           
            try
            {
                const string sp = "InfraObjectJob_GetAllJobByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObjectJob>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectJobDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
           
        }

        #endregion Methods
    }
}