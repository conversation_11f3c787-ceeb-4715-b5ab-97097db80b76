﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using CP.Helper;

namespace CP.DataAccess
{
    internal sealed class DropDownBuilder : IEntityBuilder<DropDown>
    {
        IList<DropDown> IEntityBuilder<DropDown>.BuildEntities(IDataReader reader)
        {
            var DropDownUI_List = new List<DropDown>();

            while (reader.Read())
            {
                DropDownUI_List.Add(((IEntityBuilder<DropDown>)this).BuildEntity(reader, new DropDown()));
            }

            return (DropDownUI_List.Count > 0) ? DropDownUI_List : null;
        }

        DropDown IEntityBuilder<DropDown>.BuildEntity(IDataReader reader, DropDown _DropDown)
        {
            _DropDown.Id = Convert.IsDBNull(reader["Id"]) ? "0" : Convert.ToString(reader["Id"]);
            _DropDown.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
            return _DropDown;
        }
    }
}