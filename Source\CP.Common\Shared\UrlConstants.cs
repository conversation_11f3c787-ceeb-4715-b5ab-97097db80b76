﻿namespace CP.Common.Shared
{
    public sealed partial class Constants
    {
        public static class UrlConstants
        {
            public static class Params
            {
                public const string ID = "id";
                public const string UserId = "userid";
                public const string ApplicationId = "appid";
                public const string GroupId = "grpid";
                public const string InfraObjectjobId = "infraObjectjobid";
                public const string Domaindetails = "domaindetailsid";
                public const string ApplicationGroupId = "applicationgroupid";
                public const string ServerId = "serverid";
                public const string ServerType = "servertype";
                public const string SiteId = "siteid";
                public const string CompanyId = "companyid";
                public const string DNSServerDetailsId = "dnsserverdetailsid";
                public const string ReplicationId = "replicationid";
                public const string InfraObjectId = "infraobjectId";
                public const string BusinessfunctionId = "BusinessfunctionId";
                public const string SingleSignOnId = "singlesignonid";
                public const string Whatif = "Whatif";
                public const string piechart = "piechart";
                public const string biamode = "biamode";

                public const string AlertId = "alertid";
                public const string SettingId = "settingid";
                public const string ReportId = "reportid";
                public const string WorkflowId = "workflowid";
                public const string JobId = "jobid";
                public const string UserGroupId = "usergroupid";
                public const string UserActivityId = "useractivityid";
                public const string OracleDataguardId = "oracledataguardid";
                public const string GlobalmirrorId = "globalmirrorid";
                public const string BIAId = "biaid";

                public const string UserRole = "usertype";
                public const string UserName = "userName";
                public const string UserPassword = "userPassword";
                public const string Message = "msg";

                public const string ImpactTypeMasterId = "impacttypemasterid";
                public const string ImpactMasterId = "impactmasterid";

                public const string EmailTo = "emailto";

                public const string EmailId = "emailid";
                public const string SMSTo = "smsto";
                public const string ApplicationGroupName = "applicationgroupname";
                public const string GroupName = "groupname";
                public const string SolutionType = "solid";

                public const string ReplicationType = "replicationtype";
                public const string ReplicationName = "replicationname";
                public const string ExchangegroupId = "exchangegroupid";

                public const string NodeId = "nodeid";

                public const string DatabaseId = "databaseid";
                public const string DatabaseType = "databasetype";

                public const string BusinessserviceId = "businessserviceid";
                public const string InfrastructureId = "infrastructureid";
                public const string DataSyncPropertiesId = "datasyncpropertiesid";

                public const string ReportScheduleId = "reportscheduleid";

                public const string WorkflowProfileId = "workflowprofileid";

                public const string CustomExceptionId = "customexceptionid";

                public const string configType = "configType";
                public const string ProfileID = "ProfileID";
                public const string BIAComponnentType = "BIAComponnentType";
                public const string IncidentType = "IncidentType";
                public const string IncidentWhatIfAnalysis = "IncidentWhatIfAnalysis";
                public const string IncidentSortByBusinessService = "IncidentSortByBusinessService";

                public const string VeritasClusterId = "VeritasClusterid";
                public const string HACMPClusterId = "hacmpclusterid";

                public const string Edit = "Edit";

                public const string RoboCopyOptionId = "robocopyoptionid";
                public const string RSyncOptionId = "RSyncOptionId";
                public const string LoadBalancerId = "LoadBalancerId";
            }

            public static class Urls
            {
                public static class Admin
                {
                    private const string Directory = "~/Admin/";
                    public const string HomePage = Directory + "Home.aspx";
                    public const string CommandCenter = Directory + "CommandCenter.aspx";
                    public const string ManageDBInfraObject = Directory + "InfraObjectDBManagement.aspx";
                    public const string MonitorDBInfraObject = Directory + "MonitorDBInfraObject.aspx";
                    public const string ChangePassword = Directory + "ChangePassword.aspx";
                    public const string DatabaseBackup = Directory + "DatabaseBackupManagement.aspx";
                    public const string InfraMonitorApplication = Directory + "InfraMonitorApplication.aspx";
                    public const string InfraManageApplication = Directory + "InfraApplicationManagement.aspx";
                    public const string MonitorApplication = Directory + "MonitorApplication.aspx";
                    public const string ManageApplication = Directory + "ApplicationManagement.aspx";
                    public const string SQLManageInfraObject = Directory + "MSSQLMonitoring.aspx";
                    public const string ArchiveData = Directory + "Archive.aspx";
                    public const string BusinessProcess = Directory + "BusinessProcessAuto.aspx";
                    public const string ConfigureBusinessService = Directory + "ConfigureBusinessService.aspx";
                    public const string ConfigureSite = Directory + "ConfigureSite.aspx";
                    public const string PostgreSQLMonitoring = Directory + "PostgreSQLMonitoring.aspx";
                    // public const string BusinessImpactAnalysis = Directory + "BussinessImpactAnalysis.aspx";
                    public const string BusinessImpactAnalysisList = Directory + "BusinessImpactAnalysisList.aspx";
                    public const string InfraObjectScheduledWorkflow = Directory + "InfraObjectScheduledWorkflow.aspx";
                    public const string SQL2000MonitorInfraObject = Directory + "MSSQL2000DatasyncMonitiring.aspx";
                    public const string JobManagement = Directory + "JobManagement.aspx";
                    public const string WorkflowProfileManagement = Directory + "WorkflowProfileManagement.aspx";

                    //public const string InfraObjectJob_LIST = DIRECTORY + "InfraObjectJobList.aspx";
                    // public const string WorkflowProfileManagement = Directory + "WorkflowProfileManagement.aspx";
                    public const string MonitorServces = Directory + "MonitoringServices.aspx";
                    public const string ParallelWorkflow = Directory + "ParallelWorkflow.aspx";
                    public const string BIAFunctionsReport = Directory + "BIAFunctionsReport.aspx";
                    public const string BIADashboardFunctionsReport = "DashboardBIAFunctionReport.aspx";
                    public const string BIAFunctionDependency = "BIAFunctionDependency.aspx";
                    //Added By shweta
                    public const string BIAProfile = "~/Admin/BIAProfileForm.aspx";

                    public const string BusinessFunctionsBIA = Directory + "BusinessFunctionsBIA.aspx";

                    public const string SVCGlobalMirrorMonitoring = "SVCMonitoringDetails.aspx";
                    public const string MonitorQueue = "~/Admin/MonitoringQueue.aspx";
                    public const string ODGConfig = Directory + "ActiveODGConfig.aspx"; //Prashant(xx)
                    public const string BusinessService_DashboardDetails = "~/Admin/BusinessService_DashboardDetails.aspx";
                    public const string BusinessServiceDashboard = "~/Admin/BusinessServiceDashboard.aspx";
                    public const string ManageBusinessService = "~/Admin/ManageBusinessService.aspx";
                    public const string BSOverview = "~/Admin/BusinessServiceOverview.aspx";
                }

                public static class Alert
                {
                    private const string Directory = "~/Alert/";
                    public const string AlertManagement = Directory + "AlertManagement.aspx";
                    public const string NotificationManager = Directory + "NotificationManager.aspx";
                    public const string AlertManager = Directory + "AlertManager.aspx";
                    public const string CustomExceptionManagement = Directory + "CustomExceptionManagement.aspx";
                }

                public static class Application
                {
                    private const string Directory = "~/Component/";
                    public const string ApplicationConfiguration = Directory + "ApplicationConfig.aspx";
                    public const string ApplicationList = Directory + "ApplicationList.aspx";
                }

                public static class BusinessProcessAuto
                {
                    private const string Directory = "~/Admin/";
                    public const string BusinessProcessList = Directory + "BusinessProcessAuto.aspx";
                }

                public static class BusinessImpactAnalysis
                {
                    private const string Directory = "~/Admin/";
                    public const string BIAList = Directory + "BusinessImpactAnalysisList.aspx";
                    public const string BIAConfiguration = Directory + "BussinessImpactAnalysis.aspx";
                }

                public static class BIAProfiles
                {
                    private const string Directory = "~/Admin/";
                    public const string BIAProfile = Directory + "BIAProfileList.aspx";
                    public const string BIAProfileConfiguration = Directory + "BIAProfileForm.aspx";
                }

                public static class BusinessServices
                {
                    private const string Directory = "~/BusinessServices/";

                    public const string BusinessserviceConfiguration = Directory + "BusinessServiceConfiguration.aspx";
                    public const string BusinessserviceList = Directory + "BusinessServiceList.aspx";
                }

                public static class BusinessFunction
                {
                    private const string Directory = "~/BusinessFunction/";

                    public const string BusinessFunctionConfiguration = Directory + "BusinessFunctionConfig.aspx";
                    public const string BusinessFunctionList = Directory + "BusinessFunctionList.aspx";
                }

                public static class Common
                {
                    private const string Directory = "~/User/";

                    public const string LoginPage = "~/Login.aspx";
                    public const string LogoutPage = "~/Logout.aspx";
                    public const string SuccessPage = Directory + "UserProfileConfirmation.aspx";
                }

                public static class CompanyProfile
                {
                    private const string Directory = "~/CompanyProfile/";
                    public const string ProfileConfiguration = Directory + "CompanyProfileConfiguration.aspx";
                    public const string ProfileList = Directory + "CompanyProfileList.aspx";
                }

                public static class Component
                {
                    private const string Directory = "~/Component/";

                    public const string DatabaseConfiguration = Directory + "DatabaseConfiguration.aspx";
                    public const string DatabaseList = Directory + "DatabaseList.aspx";

                    public const string SereverConfiguration = Directory + "ServerConfiguration.aspx";
                    public const string SereverList = Directory + "ServerList.aspx";

                    public const string ReplicationConfiguration = Directory + "ReplicationConfiguration.aspx";

                    public const string ReplicationList = Directory + "ReplicationList.aspx";
                    public const string ExchangegroupConfiguration = Directory + "ExchangeGroupConfiguration.aspx";

                    public const string ExchangegroupList = Directory + "ExchangeGroupList.aspx";
                    public const string NodesList = Directory + "NodesList.aspx";
                    public const string NodesConfiguration = Directory + "NodesConfiguration.aspx";

                    public const string ExchangeConfiguration = Directory + "Exchange.aspx";

                    public const string ExchangeDAGConfiguration = Directory + "MSExchDAGMonitoring.aspx";

                    public const string ExchangeSummaryConfiguration = Directory + "MSExcDAGSumryMonitrng.aspx";
                    public const string InfrastructureMonitor = Directory + "InfrastructureMonitor.aspx";
                    public const string InfrastructureMonitorList = Directory + "InfrastructureMonitorList.aspx";

                    public const string Datasyncproperties = Directory + "DataSyncPropertiesConfig.aspx";
                    public const string DatasyncpropertiesList = Directory + "DataSyncPropertiesList.aspx";

                    public const string Postgre9xMonitoring = Directory + "Postgre9xMonitoring.aspx";
                    // public const string GLOBALMIRROR_DR_CONFIGURATION = DIRECTORY + "GlobalMirrorDRConfiguration.aspx";

                    public const string SingleSignOnConfiguration = Directory + "SingleSign-OnConfiguration.aspx";
                    public const string SingleSignOnList = Directory + "SingleSignOnList.aspx";
                    public const string HACMPClusterConfiguration = Directory + "HACMPClusterConfiguration.aspx";
                    public const string HACMPClusterConfigurationList = Directory + "HACMPClusterConfigurationList.aspx";
                    public const string RoboCopyOptionsConfiguration = "~/Component/Robocopyoptionsconfig.aspx";
                    public const string RoboCopyOptionsList = "~/Component/Robocopyoptionslist.aspx";
                    public const string RSyncOptionsList = Directory + "Rsyncoptionslist.aspx";
                    public const string RsyncOptionConfig = Directory + "Rsyncoptionconfiguration.aspx";
                    public const string LoadBalancerConfig = Directory + "LoadBalancerconfiguration.aspx";
                    public const string LoadBalancerConfigList = Directory + "LoadBalancerList.aspx";

                   
                }

                public static class Controls
                {
                    private const string Directory = "~/Controls/";
                    public const string GlobalmirrorConfiguration = Directory + "GlobalMirrorConfiguration.ascx";
                    public const string OracleDataguardConfiguration = Directory + "OracleDataguardConfiguration.ascx";
                    public const string FastcopyConfiguration = Directory + "FastCopyConfiguration.ascx";
                    public const string VmwareFastcopyConfiguration = Directory + "VmwareFastCopyConfiguration.ascx";
                    public const string MSSCRConfiguration = Directory + "MSSCRConfiguration.ascx";
                    public const string EmcsdfConfiguration = Directory + "EMCSRDFConfiguration.ascx";
                    public const string HITACHIConfiguration = Directory + "Hitachi(HUR)ReplicationConfig.ascx";
                    public const string SitemenuConfiguration = Directory + "Sitemenu.ascx";

                    //  public const string VMWARE_FASTCOPY_CONFIGURATION = DIRECTORY + "VmwareFastCopyConfiguration.ascx";
                    public const string SnapmirrorConfiguration = Directory + "SnapMirrorConfiguration.ascx";
                    public const string AccessmanagerConfiguration = Directory + "CustomRoleAccessManager.ascx";
                    public const string HyperVConfiguration = Directory + "HyperVConfiguration.ascx";
                    public const string MySqlRepliConfiguration = Directory + "MySqlReplicationConfig.ascx";
                    public const string SVCGlobalMirrorConfiguration = Directory + "SVCGlobalMirrorORMetroMirrorConfiguration.ascx";
                    public const string MssqlDbMirrors = Directory + "MSSqlDBMirrorConfiguration.ascx";

                    public const string IBMxivMirrorConfigurations = Directory + "IBMXIVMirrorConfiguration.ascx";
                    public const string HyperVList = Directory + "HyperVReplicationList.ascx";
                    public const string IBMXIVMirrorLists = Directory + "IBMXIVMirrorOverView.ascx";
                    public const string EmcsrdfSG = Directory + "EMCSRDFSGConfiguration.ascx";
                    public const string EmcsrdfSTAR = Directory + "EMCSRDFStarConfiguration.ascx";
                    public const string SybaseWithSRS = Directory + "SybaseWithSRS.ascx";
                    public const string Base24Configuration = Directory + "Base24ReplicationConfig.ascx";
                    public const string TPRCConfigurationt = Directory + "TPRCConfiguration.ascx";
                    public const string MSSQLAlwaysOn = Directory + "MSSQLAlwaysOnReplicationConfig.ascx";
                    public const string RecoverPoint = Directory + "RecoveryPointConfig.ascx";
                    public const string HP3PARStorage = Directory + "HP3PARStorageMSSqlConfiguration.ascx";
                    public const string VVRReplication = Directory + "VVRMssqlFullDBConfiguration.ascx";
                    public const string HP3PARESXI = Directory + "HP3PARWithESXIConfiguration.ascx";

                    public const string VeritasClusterDetails = Directory + "VeritasClusterDetailedmonitoring.ascx";
                    public const string EmcsrdfSTARMonitor = Directory + "EMCSRDFStarMonitoring.ascx";
                    public const string EmcsrdfSGLogs = Directory + "EMCSRDFSGMonitoring.ascx";
                    public const string HP3PARMongoDB = Directory + "HP3PARmonitorDetails.ascx";
                    public const string HP3PARDetailsMonitor = Directory + "HP3PARmonitorDetails.ascx";
                    public const string MongoDB = Directory + "MongoDBDetailedMonitoring.ascx";

                    public const string ZFSReplicationMonitor = Directory + "ZFSStorageReplicationMonitoring.ascx";
                    public const string ZFSOracleFullDB = Directory + "ZFSApplicationConfiguration.ascx";
                    public const string EmcIsilon = Directory + "EMCISilonConfiguration.ascx";
                    public const string EmcMirrorView = Directory + "EmcMirrorViewConfiguration.ascx";
                    public const string HACMPPowerHACluster = Directory + "HACMPPowerHAClusterDetails.ascx";
                    public const string RoboCopyReplicationConfig = "~/Controls/RoboCopyRepliConfig.ascx";

                    public const string RecoveryPointMulti = Directory + "RecoveryPointMultiMonitoringDetails.ascx";
                    public const string RSyncMonitor = Directory + "RSyncMonitorDetails.ascx";

                    public const string SybaseWithRSHADR = Directory + "SybaseWithRSHADR.ascx";
                    public const string SybaseWithRSHADRMonitoring = Directory + "SybaseWithRSHadrMonitoring.ascx";
                    public const string EmcUnity = Directory + "EmcUnityConfiguration.ascx";
                    public const string EmcUnityMonitor = Directory + "EMCUnityMonitoring.ascx";
                    public const string SAPHANADBMonitor = Directory + "SapHanaMonitoring.ascx";
                    public const string GoldenGate = Directory + "GoldenGateRepliConfiguration.ascx";
                    public const string RSync = Directory + "Rsyncreplicationconf.ascx";

                    public const string VeeamMonitoring = Directory + "VeeamReplicationMonitoringDetails.ascx";
                    public const string VeeamRepli = Directory + "VeeamRepliConfiguration.ascx";

                    public const string _VVRAPPReplication = Directory + "VVRAppFullConfiguration.ascx";
                    public const string CloudantDBConfiguration = Directory + "CloudantDBConfiguration.ascx";
                    public const string HuaweiConfiguration = Directory + "HuaweiConfiguration.ascx"; //HuaweiReplicationASync.ascx
                    public const string HuaweiReplicationASync = Directory + "HuaweiReplicationASync.ascx";
                    public const string HuaweiReplicationSync = Directory + "HuaweiReplicationSync.ascx";
                    public const string _NutanixLeapmonitor = Directory + "NutanixLeapMonitor.ascx";
                    public const string _NutanixLeapRepli = Directory + "NutanixLeapRepliConfiguration.ascx";
                    public const string ActiveDirectory = Directory + "ActiveDirectoryConfig.ascx";
                    public const string NutanixMonitoringDet = Directory + "NutanixMonitor.ascx";
                    public const string Nutanix = Directory + "NutanixRepliConfiguration.ascx";
                    public const string _MariaDBMonitor = Directory + "MariaDBGaleraClusterMonitor.ascx";
                    public const string AzureSiteRecovery = Directory + "AzureSiteDetailsMonitoring.ascx";
                    public const string RecoveryAzureSite = Directory + "RecoveryAzureSiteConfig.ascx";
                    public const string ZertoSiteMonitor = Directory + "ZertoSiteMonitoring.ascx";
                    public const string RecoveryZertoSite = Directory + "RecoveryZertoSiteConfig.ascx";
                    public const string EmcsrdfCG = Directory + "EMCSRDFCGConfiguration.ascx";
                    public const string VmwareVsphere = Directory + "VmwareVsphereRepliConfig.ascx";
                    public const string VmwareVsphereMonitoring = Directory + "VmwareVsphereMonitoringSummery.ascx";
                }

                public static class ImpactAnalysis
                {
                    public const string EntityImpactConfiguration = "../ImpactAnalysis/ImpactConfiguration.aspx";
                    public const string EntityImpactList = "~/ImpactAnalysis/EntityImpactRelationshipForm.aspx";
                    public const string ImpactCategoryConfiguration = "../ImpactAnalysis/ImpactTypeMaster.aspx";
                    public const string ImpactMasterConfiguration = "../ImpactAnalysis/ImpactMaster.aspx";
                    public const string ImpactCategoryList = "../ImpactAnalysis/ImpactTypeMasterList.aspx";
                    public const string ImpactMasterList = "../ImpactAnalysis/ImpactMasterList.aspx";
                    public const string BusinessFunctionBIA = "../ImpactAnalysis/BussinessFunctionBIADynamic.aspx";
                    public const string ImpactSummary = "../ImpactAnalysis/ImpactSummary.aspx";
                    public const string InidentWhatIf = "../ImpactAnalysis/WhatIfAnalysis.aspx";
                    public const string DashboardIncWhatIfAnalysis = "../ImpactAnalysis/DashboardIncWhatIfAnalysis.aspx";
                    public const string DashboardImpactSummary = "../ImpactAnalysis/DashboardImpactSummary.aspx";
                    public const string ConfigureBIARule = "../ImpactAnalysis/ConfigureBIARule.aspx";  


                }
                public static class Error
                {
                    private const string Directory = "~/Error/";
                    public const string Error400 = Directory + "BadRequest.aspx";
                    public const string Error401 = Directory + "Unauthorized.aspx";
                    public const string Error403 = Directory + "Forbidden.aspx";
                    public const string Error404 = Directory + "PageNotFound.aspx";
                    public const string Error500 = Directory + "InternalServerError.aspx";
                    public const string Error503 = Directory + "ServiceUnavailable.aspx";
                    public const string XssAttack = Directory + "XSSAttack.aspx";
                    public const string ErrorServer = Directory + "SystemError.aspx";
                }

                public static class Group
                {
                    private const string Directory = "~/Group/";

                    public const string DbGroupConfig = Directory + "GroupConfiguration.aspx";

                    public const string AppGroupConfig = Directory + "ApplicaionGroupConfiguration.aspx";

                    public const string InfraObjectsConfig = Directory + "InfraObjectsConfiguration.aspx";

                    public const string DbGroupList = Directory + "GroupList.aspx";

                    public const string AppGroupList = Directory + "ApplicationGroupList.aspx";

                    public const string InfraObjectsList = Directory + "InfraObjectsList.aspx";
                }

                public static class Node
                {
                    private const string Directory = "~/Component/";
                    public const string NodeConfiguration = Directory + "NodesConfiguration.aspx";
                    public const string NodeList = Directory + "NodesList.aspx";
                }

                public static class Reports
                {
                    private const string Directory = "~/Report/";
                    public const string ReportManagement = Directory + "ReportManagement.aspx";
                    public const string NotificationManager = Directory + "NotificationManager.aspx";
                    public const string ReportScheduler = Directory + "ReportScheduler.aspx";
                }

                public static class Site
                {
                    private const string Directory = "~/Site/";
                    public const string SiteConfiguration = Directory + "SiteConfiguration.aspx";
                    public const string SiteList = Directory + "SiteList.aspx";
                }

                public static class User
                {
                    private const string Directory = "~/User/";
                    public const string UserConfiguration = Directory + "UserConfiguration.aspx";
                    public const string UserList = Directory + "UserList.aspx";
                }

                public static class Workflow
                {
                    private const string Directory = "~/Workflow/";
                    public const string WorkflowConfiguration = Directory + "WorkflowConfiguration.aspx";
                }

                public static class VeritasClusters
                {
                    private const string Directory = "~/Component/";
                    public const string VeritasClusterConfiguration = Directory + "VeritasClusterConfiguration.aspx";
                    public const string VeritasClusterList = Directory + "VeritasClusterList.aspx";
                }
            }
        }
    }
}