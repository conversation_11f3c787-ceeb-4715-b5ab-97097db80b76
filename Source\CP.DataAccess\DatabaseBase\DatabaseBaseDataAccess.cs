﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class DatabaseBaseDataAccess : BaseDataAccess, IDatabaseBaseDataAccess
    {
        #region Constructors

        public DatabaseBaseDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<DatabaseBase> CreateEntityBuilder<DatabaseBase>()
        {
            return (new DatabaseBaseBuilder()) as IEntityBuilder<DatabaseBase>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="DatabaseBase" /> bcms_Database_base table.
        /// </summary>
        /// <param name="database">DatabaseBase</param>
        /// <returns>Database</returns>
        /// <author><PERSON></author>
        DatabaseBase IDatabaseBaseDataAccess.Add(DatabaseBase database)
        {
            try
            {
                const string sp = "DatabaseBase_Create";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    string type = string.Empty;
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, database.Name);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseType", DbType.AnsiString, Convert.ToString(database.DatabaseType));
                    Database.AddInParameter(cmd, Dbstring + "iVersion", DbType.AnsiString, database.Version);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, database.Type);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, database.ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iIsracdbnode", DbType.Int32, database.Racdbnode);
                    Database.AddInParameter(cmd, Dbstring + "iIsPartOfRac", DbType.Int32, database.IsPartofRac);
                    Database.AddInParameter(cmd, Dbstring + "iMode", DbType.AnsiString, database.Mode);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, database.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseConnectivity", DbType.AnsiString, database.DatabaseConnectivity);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        database = reader.Read()
                            ? CreateEntityBuilder<DatabaseBase>().BuildEntity(reader, database)
                            : null;
                    }

                    if (database == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "Database already exists. Please specify another database.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this database.");
                                }
                        }
                    }
                    return database;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting Base Database Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Update <see cref="DatabaseBase" /> into bcms_Database_base table.
        /// </summary>
        /// <param name="database">DatabaseBase</param>
        /// <returns>Database</returns>
        /// <author>Kiran Ghadge</author>
        DatabaseBase IDatabaseBaseDataAccess.Update(DatabaseBase database)
        {
            try
            {
                const string sp = "DATABASEBASE_UPDATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, database.Id);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, database.Name);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseType", DbType.AnsiString, Convert.ToString(database.DatabaseType));
                    Database.AddInParameter(cmd, Dbstring + "iVersion", DbType.AnsiString, database.Version);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, database.Type);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, database.ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iIsracdbnode", DbType.Int32, database.Racdbnode);
                    Database.AddInParameter(cmd, Dbstring + "iIsPartOfRac", DbType.Int32, database.IsPartofRac);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, database.UpdatorId);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseConnectivity", DbType.String, database.DatabaseConnectivity);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        database = reader.Read()
                            ? CreateEntityBuilder<DatabaseBase>().BuildEntity(reader, database)
                            : null;
                    }

                    if (database == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "Database already exists. Please specify another database.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this database.");
                                }
                        }
                    }

                    return database;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Base Database Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DatabaseBase" /> from bcms_Database_base table by Id.
        /// </summary>
        /// <param name="id">Id of the DatabaseBase</param>
        /// <returns>Database</returns>
        /// <author>Kiran Ghadge</author>
        DatabaseBase IDatabaseBaseDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DatabaseBase_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<DatabaseBase>()).BuildEntity(reader, new DatabaseBase());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        DatabaseBase IDatabaseBaseDataAccess.GetDatabaseNameById(DatabaseType databaseType, int id)
        {
            try
            {
                if (id < 1 && databaseType == null)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DATABASENAME_GETBYTYPE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, databaseType.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<DatabaseBase>()).BuildEntity(reader, new DatabaseBase());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetDatabaseNameById(" + databaseType + "," + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DatabaseBase" /> from bcms_Database_base table.
        /// </summary>
        /// <param name=" "></param>
        /// <returns>Database List</returns>
        /// <author>Kiran Ghadge</author>
        IList<DatabaseBase> IDatabaseBaseDataAccess.GetAll()
        {
            try
            {
                const string sp = "DatabaseBase_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DatabaseBase>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DatabaseBase" /> from bcms_Database_base table NotConfigured Database.
        /// </summary>
        /// <param name=" "></param>
        /// <returns>Database List</returns>
        /// <author>Kiran Ghadge</author>
        IList<DatabaseBase> IDatabaseBaseDataAccess.GetAllNotConfigured()
        {
            const string sp = "DatabaseBase_GetAllBaseDatabaseNotConfigured";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                using (IDataReader reader = Database.ExecuteReader(cmd))
                {
                    return CreateEntityBuilder<DatabaseBase>().BuildEntities(reader);
                }
            }
        }

        IList<DatabaseBase> IDatabaseBaseDataAccess.GetByType(DatabaseType type)
        {
            try
            {
                const string sp = "DatabaseBase_GetByType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, type.ToString());
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        switch (type)
                        {
                            case DatabaseType.PostgreSQL:
                                return BuildPostGreSqlDatabaseEntity(reader);

                            case DatabaseType.Oracle:
                                return BuildOracleDatabaseEntity(reader);

                            case DatabaseType.OracleRac:
                                return BuildOracleRacDatabaseEntity(reader);

                            case DatabaseType.Sql:
                                return BuildSqlDatabaseEntity(reader);

                            case DatabaseType.Exchange:
                                return BuildExchangeDatabaseEntity(reader);

                            case DatabaseType.DB2:
                                return BuildDb2DatabaseEntity(reader);

                            case DatabaseType.ExchangeDAG:
                                return BuildExchangeDAGDatabaseEntity(reader);

                            case DatabaseType.MySQL:
                                return BuildMySqlDatabaseEntity(reader);

                            case DatabaseType.Postgres9x:
                                return BuildDatabasePostgre9xDatabaseEntity(reader);

                            case DatabaseType.SQLNative2008:
                                return BuildMSSqlDatabaseEntity(reader);

                            case DatabaseType.SyBase:
                                return BuildSybaseDatabaseEntity(reader);

                            case DatabaseType.MaxDB:
                                return BuildMaxDBDatabaseEntity(reader);

                            case DatabaseType.SyBaseWithSrs:
                                return BuildSybaseWithSrsDatabaseEntity(reader);

                            case DatabaseType.MongoDB:
                                return BuildMongoDBDatabaseEntity(reader);

                            case DatabaseType.SyBaseWithRsHADR:
                                return BuildSyBaseWithRsHADRDatabaseEntity(reader);

                            case DatabaseType.HANADB:
                                return BuildHANADBDatabaseEntity(reader);

                            case DatabaseType.CloudantDB:
                                return BuildCloudantNoSQlDatabaseEntity(reader);

                            case DatabaseType.MSSQLFullDB:
                                return BuildMssqlFullDBEntity(reader);

                            case DatabaseType.MariaDB:
                                return BuildMariaDatabaseEntity(reader);
                            //ITIT-7572
                            case DatabaseType.RedisCLIMode:
                                return BuildRedisCLIModeDatabaseEntity(reader);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetByType(" + type + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
            return null;
        }

        IList<DatabaseBase> IDatabaseBaseDataAccess.GetByTypeAndCompanyId(DatabaseType type, int companyId,
            bool isParent)
        {
            try
            {
                const string sp = "DatabaseBase_GtByTypnCompId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, type.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.Int32, companyId);
                    Database.AddInParameter(cmd, Dbstring + "iIsParent", DbType.Int32, isParent);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        switch (type)
                        {
                            case DatabaseType.Oracle:

                                return BuildOracleDatabaseEntity(reader);

                            case DatabaseType.OracleRac:

                                return BuildOracleRacDatabaseEntity(reader);

                            case DatabaseType.Sql:

                                return BuildSqlDatabaseEntity(reader);

                            case DatabaseType.Exchange:

                                return BuildExchangeDatabaseEntity(reader);

                            case DatabaseType.DB2:
                                return BuildDb2DatabaseEntity(reader);

                            case DatabaseType.ExchangeDAG:
                                return BuildExchangeDAGDatabaseEntity(reader);

                            case DatabaseType.MySQL:
                                return BuildMySqlDatabaseEntity(reader);

                            case DatabaseType.SQLNative2008:
                                return BuildMSSqlDatabaseEntity(reader);

                            case DatabaseType.MaxDB:
                                return BuildMaxDBDatabaseEntity(reader);

                            case DatabaseType.MongoDB:
                                return BuildMongoDBDatabaseEntity(reader);

                            case DatabaseType.SyBaseWithRsHADR:
                                return BuildSyBaseWithRsHADRDatabaseEntity(reader);

                            case DatabaseType.CloudantDB:
                                return BuildCloudantNoSQlDatabaseEntity(reader);

                            case DatabaseType.MSSQLFullDB:
                                return BuildMssqlFullDBEntity(reader);

                            case DatabaseType.MariaDB:
                                return BuildMariaDatabaseEntity(reader);
                            //ITIT-7572    
                            case DatabaseType.RedisCLIMode:
                                return BuildRedisCLIModeDatabaseEntity(reader);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetByTypeAndCompanyId(" +
                    companyId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
            return null;
        }

        /// <summary>
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="isParent"></param>
        /// <returns></returns>
        IList<DatabaseBase> IDatabaseBaseDataAccess.GetAllDatabaseByUserCompanyId(int companyId, bool isParent)
        {
            try
            {
                const string sp = "DatabaseBase_GetByUsrCompId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.Int32, companyId);

                    Database.AddInParameter(cmd, Dbstring + "iIsParent", DbType.Int32, isParent);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DatabaseBase>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseDataAccess.GetDatabaseByUserCompanyId(" +
                    companyId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="DatabaseBase" /> bcms_Database_base table by Id.
        /// </summary>
        /// <param name="id">Id of the Database</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IDatabaseBaseDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "DatabaseBase_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting DatabaseBase Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Check <see cref="DatabaseBase" /> Database name from bcms_Database_base table by name.
        /// </summary>
        /// <param name="name">Name of the Database</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IDatabaseBaseDataAccess.IsExistByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "DatabaseBase_IsExistByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this companyProfile.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.IsExistByName (" + name +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        bool IDatabaseBaseDataAccess.UpdateByMode(int id, string mode)
        {
            try
            {
                if (mode == string.Empty)
                {
                    throw new ArgumentNullException("mode");
                }

                const string sp = "DatabaseBase_UpdateByStatus";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    Database.AddInParameter(cmd, Dbstring + "iMode", DbType.AnsiString, mode);

                    int success = Database.ExecuteNonQuery(cmd);

                    return success > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.UpdateByStatus" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<DatabaseBase> IDatabaseBaseDataAccess.GetByServerId(int serverId)
        {
            try
            {
                const string sp = "DatabaseBase_GetByServerId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, serverId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DatabaseBase>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetByServerId(" + serverId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        IList<DatabaseBase> IDatabaseBaseDataAccess.GetByServerId_NW(int serverId)
        {
            try
            {
                const string sp = "DATABASEBASE_GETBYSERVERID_NW";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, serverId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DatabaseBase>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetByServerId_NW(" + serverId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        IList<DatabaseBase> IDatabaseBaseDataAccess.GetByServerIdAndType(int serverId, string type)
        {
            try
            {
                const string sp = "DatabaseBase_GtBySrvrIdAndType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, serverId);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, type);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        var databases = new List<DatabaseBase>();
                        var dblist = CreateEntityBuilder<DatabaseBase>().BuildEntities(reader);
                        if (dblist != null)
                        {
                            if (dblist.Count > 0)
                            {
                                foreach (var dbItem in dblist)
                                {
                                    if (dbItem.Type.ToLower().Contains(type.ToLower()))
                                        databases.Add(dbItem);
                                }
                            }
                        }
                        return databases;
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetByServerIdAndType(" + serverId + type +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        /// Get DatabaseBaseDataAccess details
        /// </summary>
        /// <param name="infraObjectId">infraObjectId</param>
        /// <returns>List of DatabaseBase</returns>
        /// <author>Ram Mahajan</author>
        IList<DatabaseBase> IDatabaseBaseDataAccess.GetByInfraObjectId(int infraObjectId)
        {
            try
            {
                const string sp = "DATABASEBASE_BYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DatabaseBase>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetByInfraObjectId(" + infraObjectId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        DatabaseBase IDatabaseBaseDataAccess.GetByInfraObjectIdPRServerId(int groupId)
        {
            try
            {
                if (groupId < 1)
                {
                    throw new ArgumentNullException("groupId");
                }

                const string sp = "DatabaseBas_GtByInfrIdPRDtbsId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, groupId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<DatabaseBase>()).BuildEntity(reader, new DatabaseBase());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetByGroupId(" + groupId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        DatabaseBase IDatabaseBaseDataAccess.GetByGroupIdDrServerId(int groupId)
        {
            try
            {
                if (groupId < 1)
                {
                    throw new ArgumentNullException("groupId");
                }

                const string sp = "DatabaseBas_GtByInfrIdDRDtbsId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, groupId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<DatabaseBase>()).BuildEntity(reader, new DatabaseBase());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetByGroupIdDrServerId(" +
                    groupId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        private IList<DatabaseBase> BuildExchangeDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabaseExchange =
                    {
                        StorageGroupName = Convert.IsDBNull(reader["StorageGroupName"])
                ? string.Empty
                : Convert.ToString(reader["StorageGroupName"]),
                        MailBoxDBName = Convert.IsDBNull(reader["MailBoxDBName"])
                ? string.Empty
                : Convert.ToString(reader["MailBoxDBName"])
                    }
                };
                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildExchangeDAGDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabaseExcahngeDAG =
                    {
                        MailBoxDBName = Convert.IsDBNull(reader["MailBoxDBName"])
                            ? string.Empty
                            : Convert.ToString(reader["MailBoxDBName"])
                    }
                };
                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildSqlDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabaseSql =
                    {
                        DatabaseSID = Convert.IsDBNull(reader["DatabaseSID"])
                ? string.Empty
                : Convert.ToString(reader["DatabaseSID"]),
                        AuthenticationMode = Convert.IsDBNull(reader["AuthenticationMode"])
                ? SqlAuthenticateType.Undefined
                : (SqlAuthenticateType)
                    Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(reader["AuthenticationMode"]), true),
                        InstanceName = Convert.IsDBNull(reader["InstanceName"]) ? string.Empty : Convert.ToString(reader["InstanceName"]),
                        UserName = Convert.IsDBNull(reader["UserName"])
                ? string.Empty
                : Convert.ToString(reader["UserName"]),
                        Password = Convert.IsDBNull(reader["Password"])
                ? string.Empty
                : Convert.ToString(reader["Password"])
                    }
                };
                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildOracleDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabaseOracle =
                    {
                        OracleSID = Convert.IsDBNull(reader["OracleSID"])
                ? string.Empty
                : Convert.ToString(reader["OracleSID"]),
                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"])
                    }
                };
                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildOracleRacDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                };

                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildDb2DatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabaseDb2 =
                    {
                        DatabaseSID = Convert.IsDBNull(reader["DatabaseSID"])
                ? string.Empty
                : Convert.ToString(reader["DatabaseSID"]),
                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"])
                    }
                };

                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildMySqlDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabaseMySql =
                    {
                        DatabaseID = Convert.IsDBNull(reader["DatabaseID"]) ? string.Empty : Convert.ToString(reader["DatabaseID"]),

                        UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]),
                        Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]),

                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"])
                    }
                };

                //database.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                //database.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                ////database.DatabaseType = Convert.IsDBNull(reader["DatabaseType"]) ? DatabaseType.Undefined : (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(reader["DatabaseType"]), true);
                //database.Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]);
                //database.Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
                //database.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                //database.Mode = Convert.IsDBNull(reader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true);
                //database.DatabaseMySql.DatabaseID = Convert.IsDBNull(reader["DatabaseID"]) ? string.Empty : Convert.ToString(reader["DatabaseID"]);
                //database.DatabaseMySql.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);

                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildCloudantNoSQlDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabaseCloudantNoSQL =
                    {
                        DBName = Convert.IsDBNull(reader["DBName"]) ? string.Empty : Convert.ToString(reader["DBName"]),
                        DBPath = Convert.IsDBNull(reader["DBPath"]) ? string.Empty : Convert.ToString(reader["DBPath"]),
                        LoadBalancerNodeURL = Convert.IsDBNull(reader["LoadBalancerNodeURL"]) ? string.Empty : Convert.ToString(reader["LoadBalancerNodeURL"]),
                        UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]),
                        Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"])
                    }
                };

                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildMssqlFullDBEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),

                    Mode = Convert.IsDBNull(reader["ModeType"])
              ? DatabaseMode.Undefined
              : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabaseMssqlfulldb =
                    {
                        // DatabaseID = Convert.IsDBNull(reader["DatabaseID"]) ? string.Empty : Convert.ToString(reader["DatabaseID"]),
                        AuthenticationMode = Convert.IsDBNull(reader["AuthenticationMode"])
               ? SqlAuthenticateType.Undefined
               : (SqlAuthenticateType)
                   Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(reader["AuthenticationMode"]), true),
                        //DbMethod = Convert.IsDBNull(reader["DbMethod"]) ? 0 : Convert.ToInt32(reader["DbMethod"]),
                        //SqlServerType = Convert.IsDBNull(reader["SqlServerType"]) ? string.Empty : Convert.ToString(reader["SqlServerType"]),
                        //IPAddress = Convert.IsDBNull(reader["IPAddress"]) ? string.Empty : Convert.ToString("IPAddress"),
                        //InstanceName = Convert.IsDBNull(reader["InstanceName"]) ? string.Empty : Convert.ToString("InstanceName"),
                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]),
                        //  UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]),
                        //ExcludeDatabaseList = Convert.IsDBNull(reader["ExcludeDatabaseList"]) ? string.Empty : Convert.ToString(reader["ExcludeDatabaseList"]),
                    }
                };
                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildMariaDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    MariaDB =
                    {
                        DatabaseName = Convert.IsDBNull(reader["DatabaseName"]) ? string.Empty : Convert.ToString(reader["DatabaseName"]),

                        UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]),
                        Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]),

                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"])
                    }
                };
                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildPostGreSqlDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    PostgreSql =
                    {
                        DatabaseName = Convert.IsDBNull(reader["DatabaseName"]) ? string.Empty : Convert.ToString(reader["DatabaseName"]),

                        UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]),
                        Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]),

                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]),
                        ArchiveLog = Convert.IsDBNull(reader["Archive"]) ? string.Empty : Convert.ToString(reader["Archive"])
                    }
                };

                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildDatabasePostgre9xDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabasePostgre9x =
                    {
                        DatabaseName = Convert.IsDBNull(reader["DatabaseName"]) ? string.Empty : Convert.ToString(reader["DatabaseName"]),

                        UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]),
                        Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]),

                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]),
                        DBDataDirectory = Convert.IsDBNull(reader["DBDataDirectory"]) ? string.Empty : Convert.ToString(reader["DBDataDirectory"]),
                        DBbinDirectory = Convert.IsDBNull(reader["DBbinDirectory"]) ? string.Empty : Convert.ToString(reader["DBbinDirectory"]),
                        SULogin = Convert.IsDBNull(reader["SULogin"]) ? string.Empty : Convert.ToString(reader["SULogin"]),
                        ServiceName = Convert.IsDBNull(reader["ServiceName"]) ? string.Empty : Convert.ToString(reader["ServiceName"])
                    }
                };

                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildMSSqlDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    Databasemssql =
                    {
                        //DatabaseID = Convert.IsDBNull(reader["DatabaseID"]) ? string.Empty : Convert.ToString(reader["DatabaseID"]),
                        DatabaseName = Convert.IsDBNull(reader["DatabaseName"]) ? string.Empty : Convert.ToString(reader["DatabaseName"]),
                        UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]),
                        Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]),
                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"])
                    }
                };
                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                };
                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }


        private IList<DatabaseBase> BuildSybaseDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    Databasesybase =
                    {
                        DatabaseSID = Convert.IsDBNull(reader["DatabaseSID"]) ? string.Empty : Convert.ToString(reader["DatabaseSID"]),
                        UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]),
                        Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]),
                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]),
                        TransactionFileLocation = Convert.IsDBNull(reader["TransactionFileLocation"]) ? string.Empty : Convert.ToString(reader["TransactionFileLocation"]),
                        //DRTransactionFileLocation = Convert.IsDBNull(reader["DRTransactionFileLocation"]) ? string.Empty : Convert.ToString(reader["DRTransactionFileLocation"]),
                        SybaseDataServerName = Convert.IsDBNull(reader["SybaseDataServerName"]) ? string.Empty : Convert.ToString(reader["SybaseDataServerName"]),
                        SybaseBackupServer = Convert.IsDBNull(reader["SybaseBackupServer"]) ? string.Empty : Convert.ToString(reader["SybaseBackupServer"]),
                        IsStandByaccess = Convert.IsDBNull(reader["IsStandByaccess"]) ? 0 : Convert.ToInt32(reader["IsStandByaccess"])
                    }
                };
                databaseBase.Add(database);
            }
            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildPostgres9xDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    //Name = Convert.IsDBNull(reader["DatabaseName"]) ? string.Empty : Convert.ToString(reader["DatabaseName"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabasePostgre9x =
                    {
                        DatabaseName = Convert.IsDBNull(reader["DatabaseName"]) ? string.Empty : Convert.ToString(reader["DatabaseName"]),
                        DBDataDirectory = Convert.IsDBNull(reader["DBDataDirectory"]) ? string.Empty : Convert.ToString(reader["DBDataDirectory"]),
                        DBbinDirectory = Convert.IsDBNull(reader["DBBinDirectory"]) ? string.Empty : Convert.ToString(reader["DBBinDirectory"]),
                        SULogin = Convert.IsDBNull(reader["SULogin"]) ? string.Empty : Convert.ToString(reader["SULogin"]),
                        ServiceName = Convert.IsDBNull(reader["ServiceName"]) ? string.Empty : Convert.ToString(reader["ServiceName"])
                       
                    }
                };
                databaseBase.Add(database);
            }
            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        /// <summary>
        ///     Fetch <see cref="DatabaseBase" /> bcms_Database_base table.
        /// </summary>
        /// <param name="IDataReader">IDataReader</param>
        /// <returns>DatabaseBase</returns>
        /// <author>Uma Mehavarnan</author>
        private IList<DatabaseBase> BuildMaxDBDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabasemaxDB =
                    {
                        DatabaseSID = Convert.IsDBNull(reader["DatabaseSID"]) ? string.Empty : Convert.ToString(reader["DatabaseSID"]),
                        UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]),
                        Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]),
                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]),
                        InstanceName = Convert.IsDBNull(reader["InstanceName"]) ? string.Empty : Convert.ToString(reader["InstanceName"]),
                        InstallationPath = Convert.IsDBNull(reader["InstallationPath"]) ? string.Empty : Convert.ToString(reader["InstallationPath"]),
                        MediumName = Convert.IsDBNull(reader["MediumName"]) ? string.Empty : Convert.ToString(reader["MediumName"]),
                        LogfileName = Convert.IsDBNull(reader["LogfileName"]) ? string.Empty : Convert.ToString(reader["LogfileName"]),
                        LogPath = Convert.IsDBNull(reader["LogPath"]) ? string.Empty : Convert.ToString(reader["LogPath"])
                    }
                };
                databaseBase.Add(database);
            }
            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        //Added By Sumit Wakade
        private IList<DatabaseBase> BuildSybaseWithSrsDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabasesybaseWithSrs =
                    {
                        DatabaseSID = Convert.IsDBNull(reader["DatabaseSID"]) ? string.Empty : Convert.ToString(reader["DatabaseSID"]),
                        UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]),
                        Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]),
                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]),
                        SybaseDataServerName = Convert.IsDBNull(reader["SybaseDataServerName"]) ? string.Empty : Convert.ToString(reader["SybaseDataServerName"]),
                        SybaseBackupServer = Convert.IsDBNull(reader["SybaseBackupServer"]) ? string.Empty : Convert.ToString(reader["SybaseBackupServer"]),
                        SybaseEnv_Path = Convert.IsDBNull(reader["SybaseEnv_Path"]) ? string.Empty : Convert.ToString(reader["SybaseEnv_Path"]),
                    }
                };
                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;


        }

        IList<DatabaseBase> IDatabaseBaseDataAccess.GetByDBType(string dbtype, string type)
        {
            try
            {
                const string sp = "DatabaseBase_GetByDBType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //Database.AddInParameter(cmd, Dbstring + "iDBType", DbType.Int32, dbtype);
                    Database.AddInParameter(cmd, Dbstring + "iDBType", DbType.AnsiString, dbtype);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, type);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DatabaseBase>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetByDBType(" + dbtype +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        DatabaseBase IDatabaseBaseDataAccess.GetDBNameById(DatabaseType databaseType, int id)
        {
            try
            {
                if (id < 1 && databaseType == null)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DBNAME_GETBYTYPE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, databaseType.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<DatabaseBase>()).BuildEntity(reader, new DatabaseBase());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetDBNameById(" + databaseType + "," + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
        IList<DatabaseBase> IDatabaseBaseDataAccess.GetDatabaseBasetypecompanyiduserid(DatabaseType type, int companyid, int userid, string role)
        {
            try
            {
                const string sp = "DATABASEBASE_LISTUSERINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, type.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.Int32, companyid);
                    Database.AddInParameter(cmd, Dbstring + "iuserid", DbType.Int32, userid);
                    Database.AddInParameter(cmd, Dbstring + "irole", DbType.AnsiString, role.ToString());


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        switch (type)
                        {

                            case DatabaseType.Undefined:
                                return BuildDatabaseEntity(reader);

                            case DatabaseType.Oracle:

                                return BuildOracleDatabaseEntity(reader);

                            case DatabaseType.OracleRac:

                                return BuildOracleRacDatabaseEntity(reader);

                            case DatabaseType.Sql:

                                return BuildSqlDatabaseEntity(reader);

                            case DatabaseType.Exchange:

                                return BuildExchangeDatabaseEntity(reader);

                            case DatabaseType.DB2:
                                return BuildDb2DatabaseEntity(reader);

                            case DatabaseType.ExchangeDAG:
                                return BuildExchangeDAGDatabaseEntity(reader);

                            case DatabaseType.MySQL:
                                return BuildMySqlDatabaseEntity(reader);

                            case DatabaseType.SQLNative2008:
                                return BuildMSSqlDatabaseEntity(reader);

                            case DatabaseType.SyBase:
                                return BuildSybaseDatabaseEntity(reader);

                            case DatabaseType.Postgres9x:
                                return BuildPostgres9xDatabaseEntity(reader);

                            case DatabaseType.MongoDB:
                                return BuildMongoDBDatabaseEntity(reader);

                            case DatabaseType.MSSQLFullDB:
                                return BuildMssqlFullDBEntity(reader);

                            case DatabaseType.MariaDB:
                                return BuildMariaDatabaseEntity(reader);
                                //ITIT-7572
                            case DatabaseType.RedisCLIMode:
                                return BuildRedisCLIModeDatabaseEntity(reader);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetDatabaseBasetypecompanyiduserid(" +
                    userid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
            return null;
        }

        IList<DatabaseBase> IDatabaseBaseDataAccess.GetDatabaseBaseUserInfraId(int userid)
        {
            try
            {
                if (userid < 1)
                {
                    throw new ArgumentNullException("userid");
                }
                const string sp = "DATABASE_USERINFRAID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //Database.AddInParameter(cmd, Dbstring+"iModules", DbType.AnsiString, modules.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iuserid", DbType.Int32, userid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        var serlogs = new List<DatabaseBase>();

                        while (reader.Read())
                        {
                            var Logs = new DatabaseBase
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                                DBServerTypeCount = Convert.IsDBNull(reader["DBServerTypeCount"]) ? 0 : Convert.ToInt32(reader["DBServerTypeCount"]),
                                DatabaseType = Convert.IsDBNull(reader["DatabaseType"])
                               ? DatabaseType.Undefined
                               : (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(reader["DatabaseType"]), true)
                            };

                            serlogs.Add(Logs);

                        }
                        return serlogs;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetDatabaseBaseUserInfraId(" + userid +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        private IList<DatabaseBase> BuildMongoDBDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),

                    mongodb =
                    {
                        //     Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                        //BaseDatabaseId = Convert.IsDBNull(reader["BaseDatabaseId"])
                        //     ? 0
                        //     : Convert.ToInt32(reader["BaseDatabaseId"]),
                        InstallationPath = Convert.IsDBNull(reader["InstallationPath"])
                             ? string.Empty
                             : Convert.ToString(reader["InstallationPath"]),
                        InstanceName = Convert.IsDBNull(reader["InstanceName"])
                       ? string.Empty
                       : Convert.ToString(reader["InstanceName"]),

                        // UserName = Convert.IsDBNull(reader["UserName"])
                        //      ? string.Empty
                        //      : Convert.ToString(reader["UserName"]),
                        //Password = Convert.IsDBNull(reader["Password"])
                        //      ? string.Empty
                        //      : Convert.ToString(reader["Password"]),
                        Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]),
                        BinaryLocation = Convert.IsDBNull(reader["BinaryLocation"])
                       ? string.Empty
                       : Convert.ToString(reader["BinaryLocation"]),

                    }
                };
                databaseBase.Add(database);
            }
            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildSyBaseWithRsHADRDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),

                    DatabaseSybaseWithRSHADR =
                    {
                        //     Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                        //BaseDatabaseId = Convert.IsDBNull(reader["BaseDatabaseId"])
                        //     ? 0
                        //     : Convert.ToInt32(reader["BaseDatabaseId"]),                     

                        // UserName = Convert.IsDBNull(reader["UserName"])
                        //      ? string.Empty
                        //      : Convert.ToString(reader["UserName"]),
                        //Password = Convert.IsDBNull(reader["Password"])
                        //      ? string.Empty
                        //      : Convert.ToString(reader["Password"]),
                        DataBaseSID = Convert.IsDBNull(reader["DataBaseSID"])
                    ? string.Empty
                    : Convert.ToString(reader["DataBaseSID"]),
                        ServerWithPort = Convert.IsDBNull(reader["ServerWithPort"])
                     ? string.Empty
                     : Convert.ToString(reader["ServerWithPort"]),
                        //ServerWithPort = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]),

                    }
                };
                databaseBase.Add(database);
            }
            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        //ITIT-7572
        private IList<DatabaseBase> BuildRedisCLIModeDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    //Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    RedisCLIMode =
                    {
                        SourceLocation = Convert.IsDBNull(reader["SourceLocation"]) ? string.Empty : Convert.ToString(reader["SourceLocation"]),
                        PortNumber = Convert.IsDBNull(reader["PortNumber"]) ? string.Empty : Convert.ToString(reader["PortNumber"]),
                    }
                };
                databaseBase.Add(database);
            }
            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        private IList<DatabaseBase> BuildHANADBDatabaseEntity(IDataReader reader)
        {
            var databaseBase = new List<DatabaseBase>();

            while (reader.Read())
            {
                var database = new DatabaseBase
                {
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                    Version = Convert.IsDBNull(reader["Version"]) ? string.Empty : Convert.ToString(reader["Version"]),
                    Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                    ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]),
                    Mode = Convert.IsDBNull(reader["ModeType"])
                ? DatabaseMode.Undefined
                : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(reader["ModeType"]), true),
                    DatabaseHanaDb =
                    {
                        DatabaseSID = Convert.IsDBNull(reader["DatabaseSID"])
                ? string.Empty
                : Convert.ToString(reader["DatabaseSID"])
                        //Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"])
                    }
                };

                databaseBase.Add(database);
            }

            return (databaseBase.Count > 0) ? databaseBase : null;
        }

        DatabaseBase IDatabaseBaseDataAccess.GetByName(string databaseName)
        {
            try
            {
                if (databaseName == string.Empty)
                {
                    throw new ArgumentNullException("databaseName");
                }

                const string sp = "DatabaseBaseGetByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, databaseName);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<DatabaseBase>()).BuildEntity(reader, new DatabaseBase());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetByName(" + databaseName + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }

        }

        IList<DatabaseBase> IDatabaseBaseDataAccess.GetDatabBaseId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "DatabaseBase_GetById";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DatabaseBase>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseBaseDataAccess.GetDatabBaseId(" + id +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        #endregion

    }
}