﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class BusinessServiceDataAccess : BaseDataAccess, IBusinessServiceDataAccess
    {
        #region Constructors

        public BusinessServiceDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<BusinessService> CreateEntityBuilder<BusinessService>()
        {
            return (new BusinessServiceBuilder()) as IEntityBuilder<BusinessService>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="BusinessService" /> into bcms_business_service table.
        /// </summary>
        /// <param name="businessService">businessService</param>
        /// <returns>BusinessService</returns>
        /// <author>Uma Mehavarnan</author>

        BusinessService IBusinessServiceDataAccess.Add(BusinessService businessService)
        {
            try
            {
                const string sp = "BusinessService_Create";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.AnsiString, businessService.Name);
                    Database.AddInParameter(cmd, Dbstring+"iDescription", DbType.AnsiString, businessService.Description);
                    Database.AddInParameter(cmd, Dbstring+"iCompanyId", DbType.Int32, businessService.CompanyId);
                    Database.AddInParameter(cmd, Dbstring+"iSiteId", DbType.Int32, businessService.SiteId);

                    Database.AddInParameter(cmd, Dbstring+"iConfiguredRPO", DbType.AnsiString, businessService.ConfiguredRPO);
                    Database.AddInParameter(cmd, Dbstring+"iConfiguredRTO", DbType.AnsiString, businessService.ConfiguredRTO);
                    Database.AddInParameter(cmd, Dbstring+"iConfiguredMTPOD", DbType.AnsiString, businessService.ConfiguredMAO);

                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, businessService.CreatorId);
                    Database.AddInParameter(cmd, Dbstring+"iPriority", DbType.Int32, businessService.Priority);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        businessService = reader.Read()
                            ? CreateEntityBuilder<BusinessService>().BuildEntity(reader, businessService)
                            : null;
                    }

                    if (businessService == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "BusinessService already exists. Please specify another businessService.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this businessService.");
                                }
                        }
                    }

                    return businessService;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting BusinessService Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Update <see cref="BusinessService" /> into bcms_business_service table.
        /// </summary>
        /// <param name="businessService">businessService</param>
        /// <returns>BusinessService</returns>
        /// <author>Uma Mehavarnan</author>
        BusinessService IBusinessServiceDataAccess.Update(BusinessService businessService)
        {
            try
            {
                const string sp = "BusinessService_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, businessService.Id);
                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.AnsiString, businessService.Name);
                    Database.AddInParameter(cmd, Dbstring+"iDescription", DbType.AnsiString, businessService.Description);
                    Database.AddInParameter(cmd, Dbstring+"iCompanyId", DbType.Int32, businessService.CompanyId);
                    Database.AddInParameter(cmd, Dbstring+"iSiteId", DbType.Int32, businessService.SiteId);

                    Database.AddInParameter(cmd, Dbstring+"iConfiguredRPO", DbType.AnsiString, businessService.ConfiguredRPO);
                    Database.AddInParameter(cmd, Dbstring+"iConfiguredRTO", DbType.AnsiString, businessService.ConfiguredRTO);
                    Database.AddInParameter(cmd, Dbstring+"iConfiguredMTPOD", DbType.AnsiString, businessService.ConfiguredMAO);

                    Database.AddInParameter(cmd, Dbstring+"iUpdatorId", DbType.Int32, businessService.UpdatorId);
                    Database.AddInParameter(cmd, Dbstring+"iPriority", DbType.Int32, businessService.Priority);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        businessService = reader.Read()
                            ? CreateEntityBuilder<BusinessService>().BuildEntity(reader, businessService)
                            : null;
                    }

                    if (businessService == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "BusinessService already exists. Please specify another businessService.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this businessService.");
                                }
                        }
                    }

                    return businessService;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating BusinessService Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="BusinessService" /> from bcms_business_service table by id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>BusinessService</returns>
        /// <author>Uma Mehavarnan</author>
        BusinessService IBusinessServiceDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "BusinessService_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<BusinessService>()).BuildEntity(reader, new BusinessService())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="BusinessService" /> from bcms_business_service table by name.
        /// </summary>
        /// <param name="name">name</param>
        /// <returns>BusinessService</returns>
        /// <author>Uma Mehavarnan</author>
        BusinessService IBusinessServiceDataAccess.GetByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "BusinessService_GetByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.AnsiString, name);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<BusinessService>()).BuildEntity(reader, new BusinessService())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetByName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="BusinessService" /> from bcms_business_service table.
        /// </summary>
        /// <returns>BusinessService List</returns>
        /// <author>Uma Mehavarnan</author>
        IList<BusinessService> IBusinessServiceDataAccess.GetAll()
        {
            try
            {
                const string sp = "BusinessService_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BusinessService>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="BusinessService" /> from bcms_business_service table by infraObjectId.
        /// </summary>
        /// <param name="infraObjectId">infraObjectId</param>
        /// <returns>BusinessService List</returns>
        /// <author>Shivraj Mujumale</author>
        /// <Modified> Kuntesh Thakker </Modified>
        IList<BusinessService> IBusinessServiceDataAccess.GetByInfraObjectId(int infraObjectId)
        {
            try
            {
                const string sp = "BusiServce_GetAllByInfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BusinessService>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetByInfraObjectId(" + infraObjectId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="BusinessService" /> from bcms_business_service table by group companyid and isParent.
        /// </summary>
        /// <param name="companyId">companyId</param>
        /// <param name="isParent">isParent</param>
        /// <returns>BusinessService List</returns>
        /// <author>Uma Mehavarnan</author>
        IList<BusinessService> IBusinessServiceDataAccess.GetByCompanyId(int companyId, bool isParent)
        {
            try
            {
                const string sp = "BusinessService_GetByCompanyId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iCompanyId", DbType.Int32, companyId);

                    Database.AddInParameter(cmd, Dbstring+"iIsParent", DbType.Int32, isParent);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BusinessService>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetByCompanyId(" +
                    companyId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="BusinessService" /> from bcms_business_service table by storageImageId.
        /// </summary>
        /// <param name="storageImageId">storageImageId</param>
        /// <returns>BusinessService List</returns>
        /// <author>Uma Mehavarnan</author>
        BusinessService IBusinessServiceDataAccess.GetByStorageImageId(string storageImageId)
        {
            try
            {
                var application = new BusinessService();

                const string sp = "BusiSrvic_GetByStorgeImgId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iStorageImageId", DbType.AnsiString, storageImageId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            //application.Id = Convert.ToInt32(reader[0]);
                            application.Name = reader[0].ToString();
                            //businessServices.Add(application);
                        }
                        return application;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetByStorageImageId(" +
                    storageImageId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="BusinessService" /> from bcms_business_service table by userId.
        /// </summary>
        /// <param name="userId">userId</param>
        /// <returns>BusinessService List</returns>
        /// <author>Uma Mehavarnan</author>
        /// <Modified> Kuntesh Thakker - Added BusinessService_GetByUserId in Database </Modified>
        IList<BusinessService> IBusinessServiceDataAccess.GetByUserId(int userId)
        {
            try
            {
                const string sp = "BusinessService_GetByUserId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iUserId", DbType.Int32, userId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BusinessService>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetByUserId(" + userId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="BusinessService" /> from bcms_business_service table by id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>BusinessService List</returns>
        /// <author>Uma Mehavarnan</author>
        bool IBusinessServiceDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "BusinessService_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    //Database.ExecuteNonQuery(cmd);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                    //int returnCode = GetReturnCodeFromParameter(cmd);

                    //switch (returnCode)
                    //{
                    //    case Constants.MySqlConstants.DB_STATUS_CODE_SUCCESS_DELETE:
                    //        {
                    //            return true;
                    //        }
                    //    case Constants.MySqlConstants.DB_STATUS_CODE_ERROR_CHILD_EXISTS:
                    //        {
                    //            throw new ArgumentException("Cannot delete a businessService which has association.");
                    //        }
                    //    default:
                    //        {
                    //            throw new SystemException("An unexpected error has occurred while deleting this businessService.");
                    //        }
                    //}
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting BusinessService Entry : " + id + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     check <see cref="BusinessService" />bcms_business_service name is Exist.
        /// </summary>
        /// <param name="name">name</param>
        /// <returns>BusinessService List</returns>
        /// <author>Uma Mehavarnan</author>
        bool IBusinessServiceDataAccess.IsExistByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "BusinessService_IsExistByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.AnsiString, name);

                    //#if ORACLE
                    //                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
                    //#endif

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this companyProfile.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.IsExistByName (" + name +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="BusinessService" /> from bcms_business_service table.
        /// </summary>
        /// <returns>BusinessService List</returns>
        /// <author>Ranjith Singh</author>
        IList<BusinessService> IBusinessServiceDataAccess.GetRpt()
        {
            try
            {
                const string sp = "BusinessServiceSummaryReport";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    var bsService = new List<BusinessService>();
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var bs = new BusinessService
                            {
                                Name = Convert.IsDBNull(reader["BSNAME"]) ? string.Empty : Convert.ToString(reader["BSNAME"]),
                                InfraObjectCount = Convert.IsDBNull(reader["InfraObjectCount"]) ? 0 : Convert.ToInt32(reader["InfraObjectCount"]),
                                Up = Convert.IsDBNull(reader["Up"]) ? 0 : Convert.ToInt32(reader["Up"]),
                                Down = Convert.IsDBNull(reader["Down"]) ? 0 : Convert.ToInt32(reader["Down"]),
                                Description = Convert.IsDBNull(reader["Description"]) ? string.Empty : Convert.ToString(reader["Description"])
                            };

                            bsService.Add(bs);
                        }

                        return bsService;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetRpt" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        BusinessService IBusinessServiceDataAccess.GetByDROperationId(int droperationid)
        {
            try
            {
                if (droperationid < 1)
                {
                    throw new ArgumentNullException("droperationid");
                }

                const string sp = "BUSINESSSRICE_GETBYDROPRTIONID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iDroperationId", DbType.Int32, droperationid);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<BusinessService>()).BuildEntity(reader, new BusinessService())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetByDROperationId(" + droperationid + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<BusinessService> IBusinessServiceDataAccess.GetBusinessServiceSubRpt()
        {
            try
            {
                const string sp = "Bservicesummarysubreport";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    var bsService = new List<BusinessService>();
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var bs = new BusinessService
                            {
                                Name = Convert.IsDBNull(reader["NAME"]) ? string.Empty : Convert.ToString(reader["NAME"]),
                                InfraObjectCount = Convert.IsDBNull(reader["InfraObjectCount"]) ? 0 : Convert.ToInt32(reader["InfraObjectCount"]),
                                //Up = Convert.IsDBNull(reader["Up"]) ? 0 : Convert.ToInt32(reader["Up"]),
                                //Down = Convert.IsDBNull(reader["Down"]) ? 0 : Convert.ToInt32(reader["Down"]),
                                Description = Convert.IsDBNull(reader["Description"]) ? string.Empty : Convert.ToString(reader["Description"])
                            };

                            bsService.Add(bs);
                        }

                        return bsService;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetRpt" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<BusinessService> IBusinessServiceDataAccess.GetBusinessServicesByType(int typeid)
        {
            try
            {
                const string sp = "InfraObjectJob_GetByBusinessInfraType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iTypeId", DbType.Int32, typeid);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BusinessService>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetByInfraObjectId(" + typeid +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<BusinessService> IBusinessServiceDataAccess.GetImpactedBusinessServiceList()
        {
            try
            {

                const string sp = "GetImpactedBusinessService";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BusinessService>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetImpactedBusinessServiceList" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<BusinessService> IBusinessServiceDataAccess.GetSitePropertiesByBusinessServiceName(string name)
        {
            IList<BusinessService> businessServiceList = new List<BusinessService>();
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "GetSitePropertiesByServiceName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {


                        while (reader.Read())
                        {
                            var businessService = new BusinessService();
                            businessService.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            businessService.Location = Convert.IsDBNull(reader["Location"]) ? string.Empty : Convert.ToString(reader["Location"]);
                            businessService.CompanyName = Convert.IsDBNull(reader["CompanyName"]) ? string.Empty : Convert.ToString(reader["CompanyName"]);
                            businessService.SiteType = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
                            businessService.Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]);
                            businessServiceList.Add(businessService);
                        }
                    }
                }
                return businessServiceList;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.GetSitePropertiesByBusinessServiceName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }

        }

        #endregion Methods
    }
}