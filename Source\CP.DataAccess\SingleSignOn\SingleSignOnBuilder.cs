﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.DataAccess
{
   internal sealed class SingleSignOnBuilder:IEntityBuilder<SSOConfiguration>
    {
       IList<SSOConfiguration> IEntityBuilder<SSOConfiguration>.BuildEntities(IDataReader reader)
       {
           var singlesignon = new List<SSOConfiguration>();

           while (reader.Read())
           {
               singlesignon.Add(((IEntityBuilder<SSOConfiguration>)this).BuildEntity(reader, new SSOConfiguration()));
           }

           return (singlesignon.Count > 0) ? singlesignon : null;
       }


       SSOConfiguration IEntityBuilder<SSOConfiguration>.BuildEntity(IDataReader reader, SSOConfiguration singlesignon)
       {
           
           singlesignon.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"].ToString());
           singlesignon.SSOTypeId = Convert.IsDBNull(reader["SSOTypeId"]) ? 0 : Convert.ToInt32(reader["SSOTypeId"].ToString());
           singlesignon.UserName = Convert.IsDBNull(reader["HOSTNAME"]) ? string.Empty : Convert.ToString(reader["HOSTNAME"].ToString());
           singlesignon.SiteId = Convert.IsDBNull(reader["SiteId"]) ? 0 : Convert.ToInt32(reader["SiteId"].ToString());          
           singlesignon.IpAddress = Convert.IsDBNull(reader["IPAddress"]) ? string.Empty : Convert.ToString(reader["IPAddress"]);
           singlesignon.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"].ToString());

           singlesignon.ExcecutionPath = Convert.IsDBNull(reader["ExecutionPath"]) ? string.Empty : Convert.ToString(reader["ExecutionPath"]);
           singlesignon.KeyLocation = Convert.IsDBNull(reader["keyLocation"]) ? string.Empty : Convert.ToString(reader["keyLocation"]);
           singlesignon.CredFilePath = Convert.IsDBNull(reader["CredFilePath"]) ? string.Empty : Convert.ToString(reader["CredFilePath"]);
           
           singlesignon.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
           singlesignon.CreateDate = Convert.IsDBNull(reader["CreateDate"])
               ? DateTime.MinValue
               : Convert.ToDateTime(reader["CreateDate"].ToString());
           singlesignon.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
           singlesignon.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
               ? DateTime.MinValue
               : Convert.ToDateTime(reader["UpdateDate"].ToString());
           singlesignon.ConnectionTimeOut = Convert.IsDBNull(reader["ConnectionTimeOut"]) ? 0 : Convert.ToInt32(reader["ConnectionTimeOut"].ToString());
           singlesignon.ProfileName = Convert.IsDBNull(reader["profilename"]) ? string.Empty : Convert.ToString(reader["profilename"].ToString());
           
           singlesignon.ARCOSOnlineUrl = Convert.IsDBNull(reader["ARCOSOnlineUrl"]) ? string.Empty : Convert.ToString(reader["ARCOSOnlineUrl"].ToString());
           singlesignon.ARCOSWebAPIURL = Convert.IsDBNull(reader["ARCOSWebAPIURL"]) ? string.Empty : Convert.ToString(reader["ARCOSWebAPIURL"].ToString());
           singlesignon.SharedKey = Convert.IsDBNull(reader["SharedKey"]) ? string.Empty : Convert.ToString(reader["SharedKey"].ToString());
           singlesignon.ServiceType = Convert.IsDBNull(reader["ServiceType"]) ? string.Empty : Convert.ToString(reader["ServiceType"].ToString());
           singlesignon.DBInstance = Convert.IsDBNull(reader["DBInstance"]) ? string.Empty : Convert.ToString(reader["DBInstance"].ToString());

           return singlesignon;
       }
    }
}
