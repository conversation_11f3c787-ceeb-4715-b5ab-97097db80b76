﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "UserInfraObject", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class UserInfraObject : BaseEntity
    {
        #region Properties

        [DataMember]
        public int UserId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int IsApplication { get; set; }


        #endregion Properties
    }
}