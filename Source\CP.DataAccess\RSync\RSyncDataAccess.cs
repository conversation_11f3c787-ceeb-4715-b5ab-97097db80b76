﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.RSync
{
    internal sealed class RSyncDataAccess : BaseDataAccess, IRSyncDataAccess
    {
        public RSyncDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<RSyncReplication> CreateEntityBuilder<RSyncReplication>()
        {
            return (new RSyncBuilder()) as IEntityBuilder<RSyncReplication>;
        }

        #region Method

        /// <summary>
        ///     Get <see cref="RoboCopy" /> from RoboCopy table.
        /// </summary>
        /// <returns>RoboCopy IList</returns>
        /// <author><PERSON><PERSON><PERSON></author>
        /// 

        IList<RSyncReplication> IRSyncDataAccess.GetAllRSync()
        {
            try
            {
                const string sp = "RSYNCREPLICATION_GETALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<RSyncReplication>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IRSyncDataAccess.GetAllRSync" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        RSyncReplication IRSyncDataAccess.GetRSyncByRepliId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "RSYNCREPLI_GETBYREPLICATIONID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        return reader.Read()
                            ? (CreateEntityBuilder<RSyncReplication>()).BuildEntity(reader, new RSyncReplication())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IRSyncDataAccess.GetRSyncByRepliId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        RSyncReplication IRSyncDataAccess.AddRSync(RSyncReplication rsync)
        {
            try
            {
                const string sp = "RSYNCREPLICATION_CREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iREPLICATIONID", DbType.Int32, rsync.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iRSYNCPATH", DbType.AnsiString, rsync.RSyncPath);
                    Database.AddInParameter(cmd, Dbstring + "iEXECUTEWITHSUDO", DbType.AnsiString, rsync.ExecuteWithSudo);
                    Database.AddInParameter(cmd, Dbstring + "iSCHEDULETIME", DbType.AnsiString, rsync.ScheduleTime);
                    Database.AddInParameter(cmd, Dbstring + "iCREATORID", DbType.Int32, rsync.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, rsync.Password);
                    Database.AddInParameter(cmd, Dbstring + "iOSPlatform", DbType.AnsiString, rsync.OSPlatform);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        rsync = reader.Read() ? CreateEntityBuilder<RSyncReplication>().BuildEntity(reader, rsync) : null;
                    }

                    if (rsync == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Group already exists. Please specify another group.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this group.");
                                }
                        }
                    }

                    return rsync;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting RSync Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        RSyncReplication IRSyncDataAccess.UpdateRSync(RSyncReplication rsync)
        {
            try
            {
                const string sp = "RSYNCREPLICATION_UPDATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, rsync.Id);
                    Database.AddInParameter(cmd, Dbstring + "iREPLICATIONID", DbType.Int32, rsync.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iRSYNCPATH", DbType.AnsiString, rsync.RSyncPath);
                    Database.AddInParameter(cmd, Dbstring + "iEXECUTEWITHSUDO", DbType.AnsiString, rsync.ExecuteWithSudo);
                    Database.AddInParameter(cmd, Dbstring + "iSCHEDULETIME", DbType.AnsiString, rsync.ScheduleTime);
                    Database.AddInParameter(cmd, Dbstring + "iUPDATORID", DbType.Int32, rsync.UpdatorId);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, rsync.Password);
                    Database.AddInParameter(cmd, Dbstring + "iOSPlatform", DbType.AnsiString, rsync.OSPlatform);
                 
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        rsync = reader.Read() ? CreateEntityBuilder<RSyncReplication>().BuildEntity(reader, rsync) : null;
                    }

                    //if (robocopy == null)
                    //{
                    //    int returnCode = GetReturnCodeFromParameter(cmd);

                    //    switch (returnCode)
                    //    {
                    //        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                    //            {
                    //                throw new ArgumentException("Group already exists. Please specify another group.");
                    //            }
                    //        default:
                    //            {
                    //                throw new SystemException("An unexpected error has occurred while updating this group.");
                    //            }
                    //    }
                    //}

                    return rsync;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating RSync Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }


        RSyncReplication IRSyncDataAccess.GetRSyncByyId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "RSYNCREPLICATION_GETBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        return reader.Read()
                            ? (CreateEntityBuilder<RSyncReplication>()).BuildEntity(reader, new RSyncReplication())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IRSyncDataAccess.GetRSyncByyId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<RSyncReplication> IRSyncDataAccess.GetAllRSyncAndJob()
        {
            try
            {
                const string sp = "RSYNCANDJOB_GETALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildRoboCopyEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IRoboCopyDataAccess.GetAllRoboCopy" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        private IList<RSyncReplication> BuildRoboCopyEntities(IDataReader reader)
        {
            var RSyncs = new List<RSyncReplication>();

            while (reader.Read())
            {
                var Rsync = new RSyncReplication();
                Rsync.ReplicationBase.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                Rsync.ReplicationBase.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                Rsync.ReplicationBase.Type = Convert.IsDBNull(reader["Type"]) ? ReplicationType.Undefined : (ReplicationType)Enum.Parse(typeof(ReplicationType), Convert.ToString(reader["Type"]).Trim(), true);
                Rsync.Id = Convert.IsDBNull(reader["RSyncId"]) ? 0 : Convert.ToInt32(reader["RSyncId"]);
               
                Rsync.OSPlatform = Convert.IsDBNull(reader["OSPlatform"])
              ? string.Empty
              : Convert.ToString(reader["OSPlatform"]);
                //Rsync.LocalDirectory = Convert.IsDBNull(reader["LocalDirectory"])
                //? string.Empty
                //: Convert.ToString(reader["LocalDirectory"]);
                //Rsync.RemoteDirectory = Convert.IsDBNull(reader["RemoteDirectory"])
                //? string.Empty
                //: Convert.ToString(reader["RemoteDirectory"]);
               
               
                RSyncs.Add(Rsync);
            }

            return (RSyncs.Count > 0) ? RSyncs : null;
        }

        #endregion
    }
}
