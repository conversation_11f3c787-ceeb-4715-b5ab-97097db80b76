﻿using System.Collections.Generic;
using System.Linq;

namespace CP.Common.BusinessEntity
{
    public class ActionTypeBase
    {
        public int Id { get; set; }

        public string BaseActionTypeBase { get; set; }

        public List<ActionTypeBase> FetchActionTypeBases()
        {
            return new List<ActionTypeBase>
            {
                new ActionTypeBase {Id = 1, BaseActionTypeBase = "Storage"},
                new ActionTypeBase {Id = 2, BaseActionTypeBase = "Database"},
                new ActionTypeBase {Id = 3, BaseActionTypeBase = "OS"},
                new ActionTypeBase {Id = 4, BaseActionTypeBase = "VMWare"},
                new ActionTypeBase {Id = 5, BaseActionTypeBase = "Network"},
                new ActionTypeBase {Id = 6, BaseActionTypeBase = "WorkFlow"},
                new ActionTypeBase {Id = 7, BaseActionTypeBase = "Hypervisor"},//comment
                new ActionTypeBase {Id = 8, BaseActionTypeBase = "Web"},
                new ActionTypeBase {Id = 9, BaseActionTypeBase = "FileSystem"},
                new ActionTypeBase {Id = 10, BaseActionTypeBase = "Virtualization"},
                new ActionTypeBase {Id = 11, BaseActionTypeBase = "MS-Exchange Server"},
                new ActionTypeBase {Id = 12, BaseActionTypeBase = "Cloud"},
                new ActionTypeBase {Id = 13, BaseActionTypeBase = "Server Management"},
                new ActionTypeBase {Id = 14, BaseActionTypeBase = "Replication"},
                new ActionTypeBase {Id = 15, BaseActionTypeBase = "System Management Tools"},
                new ActionTypeBase {Id = 16, BaseActionTypeBase = "Third Party"},
                 
                //new ActionTypeBase {Id = 17, BaseActionTypeBase = "Big Data"}

                  new ActionTypeBase {Id = 18, BaseActionTypeBase = "VeritasCluster"},

                       new ActionTypeBase {Id = 19, BaseActionTypeBase = "Nutanix"},

                         new ActionTypeBase {Id = 20, BaseActionTypeBase = "DevOps"}

            };
        }

        public List<ActionTypeBase> FetchActionTypeBase(int id)
        {
            List<ActionTypeBase> actionTypeBases = FetchActionTypeBases();
            return (from p in actionTypeBases
                    where p.Id == id
                    select p).ToList();
        }
    }
}