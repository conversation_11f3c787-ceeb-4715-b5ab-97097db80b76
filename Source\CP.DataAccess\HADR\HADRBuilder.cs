﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class HADRBuilder : IEntityBuilder<HADR>
    {
        //IList<DatabaseMonitor> IEntityBuilder<DatabaseMonitor>.BuildEntities(IDataReader reader)
        //{
        //    List<DatabaseMonitor> databaseMonitors = new List<DatabaseMonitor>();

        //    while (reader.Read())
        //    {
        //        databaseMonitors.Add(((IEntityBuilder<DatabaseMonitor>)this).BuildEntity(reader,new DatabaseMonitor()));
        //    }

        //    return (databaseMonitors.Count > 0) ? databaseMonitors : null;
        //}

        IList<HADR> IEntityBuilder<HADR>.BuildEntities(IDataReader reader)
        {
            var hadrMonitors = new List<HADR>();

            while (reader.Read())
            {
                hadrMonitors.Add(((IEntityBuilder<HADR>)this).BuildEntity(reader, new HADR()));
            }

            return (hadrMonitors.Count > 0) ? hadrMonitors : null;
        }

        HADR IEntityBuilder<HADR>.BuildEntity(IDataReader reader, HADR hadr)
        {
            //const int fldID = 0;
            //const int fldGroupId = 1;

            //const int fldPrIp = 2;
            //const int fldDrIp = 3;

            //const int fldPrDatabaseInstance = 4;
            //const int fldDrDatabaseInstance = 5;

            //const int fldPrDatabaseStatus = 6;
            //const int fldDrDatabaseStatus = 7;

            //const int fldPrLogFile = 8;
            //const int fldDrLogFile = 9;

            //const int fldPrCurrentLsn = 10;
            //const int fldDrCurrentLsn = 11;

            //const int fldPrLsn = 12;
            //const int fldDrLsn = 13;

            //const int fldPrTimeStamp = 14;
            //const int fldDrTimeStamp = 15;

            //const int fldDatalag = 16;
            //const int fldCreateDate = 17;

            //hadr.Id = reader.IsDBNull(fldID) ? 0 : reader.GetInt32(fldID);
            //hadr.InfraObjectId = reader.IsDBNull(fldGroupId) ? 0 : reader.GetInt32(fldGroupId);

            //hadr.PRIp = reader.IsDBNull(fldPrIp) ? string.Empty : reader.GetString(fldPrIp);
            //hadr.DRIp = reader.IsDBNull(fldDrIp) ? string.Empty : reader.GetString(fldDrIp);

            //hadr.PRDatabaseInstance = reader.IsDBNull(fldPrDatabaseInstance)
            //    ? string.Empty
            //    : reader.GetString(fldPrDatabaseInstance);
            //hadr.DRDatabaseInstance = reader.IsDBNull(fldDrDatabaseInstance)
            //    ? string.Empty
            //    : reader.GetString(fldDrDatabaseInstance);

            //hadr.PRDatabaseStatus = reader.IsDBNull(fldPrDatabaseStatus)
            //    ? string.Empty
            //    : reader.GetString(fldPrDatabaseStatus);
            //hadr.DRDatabaseStatus = reader.IsDBNull(fldDrDatabaseStatus)
            //    ? string.Empty
            //    : reader.GetString(fldDrDatabaseStatus);

            //hadr.PRLogFile = reader.IsDBNull(fldPrLogFile) ? string.Empty : reader.GetString(fldPrLogFile);
            //hadr.DRLogFile = reader.IsDBNull(fldDrLogFile) ? string.Empty : reader.GetString(fldDrLogFile);

            //hadr.PRCurrentLSN = reader.IsDBNull(fldPrCurrentLsn) ? string.Empty : reader.GetString(fldPrCurrentLsn);
            //hadr.DRCurrentLSN = reader.IsDBNull(fldDrCurrentLsn) ? string.Empty : reader.GetString(fldDrCurrentLsn);

            //hadr.PRLSN = reader.IsDBNull(fldPrLsn) ? string.Empty : reader.GetString(fldPrLsn);
            //hadr.DRLSN = reader.IsDBNull(fldDrLsn) ? string.Empty : reader.GetString(fldDrLsn);

            //hadr.PRTimestamp = reader.IsDBNull(fldPrTimeStamp) ? string.Empty : reader.GetString(fldPrTimeStamp);
            //hadr.DRTimestamp = reader.IsDBNull(fldDrTimeStamp) ? string.Empty : reader.GetString(fldDrTimeStamp);

            //hadr.Datalag = reader.IsDBNull(fldDatalag) ? string.Empty : reader.GetString(fldDatalag);
            //hadr.CreateDate = reader.IsDBNull(fldCreateDate) ? DateTime.MinValue : reader.GetDateTime(fldCreateDate);

            hadr.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            hadr.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);

            hadr.PRIp = Convert.IsDBNull(reader["PR_IP"]) ? string.Empty : Convert.ToString(reader["PR_IP"]);
            hadr.DRIp = Convert.IsDBNull(reader["DR_IP"]) ? string.Empty : Convert.ToString(reader["DR_IP"]);

            hadr.PRDatabaseInstance = Convert.IsDBNull(reader["PR_DATABASEINSTANCE"]) ? string.Empty : Convert.ToString(reader["PR_DATABASEINSTANCE"]);
            hadr.DRDatabaseInstance = Convert.IsDBNull(reader["DR_DATABASEINSTANCE"]) ? string.Empty : Convert.ToString(reader["DR_DATABASEINSTANCE"]);

            hadr.PRDatabaseStatus = Convert.IsDBNull(reader["PR_DATABASESTATUS"]) ? string.Empty : Convert.ToString(reader["PR_DATABASESTATUS"]);
            hadr.DRDatabaseStatus = Convert.IsDBNull(reader["DR_DATABASESTATUS"]) ? string.Empty : Convert.ToString(reader["DR_DATABASESTATUS"]);

            hadr.PRLogFile = Convert.IsDBNull(reader["PR_LOGFILE"]) ? string.Empty : Convert.ToString(reader["PR_LOGFILE"]);
            hadr.DRLogFile = Convert.IsDBNull(reader["DR_LOGFILE"]) ? string.Empty : Convert.ToString(reader["DR_LOGFILE"]);

            hadr.PRCurrentLSN = Convert.IsDBNull(reader["PR_CURRENTLSN"]) ? string.Empty : Convert.ToString(reader["PR_CURRENTLSN"]);
            hadr.DRCurrentLSN = Convert.IsDBNull(reader["DR_CURRENTLSN"]) ? string.Empty : Convert.ToString(reader["DR_CURRENTLSN"]);

            hadr.PRLSN = Convert.IsDBNull(reader["PR_LSN"]) ? string.Empty : Convert.ToString(reader["PR_LSN"]);
            hadr.DRLSN = Convert.IsDBNull(reader["DR_LSN"]) ? string.Empty : Convert.ToString(reader["DR_LSN"]);

            hadr.PRTimestamp = Convert.IsDBNull(reader["PR_TIMESTAMP"]) ? string.Empty : Convert.ToString(reader["PR_TIMESTAMP"]);
            hadr.DRTimestamp = Convert.IsDBNull(reader["DR_TIMESTAMP"]) ? string.Empty : Convert.ToString(reader["DR_TIMESTAMP"]);

            hadr.Datalag = Convert.IsDBNull(reader["DATALAG"]) ? string.Empty : Convert.ToString(reader["DATALAG"]);
            hadr.CreateDate = Convert.IsDBNull(reader["CREATEDATE"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CREATEDATE"]);


            hadr.PRServerInstance = Convert.IsDBNull(reader["PR_ServerInstance"]) ? string.Empty : Convert.ToString(reader["PR_ServerInstance"]);
            hadr.DRServerInstance = Convert.IsDBNull(reader["DR_ServerInstance"]) ? string.Empty : Convert.ToString(reader["DR_ServerInstance"]);

            hadr.PRInstanceStatus = Convert.IsDBNull(reader["PR_InstanceStatus"]) ? string.Empty : Convert.ToString(reader["PR_InstanceStatus"]);
            hadr.DRInstanceStatus = Convert.IsDBNull(reader["DR_InstanceStatus"]) ? string.Empty : Convert.ToString(reader["DR_InstanceStatus"]);

            hadr.DatabaseVersion = Convert.IsDBNull(reader["DatabaseVersion"]) ? string.Empty : Convert.ToString(reader["DatabaseVersion"]);
            hadr.DatabaseDRVersion = Convert.IsDBNull(reader["DatabaseDRVersion"]) ? string.Empty : Convert.ToString(reader["DatabaseDRVersion"]);

            //hadr.DatabaseVersion = Convert.IsDBNull(reader["DatabaseVersion"]) ? string.Empty : Convert.ToString(reader["DatabaseVersion"]);
            //hadr.DatabaseDRVersion = Convert.IsDBNull(reader["DatabaseDRVersion"]) ? string.Empty : Convert.ToString(reader["DatabaseDRVersion"]);


            return hadr;
        }
    }
}