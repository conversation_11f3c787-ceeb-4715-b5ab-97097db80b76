﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "WorkflowAction", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class WorkflowAction : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public int Type { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string VGName { get; set; }

        [DataMember]
        public string MountPoint { get; set; }

        [DataMember]
        public string SudoUser { get; set; }

        [DataMember]
        public string Luns { get; set; }

        [DataMember]
        public string OracleSid { get; set; }

        [DataMember]
        public string Script { get; set; }

        [DataMember]
        public string CommandLine { get; set; }

        [DataMember]
        public string OSCommand { get; set; }

        [DataMember]
        public string WaitPeroid { get; set; }

        [DataMember]
        public int ActionType { get; set; }

        [DataMember]
        public int GroupId { get; set; }

        [DataMember]
        public int DatabaseId { get; set; }

        [DataMember]
        public int ExceptionCondition { get; set; }

        [DataMember]
        public string ExceptPassword { get; set; }

        [DataMember]
        public int SessionAddRemove { get; set; }

        [DataMember]
        public string Session { get; set; }

        [DataMember]
        public string VG { get; set; }

        [DataMember]
        public string SwitchOverSession { get; set; }

        [DataMember]
        public string Command { get; set; }

        [DataMember]
        public int PrDr { get; set; }

        [DataMember]
        public string StandByControlFile { get; set; }

        [DataMember]
        public string File { get; set; }

        [DataMember]
        public string ControlFile { get; set; }

        [DataMember]
        public int TargetServer { get; set; }

        [DataMember]
        public string ScriptFile { get; set; }

        [DataMember]
        public string TempFile { get; set; }

        [DataMember]
        public int Fastcopy { get; set; }

        [DataMember]
        public string JobQueue { get; set; }

        [DataMember]
        public string Listner { get; set; }

        [DataMember]
        public int DNSServer { get; set; }

        [DataMember]
        public string ExistingHost { get; set; }

        [DataMember]
        public string ExistingIp { get; set; }

        [DataMember]
        public string NewHost { get; set; }

        [DataMember]
        public string NewIp { get; set; }

        [DataMember]
        public string DomainName { get; set; }

        [DataMember]
        public string PrStorageImageId { get; set; }

        [DataMember]
        public string LocalMountPoints { get; set; }

        [DataMember]
        public string RemoteMountPoints { get; set; }

        [DataMember]
        public string HostName { get; set; }

        [DataMember]
        public int IsUseSudo { get; set; }

        [DataMember]
        public string CheckOutput { get; set; }

        [DataMember]
        public int IsReturn { get; set; }

        //03/07/2012
        [DataMember]
        public string FastCopyPath { get; set; }

        [DataMember]
        public string SourceFile { get; set; }

        [DataMember]
        public string SourceFolder { get; set; }

        [DataMember]
        public string TargetFolder { get; set; }

        [DataMember]
        public string DiskGroup { get; set; }

        [DataMember]
        public string DiskName { get; set; }

        //31/07/2012
        [DataMember]
        public string RsyncPath { get; set; }

        [DataMember]
        public string TargetFile { get; set; }

        [DataMember]
        public string MachineName { get; set; }

        [DataMember]
        public string SnapShotName { get; set; }

        [DataMember]
        public string TimeOut { get; set; }

        [DataMember]
        public string VirtualMachine { get; set; }

        [DataMember]
        public string DestinationPath { get; set; }

        [DataMember]
        public string SnapMirrorVolume { get; set; }

        [DataMember]
        public string TargetMachine { get; set; }

        [DataMember]
        public string ActiveOwner { get; set; }

        [DataMember]
        public string VmPath { get; set; }

        [DataMember]
        public string OriginalText { get; set; }

        [DataMember]
        public string NewText { get; set; }

        [DataMember]
        public string RouterConfiguration { get; set; }

        [DataMember]
        public string InterfacePassword { get; set; }

        [DataMember]
        public int ExectionMode { get; set; }

        [DataMember]
        public string DeviceGroup { get; set; }

        [DataMember]
        public string FileSystem { get; set; }

        [DataMember]
        public string WaitTime { get; set; }

        [DataMember]
        public string ApplicationName { get; set; }

        [DataMember]
        public string MapFile { get; set; }

        [DataMember]
        public string Task { get; set; }

        [DataMember]
        public string FileSystemMountPoint { get; set; }

        [DataMember]
        public int WorkFlowId { get; set; }

        [DataMember]
        public string WorkFlowActionId { get; set; }

        [DataMember]
        public int DependencyType { get; set; }

        [DataMember]
        public string Time { get; set; }

        [DataMember]
        public int TargetDatabase { get; set; }

        [DataMember]
        public string BackUpFile { get; set; }

        [DataMember]
        public string TraceFile { get; set; }

        [DataMember]
        public int Expression { get; set; }

        [DataMember]
        public int ProcessCount { get; set; }

        [DataMember]
        public string HDisc { get; set; }

        [DataMember]
        public string HitachihorcomInstance { get; set; }

        [DataMember]
        public string SubnetMask { get; set; }

        [DataMember]
        public string GateWay { get; set; }

        [DataMember]
        public string PrimaryDNS { get; set; }

        [DataMember]
        public string SecoundaryDNS { get; set; }

        [DataMember]
        public string RestorePoint { get; set; }

        [DataMember]
        public string AlertText { get; set; }

        [DataMember]
        public int AlertModeType { get; set; }

        [DataMember]
        public string ProcessFlow { get; set; }

        [DataMember]
        public string VolName { get; set; }

        [DataMember]
        public string Drive { get; set; }

        [DataMember]
        public string RTO { get; set; }

        [DataMember]
        public string ServiceName { get; set; }

        [DataMember]
        public string Appuser { get; set; }

        [DataMember]
        public string ProcessID { get; set; }

        [DataMember]
        public string Shellprompt { get; set; }

        [DataMember]
        public int DSCSLISERVER { get; set; }

        [DataMember]
        public int hmcserver { get; set; }

        [DataMember]
        public string LSSID { get; set; }

        [DataMember]
        public DateTime StartTime { get; set; }

        [DataMember]
        public DateTime EndTime { get; set; }

        [DataMember]
        public DateTime ActualTime { get; set; }

        [DataMember]
        public string IsCondition { get; set; }

        [DataMember]
        public string IsConditionActionId { get; set; }

        [DataMember]
        public string EX { get; set; }

        [DataMember]
        public string TotalTime { get; set; }

        [DataMember]
        public string RelationshipId
        {
            get;
            set;
        }

        [DataMember]
        public string CheckState
        {
            get;
            set;
        }

        [DataMember]
        public string RecoveryPlan
        {
            get;
            set;
        }

        [DataMember]
        public string ClusterName
        {
            get;
            set;
        }

        [DataMember]
        public string ClusterGroupResource
        {
            get;
            set;
        }

        [DataMember]
        public string EmailSuccess
        {
            get;
            set;
        }

        [DataMember]
        public string EmailFail
        {
            get;
            set;
        }

        [DataMember]
        public string SmsSuccess
        {
            get;
            set;
        }

        [DataMember]
        public string SmsFail
        {
            get;
            set;
        }

        [DataMember]
        public string AlertMechanismType
        {
            get;
            set;
        }

        [DataMember]
        public string ParallelWorkflowMessage
        {
            get;
            set;
        }

        [DataMember]
        public string ScriptBlock
        {
            get;
            set;
        }

        [DataMember]
        public string URL
        {
            get;
            set;
        }

        [DataMember]
        public string HTMLContents
        {
            get;
            set;
        }

        [DataMember]
        public string ZoneName
        {
            get;
            set;
        }

        [DataMember]
        public string CellNo
        {
            get;
            set;
        }

        [DataMember]
        public string EmailId
        {
            get;
            set;
        }

        [DataMember]
        public string Resource
        {
            get;
            set;
        }

        [DataMember]
        public string AlertUsers
        {
            get;
            set;
        }

        [DataMember]
        public string VMIsClustered { get; set; }

        [DataMember]
        public string ControllerType { get; set; }

        [DataMember]
        public string ControllerLocation { get; set; }

        [DataMember]
        public string DefineValue { get; set; }

        [DataMember]
        public string SharingOption { get; set; }

        [DataMember]
        public string ClusterSharedVolumeStatus { get; set; }

        [DataMember]
        public int InfraobjectId { get; set; }

        [DataMember]
        public int EBDRStatusId { get; set; }

        [DataMember]
        public int IsWLST { get; set; }

        [DataMember]
        public int IsLock
        {
            get;
            set;
        }


        // History 
        [DataMember]
        public string Reason { get; set; }

        [DataMember]
        public double Version { get; set; }

        [DataMember]
        public string User { get; set; }

        [DataMember]
        public virtual int WFActionId { get; set; }

        [DataMember]
        public double WorkflowVersion { get; set; }

        #endregion Properties
    }
}