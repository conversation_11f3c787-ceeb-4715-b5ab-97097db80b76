﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "InfraObjectJob", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class InfraObjectJob : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int BusinessServiceId { get; set; }

        [DataMember]
        public string StorageImageId { get; set; }

        [DataMember]
        public int JobId { get; set; }

        [DataMember]
        public string TriggerName { get; set; }

        [DataMember]
        public string CronExpression { get; set; }

        [DataMember]
        public string CronTime { get; set; }

        [DataMember]
        public string NextFireTime { get; set; }

        [DataMember]
        public string LastFireTime { get; set; }

        [DataMember]
        public string JobStatus { get; set; }

        [DataMember]
        public int IsEnabled { get; set; }

        [DataMember]
        public int InfraObjType { get; set; }

        [DataMember]
        public int CronExpressionChange { get; set; }

        [DataMember]
        public int RecoveryType { get; set; }

       [DataMember]
        public string NAME { get; set; }



        #endregion Properties
    }
}