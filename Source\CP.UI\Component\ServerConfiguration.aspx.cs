﻿using System;
using System.Web.Services;
using System.Web.UI.WebControls;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Linq;
using CP.BusinessFacade;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using CP.Common.DatabaseEntity;
using System.Web.Security;
using System.Web;
using System.Globalization;
using System.Net;
using log4net;
using System.Text.RegularExpressions;

namespace CP.UI
{
    public partial class ServerConfiguration : ServerBasePageEditor
    {
        private bool _pnlsudovalidator = true;

        private bool _licensevalidator = true;

        public static string sshkeypassword = string.Empty;
        IList<Substitute_Authentication> SubsAuthList = new List<Substitute_Authentication>();

        private string _oldIpAddress = string.Empty;
        private string _oldUsername = string.Empty;
        private string _oldPassword = string.Empty;
        public static string IPAddress = string.Empty;

        private readonly ILog _logger = LogManager.GetLogger(typeof(ServerConfiguration));

        /// <summary>
        /// Gets the static GUID from session for encryption/decryption operations
        /// </summary>
        /// <returns>Static GUID string from session</returns>
        private string GetStaticGuidFromSession()
        {
            return Session["StaticGuid"]?.ToString() ?? string.Empty;
        }

        /// <summary>
        /// Web method to get static GUID for JavaScript calls
        /// </summary>
        /// <returns>Static GUID from session</returns>
        [WebMethod]
        public static string GetStaticGuid()
        {
            return HttpContext.Current.Session["StaticGuid"]?.ToString() ?? string.Empty;
        }

        public override string MessageInitials
        {
            get { return "Server"; }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Component.SereverList;
                }
                return string.Empty;
            }
        }

        public override void SaveEditor()
        {
            try
            {

                var server = new Server();
                if ((ViewState["_token"] != null))
                {
                    if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                    {
                        Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                        //Response.Redirect("~/Logout.aspx");
                        return;
                    }
                }
                else
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    //Response.Redirect("~/Logout.aspx");
                    return;
                }

                server = Facade.GetServerByName_Deleted(txtHostName.Text);

                if (server != null)
                {
                    CurrentEntity.Id = server.Id;
                    CurrentEntity.IsActive = 1;
                    CurrentEntity.UpdatorId = LoggedInUserId;
                    CurrentEntity = Facade.UpdateServer(CurrentEntity);
                    Session["SubsAuthList"] = null;
                    SubsAuthList.Clear();
                    ActivityLogger.AddLog(LoggedInUserName, "Server", UserActionType.UpdateServerComponent, "The Server '" + CurrentEntity.Name + "' was updated to the server component table", LoggedInUserId);
                }
                else if (CurrentEntity.IsNew)
                {
                    CurrentEntity.CreatorId = LoggedInUserId;
                    CurrentEntity.UpdatorId = LoggedInUserId;
                    CurrentEntity = Facade.AddServer(CurrentEntity);
                    SaveSubsAuthn();
                    ActivityLogger.AddLog1(LoggedInUserName, "Server", UserActionType.CreateServerComponent, "The Server '" + CurrentEntity.Name + "' was updated to the server component table", LoggedInUserId, IPAddress);
                    _logger.Info("Server Created successfully " + "'" + CurrentEntity.Name + "'" + " With User IP Address " + "'" + IPAddress + "'");
                }
                else
                {
                    CurrentEntity.UpdatorId = LoggedInUserId;
                    CurrentEntity = Facade.UpdateServer(CurrentEntity);
                    Session["SubsAuthList"] = null;
                    SubsAuthList.Clear();
                    ActivityLogger.AddLog1(LoggedInUserName, "Server", UserActionType.UpdateServerComponent, "The Server '" + CurrentEntity.Name + "' was updated to the server component table", LoggedInUserId, IPAddress);
                    _logger.Info("Server Updated  successfully" + "'" + CurrentEntity.Name + "'" + " With User IP Address " + "'" + IPAddress + "'");
                }
            }
            catch (CpException ex)
            {
                if (ex != null)
                {

                    _logger.Error("CP exception while loading server Configuration in  SaveEditor method on Server Configuration page load: With User IP Address." + IPAddress + " and Exception is:" + ex.Message);
                    if (ex.InnerException != null)
                        _logger.Error("Inner Exception : " + ex.InnerException.ToString());
                    if (ex.StackTrace != null)
                        _logger.Error("Exception details : " + ex.StackTrace.ToString());
                }
                ExceptionManager.Manage(ex);
            }
        }

        public void SaveSubsAuthn()
        {
            if (Session["SubsAuthList"] != null)
            {
                SubsAuthList = (IList<Substitute_Authentication>)Session["SubsAuthList"];

                foreach (Substitute_Authentication objSubsAuthn in SubsAuthList)
                {
                    objSubsAuthn.ServerId = CurrentEntity.Id;
                    Facade.AddSubstituteAuthn(objSubsAuthn);
                }

                Session["SubsAuthList"] = null;
                SubsAuthList.Clear();
            }
        }
        //public override void BuildEntities()
        //{
        //    CurrentEntity.Name = txtHostName.Text;

        //    CurrentEntity.SiteId = Convert.ToInt32(ddlSite.SelectedValue);

        //    CurrentEntity.Type = ddlServerType.SelectedValue;

        //    CurrentEntity.IPAddress = CryptographyHelper.Md5Encrypt(txtIPAddress.Text);

        //    CurrentEntity.Port = string.IsNullOrEmpty(txtPort.Text) ? 0 : Convert.ToInt32(txtPort.Text);

        //    CurrentEntity.OSType = ddlOS.SelectedItem.Text;

        //    CurrentEntity.SSHUserName = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtSSHUser.Text, hdfStaticGuid.Value));

        //    string sudoUser = Utility.getOriginalEncryData(txtSudoUser.Text, hdfStaticGuid.Value);

        //    CurrentEntity.SudoUser = String.IsNullOrEmpty(sudoUser) ? "" : sudoUser;

        //    CurrentEntity.SudoPassword = String.IsNullOrEmpty(txtSudoUserPassword.Text) ? null : CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtSudoUser.Text, hdfStaticGuid.Value));

        //    CurrentEntity.DSIPAddress = CryptoHelperCsJava.Encrypt(txtIPAddress.Text);

        //    CurrentEntity.DSSSHUserName = CryptoHelperCsJava.Encrypt(Utility.getOriginalEncryData(txtSSHUser.Text, hdfStaticGuid.Value));


        //    CurrentEntity.IsPartOfCluster = chkCluster.Checked;

        //    CurrentEntity.DataStoreName = txtDataStoreName.Text;

        //    CurrentEntity.VmPath = txtVmPath.Text;

        //    CurrentEntity.Disk = txtDisks.Text;
        //    string keyPass = Utility.getOriginalEncryData(txtSSHKeyPassword.Text, hdfStaticGuid.Value);
        //    string SSHPass = Utility.getOriginalEncryData(txtSSHPassword.Text, hdfStaticGuid.Value);

        //    CurrentEntity.EnableSudoAccess = Convert.ToInt32(chkSubsAuth.Checked);
        //    if (ddlAuthenticationType.SelectedValue.Equals("1"))
        //    {

        //        CurrentEntity.SshKeyPath = string.IsNullOrEmpty(txtSSHKeyPath.Text) ? string.Empty : CryptographyHelper.Md5Encrypt(txtSSHKeyPath.Text);
        //        CurrentEntity.SshKeyPassword = string.IsNullOrEmpty(keyPass) ? sshkeypassword : CryptographyHelper.Md5Encrypt(keyPass);

        //        CurrentEntity.SSHPassword = string.IsNullOrEmpty(SSHPass) ? string.Empty : Utility.IsMD5EncryptedString(SSHPass) ? SSHPass : CryptographyHelper.Md5Encrypt(SSHPass);



        //    }
        //    else
        //    {
        //        CurrentEntity.SshKeyPath = string.Empty;
        //        CurrentEntity.SshKeyPassword = string.Empty;

        //        if (!string.IsNullOrEmpty(txtSSHPassword.Text))
        //        {
        //            bool isEncryptPassword = Utility.IsMD5EncryptedString(txtSSHPassword.Text);



        //            CurrentEntity.DSSSHPassword = Utility.IsMD5EncryptedString(txtSSHPassword.Text) ? CurrentEntity.DSSSHPassword : CryptoHelperCsJava.Encrypt(txtSSHPassword.Text);

        //            CurrentEntity.SSHPassword = isEncryptPassword ? txtSSHPassword.Text : Utility.IsMD5EncryptedString(txtSSHPassword.Text) ? txtSSHPassword.Text : CryptographyHelper.Md5Encrypt(txtSSHPassword.Text);
        //        }
        //        else
        //        {
        //            CurrentEntity.SSHPassword = string.Empty;

        //            CurrentEntity.DSSSHPassword = string.Empty;
        //        }

        //        CurrentEntity.IsUseSshKeyAuth = Convert.ToInt32(ddlAuthenticationType.SelectedValue);

        //    }
        //    CurrentEntity.ShellPrompt = txtShellPrompt.Text;
        //    CurrentEntity.LicenseKey = txtLicenceKey.Text;
        //    CurrentEntity.HostName = txtHostName1.Text;

        //    CurrentEntity.ServerRole = Convert.ToInt32(ddlServerRole.SelectedValue);

        //    CurrentEntity.IsVirtualGuestOS = chkVirtualGuestOS.Checked ? 1 : 0;
        //}



        public override void BuildEntities()
        {
            CurrentEntity.Name = txtHostName.Text;

            CurrentEntity.SiteId = Convert.ToInt32(ddlSite.SelectedValue);

            CurrentEntity.Type = ddlServerType.SelectedValue;

            // CurrentEntity.IPAddress = CryptographyHelper.Md5Encrypt(txtIPAddress.Text);

            if (!string.IsNullOrEmpty(txtIPAddress.Text))
                CurrentEntity.IPAddress = CryptographyHelper.Md5Encrypt(txtIPAddress.Text);
            else
                CurrentEntity.IPAddress = string.Empty;


            CurrentEntity.Port = string.IsNullOrEmpty(txtPort.Text) ? 0 : Convert.ToInt32(txtPort.Text);

            CurrentEntity.OSType = ddlOS.SelectedItem.Text;

            CurrentEntity.SSHUserName = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtSSHUser.Text, GetStaticGuidFromSession()));

            string sudoUser = Utility.getOriginalEncryData(txtSudoUser.Text, GetStaticGuidFromSession());

            CurrentEntity.SudoUser = String.IsNullOrEmpty(sudoUser) ? "" : sudoUser;

            CurrentEntity.SudoPassword = String.IsNullOrEmpty(txtSudoUserPassword.Text) ? null : CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtSudoUser.Text, GetStaticGuidFromSession()));

            //CurrentEntity.DSIPAddress = CryptoHelperCsJava.Encrypt(txtIPAddress.Text);

            if (!string.IsNullOrEmpty(txtIPAddress.Text))
                CurrentEntity.DSIPAddress = CryptoHelperCsJava.Encrypt(txtIPAddress.Text);
            else
                CurrentEntity.DSIPAddress = string.Empty;

            CurrentEntity.DSSSHUserName = CryptoHelperCsJava.Encrypt(Utility.getOriginalEncryData(txtSSHUser.Text, GetStaticGuidFromSession()));

            if (ChkSSOEnable.Checked)
            {
                CurrentEntity.SSOTypeId = Convert.ToInt32(ddlSignOnType.SelectedValue);
                CurrentEntity.SSOEnabled = 1;

                CurrentEntity.SSOProfileId = Convert.ToInt32(ddlSSOProfile.SelectedValue);

            }
            else
            {
                CurrentEntity.DSSSHPassword = CryptoHelperCsJava.Encrypt(Utility.getOriginalEncryData(txtSSHPassword.Text, GetStaticGuidFromSession()));
                CurrentEntity.SSOTypeId = 0;
                CurrentEntity.SSOEnabled = 0;

                CurrentEntity.SSOProfileId = 0;
                CurrentEntity.Safe = string.Empty;
                CurrentEntity.Object = string.Empty;
                CurrentEntity.Folder = string.Empty;
                CurrentEntity.Reason = string.Empty;
            }

            if (chkIsASM.Checked)
            {
                CurrentEntity.IsASMGrid = 1;
                CurrentEntity.ASMInstanceId = Convert.ToInt32(ddlASMInstance.SelectedValue);
            }
            else
            {
                CurrentEntity.IsASMGrid = 0;
                CurrentEntity.ASMInstanceId = 0;
            }

            CurrentEntity.IsPartOfCluster = chkCluster.Checked;

            CurrentEntity.DataStoreName = txtDataStoreName.Text;

            CurrentEntity.VmPath = txtVmPath.Text;

            CurrentEntity.Disk = txtDisks.Text;

            CurrentEntity.EnableSudoAccess = Convert.ToInt32(chkSubsAuth.Checked);  // Convert.ToInt32(ddlSudosu.SelectedValue);

            string keyPass = Utility.getOriginalEncryData(txtSSHKeyPassword.Text, GetStaticGuidFromSession());
            string SSHPass = Utility.getOriginalEncryData(txtSSHPassword.Text, GetStaticGuidFromSession());

            if (ddlAuthenticationType.SelectedValue.Equals("1"))
            {

                CurrentEntity.IsUseSshKeyAuth = Convert.ToInt32(ddlAuthenticationType.SelectedValue);
                // if (!ChkSSOEnable.Checked)
                // {
                CurrentEntity.SshKeyPath = string.IsNullOrEmpty(txtSSHKeyPath.Text) ? string.Empty : CryptographyHelper.Md5Encrypt(txtSSHKeyPath.Text);
                CurrentEntity.SshKeyPassword = string.IsNullOrEmpty(keyPass) ? sshkeypassword : CryptographyHelper.Md5Encrypt(keyPass);
                //CurrentEntity.SSHPassword = string.IsNullOrEmpty(SSHPass) ? string.Empty : Utility.IsMD5EncryptedString(SSHPass) ? txtSSHPassword.Text : CryptographyHelper.Md5Encrypt(SSHPass);// string.Empty;
                CurrentEntity.SSHPassword = string.IsNullOrEmpty(SSHPass) ? string.Empty : Utility.IsMD5EncryptedString(SSHPass) ? SSHPass : CryptographyHelper.Md5Encrypt(SSHPass);
                // }

            }
            //}
            else
            {
                CurrentEntity.SshKeyPath = string.Empty;
                CurrentEntity.SshKeyPassword = string.Empty;
                if (!ChkSSOEnable.Checked)
                {
                    CurrentEntity.SSHPassword = string.IsNullOrEmpty(SSHPass) ? string.Empty : Utility.IsMD5EncryptedString(SSHPass) ? SSHPass : CryptographyHelper.Md5Encrypt(SSHPass);
                    CurrentEntity.IsUseSshKeyAuth = Convert.ToInt32(ddlAuthenticationType.SelectedValue);
                }

            }

            if (ChkSSOEnable.Checked)
            {
                if (ddlSignOnType.SelectedValue == "2")
                {
                    CurrentEntity.SSOEnabled = 1;
                    CurrentEntity.SSOTypeId = Convert.ToInt32(ddlSignOnType.SelectedValue);
                    CurrentEntity.Safe = txtSafe.Text;
                    CurrentEntity.Object = txtObject.Text;
                    CurrentEntity.Folder = txtFolder.Text;
                    CurrentEntity.Reason = txtReason.Text;
                    CurrentEntity.SSOProfileId = Convert.ToInt32(ddlSSOProfile.SelectedValue);
                }
                else if (ddlSignOnType.SelectedValue == "1")
                {
                    //dsSSHPassword.Visible = true;
                    CurrentEntity.SSOEnabled = 1;
                    CurrentEntity.SSOTypeId = Convert.ToInt32(ddlSignOnType.SelectedValue);
                    CurrentEntity.Safe = string.Empty;
                    CurrentEntity.Object = string.Empty;
                    CurrentEntity.Folder = string.Empty;
                    CurrentEntity.Reason = string.Empty;
                    CurrentEntity.SSOProfileId = Convert.ToInt32(ddlSSOProfile.SelectedValue);
                }
                else if (ddlSignOnType.SelectedValue == "3")
                {
                    //dsSSHPassword.Visible = true;
                    CurrentEntity.SSOEnabled = 1;
                    CurrentEntity.SSOTypeId = Convert.ToInt32(ddlSignOnType.SelectedValue);
                    CurrentEntity.Safe = string.Empty;
                    CurrentEntity.Object = string.Empty;
                    CurrentEntity.Folder = string.Empty;
                    CurrentEntity.Reason = string.Empty;
                    CurrentEntity.SSOProfileId = Convert.ToInt32(ddlSSOProfile.SelectedValue);
                }
            }

            //if (ddlAuthenticationType.SelectedValue.Equals("1"))
            //{
            //    CurrentEntity.SshKeyPath = string.IsNullOrEmpty(txtSSHKeyPath.Text) ? string.Empty : CryptographyHelper.Md5Encrypt(txtSSHKeyPath.Text);

            //    if (string.IsNullOrEmpty(txtSSHKeyPassword.Text))
            //    {
            //        CurrentEntity.SshKeyPassword = string.Empty;
            //    }
            //    else
            //    {
            //        CurrentEntity.SshKeyPassword = Utility.IsMD5EncryptedString(txtSSHKeyPassword.Text) ? txtSSHKeyPassword.Text : CryptographyHelper.Md5Encrypt(txtSSHKeyPassword.Text);
            //    }

            //    CurrentEntity.SSHPassword = string.Empty;

            //    CurrentEntity.DSSSHPassword = string.Empty;

            //    CurrentEntity.IsUseSshKeyAuth = Convert.ToInt32(ddlAuthenticationType.SelectedValue);
            //}
            //else
            //{
            //    CurrentEntity.SshKeyPath = string.Empty;
            //    CurrentEntity.SshKeyPassword = string.Empty;

            //    if (!string.IsNullOrEmpty(txtSSHPassword.Text))
            //    {
            //        bool isEncryptPassword = Utility.IsMD5EncryptedString(txtSSHPassword.Text);

            //        CurrentEntity.DSSSHPassword = Utility.IsMD5EncryptedString(txtSSHPassword.Text) ? CurrentEntity.DSSSHPassword : CryptoHelperCsJava.Encrypt(txtSSHPassword.Text);

            //        CurrentEntity.SSHPassword = isEncryptPassword ? txtSSHPassword.Text : CryptographyHelper.Md5Encrypt(txtSSHPassword.Text);
            //    }
            //    else
            //    {
            //        CurrentEntity.SSHPassword = string.Empty;

            //        CurrentEntity.DSSSHPassword = string.Empty;
            //    }

            //    CurrentEntity.IsUseSshKeyAuth = Convert.ToInt32(ddlAuthenticationType.SelectedValue);
            //}
            CurrentEntity.ShellPrompt = txtShellPrompt.Text;
            CurrentEntity.LicenseKey = txtLicenceKey.Text;

            CurrentEntity.HostName = txtHostName1.Text;

            CurrentEntity.ServerRole = Convert.ToInt32(ddlServerRole.SelectedValue);

            CurrentEntity.IsVirtualGuestOS = chkVirtualGuestOS.Checked ? 1 : 0;

            CurrentEntity.WinRMPort = Convert.ToInt32(ddlWinRMPort.SelectedItem.Value);

            CurrentEntity.ProxyAccessType = ddlProxyAccess.SelectedItem.Text.ToString();
        }


        protected void lvsubaunth_ItemCreated(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.InsertItem)
            {
                var ddlAuthType = (DropDownList)e.Item.FindControl("ddlAuthType");

                if (ddlAuthType != null)
                {
                    EnumHelper.PopulateEnumIntoList(ddlAuthType, typeof(SubsAuthnTypes), "- Select Type -");
                }
            }
        }

        protected void lvsubaunth_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                TextBox txtDispAuthType = (TextBox)e.Item.FindControl("txtDispAuthType");
                var ddlDispAuthType = (DropDownList)e.Item.FindControl("ddlDispAuthType");

                if (txtDispAuthType != null && ddlDispAuthType != null)
                {
                    ListViewDataItem dataItem = (ListViewDataItem)e.Item;
                    Substitute_Authentication rowView = dataItem.DataItem as Substitute_Authentication;

                    int Auth_Type = rowView.Auth_Type;

                    EnumHelper.PopulateEnumIntoList(ddlDispAuthType, typeof(SubsAuthnTypes), "- Select Type -");
                    ddlDispAuthType.SelectedValue = Convert.ToString(Auth_Type);
                }

                var lblDispPass = (Label)e.Item.FindControl("lblDispPass");
                var txtDispPass = (TextBox)e.Item.FindControl("txtDispPass");

                if (lblDispPass != null && txtDispPass != null)
                {
                    txtDispPass.Attributes.Add("Value", lblDispPass.Text);
                }
            }

        }


        protected void lvsubaunth_ItemInserting(object sender, ListViewInsertEventArgs e)
        {
            var ddlAuthType = (DropDownList)e.Item.FindControl("ddlAuthType");
            var txtPath = (TextBox)e.Item.FindControl("txtPath");
            var txtUser = (TextBox)e.Item.FindControl("txtUser");
            var txtPass = (TextBox)e.Item.FindControl("txtPass");

            if (Session["SubsAuthList"] != null)
            {
                SubsAuthList = (IList<Substitute_Authentication>)Session["SubsAuthList"];
            }
            Substitute_Authentication objSubsAuthn = new Substitute_Authentication();
            objSubsAuthn.Auth_Type = Convert.ToInt32(ddlAuthType.SelectedValue);
            objSubsAuthn.Path = string.IsNullOrEmpty(txtPath.Text) ? string.Empty : txtPath.Text;
            objSubsAuthn.UserName = string.IsNullOrEmpty(txtUser.Text) ? string.Empty : txtUser.Text;
            objSubsAuthn.Password = string.IsNullOrEmpty(txtPass.Text) ? string.Empty : Utility.IsMD5EncryptedString(txtPass.Text) ? txtPass.Text : CryptographyHelper.Md5Encrypt(txtPass.Text);
            objSubsAuthn.CreatorId = LoggedInUserId;

            if (btnSave.Text == "Save")
            {
                SubsAuthList.Add(objSubsAuthn);
                Session["SubsAuthList"] = SubsAuthList;
            }
            else
            {
                SubsAuthList.Clear();
                objSubsAuthn.ServerId = CurrentEntity.Id;
                Facade.AddSubstituteAuthn(objSubsAuthn);
                lvsubaunth.EditIndex = -1;
                BindSubsAuthn();
            }

            ShowList();
            lvsubaunth.EditIndex = -1;
        }

        private void BindSubsAuthn()
        {
            SubsAuthList = Facade.GetAllByServerIdSubstituteAuthn(CurrentServerId);
            Session["SubsAuthList"] = SubsAuthList;
        }
        protected void lvsubaunth_ItemUpdating(object sender, ListViewUpdateEventArgs e)
        {
            var lbl = (lvsubaunth.Items[e.ItemIndex].FindControl("Id")) as Label;
            var lblId = lbl.Text.ToInteger();


            var ddlAuthType = lvsubaunth.Items[e.ItemIndex].FindControl("ddlEditAuthType") as DropDownList;
            var txtPath = lvsubaunth.Items[e.ItemIndex].FindControl("txtEditPath") as TextBox;
            var txtUser = lvsubaunth.Items[e.ItemIndex].FindControl("txtEditUser") as TextBox;
            var txtPass = lvsubaunth.Items[e.ItemIndex].FindControl("txtEditPass") as TextBox;

            Substitute_Authentication objSubsAuthn = new Substitute_Authentication();
            objSubsAuthn.Id = lblId;
            objSubsAuthn.Auth_Type = Convert.ToInt32(ddlAuthType.SelectedValue);
            objSubsAuthn.Path = string.IsNullOrEmpty(txtPath.Text) ? string.Empty : txtPath.Text;
            objSubsAuthn.UserName = string.IsNullOrEmpty(txtUser.Text) ? string.Empty : txtUser.Text;
            objSubsAuthn.Password = string.IsNullOrEmpty(txtPass.Text) ? string.Empty : Utility.IsMD5EncryptedString(txtPass.Text) ? txtPass.Text : CryptographyHelper.Md5Encrypt(txtPass.Text);
            objSubsAuthn.CreatorId = LoggedInUserId;

            if (btnSave.Text == "Save")
            {
                if (Session["SubsAuthList"] != null)
                {
                    SubsAuthList = (IList<Substitute_Authentication>)Session["SubsAuthList"];
                    SubsAuthList.Insert(e.ItemIndex, objSubsAuthn);
                    SubsAuthList.RemoveAt(e.ItemIndex + 1);
                    Session["SubsAuthList"] = SubsAuthList;
                    lvsubaunth.EditIndex = -1;
                }
            }
            else
            {
                SubsAuthList.Clear();
                objSubsAuthn.ServerId = CurrentEntity.Id;
                objSubsAuthn.UpdatorId = LoggedInUserId;
                Facade.UpdateSubstituteAuthn(objSubsAuthn);
                lvsubaunth.EditIndex = -1;
                BindSubsAuthn();
            }

            ShowList();
        }

        protected void lvsubaunth_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lblDeleteId = (lvsubaunth.Items[e.ItemIndex].FindControl("Id")) as Label;

            if (lblDeleteId != null)
            {
                int id = lblDeleteId.Text.ToInteger();

                if (btnSave.Text == "Save")
                {
                    if (Session["SubsAuthList"] != null)
                    {
                        SubsAuthList = (List<Substitute_Authentication>)Session["SubsAuthList"];
                        SubsAuthList.RemoveAt(e.ItemIndex);
                        Session["SubsAuthList"] = SubsAuthList;
                    }
                }
                else
                {
                    Facade.DeleteSubstituteAuthn(id);
                    BindSubsAuthn();
                }

                ShowList();

            }

        }
        protected void lvsubaunth_ItemCanceling(object sender, ListViewCancelEventArgs e)
        {
            lvsubaunth.EditIndex = -1;

            if (Session["SubsAuthList"] != null)
            {
                SubsAuthList = (IList<Substitute_Authentication>)Session["SubsAuthList"];
                ShowList();
            }
        }

        protected void lvsubaunth_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Label lblId = lvsubaunth.Items[e.NewEditIndex].FindControl("Id") as Label;
            var Id = lblId.Text;

            if (Session["SubsAuthList"] != null)
            {
                SubsAuthList = (IList<Substitute_Authentication>)Session["SubsAuthList"];
                lvsubaunth.EditIndex = e.NewEditIndex;
                lvsubaunth.DataSource = SubsAuthList;
                lvsubaunth.DataBind();
            }

            var txtAuthType = lvsubaunth.Items[e.NewEditIndex].FindControl("txtEditAuthType") as TextBox;
            var ddlAuthType = lvsubaunth.Items[e.NewEditIndex].FindControl("ddlEditAuthType") as DropDownList;
            var lblEditPass = lvsubaunth.Items[e.NewEditIndex].FindControl("lblEditPass") as Label;
            var txtEditPass = lvsubaunth.Items[e.NewEditIndex].FindControl("txtEditPass") as TextBox;

            if (txtAuthType != null && ddlAuthType != null)
            {
                EnumHelper.PopulateEnumIntoList(ddlAuthType, typeof(SubsAuthnTypes), "- Select Type -");
                ddlAuthType.SelectedValue = txtAuthType.Text.Trim();
            }

            if (txtEditPass != null && lblEditPass != null)
            {
                txtEditPass.Attributes.Add("Value", lblEditPass.Text);
            }
        }


        public override void PrepareView()
        {
            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST   
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();

            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            // Store GUID in Session instead of hidden field for security
            Session["StaticGuid"] = Guid.NewGuid().ToString();
            if (IsUserOperator || IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            if (IsUserCustom)
            {
                IList<AccessManagerCustom> lstAccess = Facade.GetAccessManagerByUserId(LoggedInUserId);
                if (lstAccess != null)
                {
                    var ObjAccess = lstAccess.FirstOrDefault(x => x.AccessMenuType == AccessManagerType.Configuration.ToString());
                    if (ObjAccess == null)
                    {
                        Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                        return;
                    }
                }
                else
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    return;
                }


            }


            Utility.SelectMenu(Master, "Module2");

            // Utility.PopulateSite(ddlSite, true);
            //Utility.PopulateSiteByCompanyIdAndRole(ddlSite, LoggedInUserCompanyId, IsSuperAdmin, IsParentCompnay, true);

            if (IsSuperAdmin)
            {
                ddlSite.DataSource = Facade.GetSitesByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
                ddlSite.DataTextField = "Name";
                ddlSite.DataValueField = "Id";
                ddlSite.DataBind();
                ddlSite.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectSiteName, "000"));
            }
            else
            {
                ddlSite.DataSource = Facade.GetSiteByUserId(LoggedInUserId);
                ddlSite.DataTextField = "Name";
                ddlSite.DataValueField = "Id";
                ddlSite.DataBind();
                ddlSite.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectSiteName, "000"));
            }

            EnumHelper.PopulateAllEnumDescriptionIntoList(ddlOS, typeof(OSTypes), "- Select OS Type -");
            Utility.Populat(ddlAuthenticationType, true);
            Utility.PopulateSingleSignOnType(ddlSignOnType, true);
            Utility.PopulateASMInstance(ddlASMInstance, true);
            PrepareEditView();

            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 

        }

        public override void PrepareEditView()
        {
            if (CurrentServerId > 0)
            {
                txtHostName1.Enabled = false;
                txtIPAddress.Enabled = false;
                txtIPAddress.CssClass = "form-control aspNetDisabled";
                txtHostName1.CssClass = "form-control aspNetDisabled";

                BindControlsValue();
                btnSave.Text = "Update";
            }
            else
            {
                txtHostName1.Enabled = true;
                txtIPAddress.Enabled = true;
                txtIPAddress.CssClass = "form-control";
                txtHostName1.CssClass = "form-control";
                Session["SubsAuthList"] = null;
                dSSHKeyPath.Visible = false;
                dSSHKeyPassword.Visible = false;
                dvUser.Visible = true;
                dsSSHPassword.Visible = true;
                lblSSHUser.Text = "SSH  User";
                lblSSHPassword.Text = "SSH Password";
                rfvtxtSSHPassword.Enabled = true;
                lblSshStatus.Text = "*";
                // txtSSHPassword.Attributes["value"] = string.Empty;
            }
        }

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                IgnoreIDs.Add("txtSSHUser");
                IgnoreIDs.Add("txtLicenceKey");
                IgnoreIDs.Add("txtHostName1");
                IgnoreIDs.Add("txtSSHKeyPath");
                IgnoreIDs.Add("txtSSHKeyPassword");
                IgnoreIDs.Add("txtSSHUser");
                IgnoreIDs.Add("txtSSHPassword");

                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        //BOC Validate Request
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    //ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    ActivityLogger.AddLog1(LoggedInUserName, "Server", UserActionType.UpdateServerComponent, "The Server '" + CurrentEntity.Name + "' was updated to the server component table", LoggedInUserId, IPAddress);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                //ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                ActivityLogger.AddLog1(LoggedInUserName, "Server", UserActionType.UpdateServerComponent, "The Server '" + CurrentEntity.Name + "' was updated to the server component table", LoggedInUserId, IPAddress);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request


        private void BindControlsValue()
        {
            Regex ip = new Regex(@"^(([01]?\d\d?|2[0-4]\d|25[0-5])\.){3}([01]?\d\d?|25[0-5]|2[0-4]\d)$");

            MatchCollection result = ip.Matches(CryptographyHelper.Md5Decrypt(CurrentEntity.IPAddress));
            bool IsIPAddress = false;
            if (result.Count == 1)
                IsIPAddress = true;
            else
                IsIPAddress = false;

            Utility.PopulateSSOProfile(ddlSSOProfile, true, CurrentEntity.SSOTypeId);
            txtHostName.Text = CurrentEntity.Name;
            txtHostName1.Text = CurrentEntity.HostName;
            ddlSite.SelectedValue = CurrentEntity.SiteId.ToString();
            ddlServerType.SelectedValue = CurrentEntity.Type;
            chkCluster.Checked = CurrentEntity.IsPartOfCluster;
            //txtIPAddress.Text = CryptographyHelper.Md5Decrypt(CurrentEntity.IPAddress);

            if (!string.IsNullOrEmpty(CurrentEntity.IPAddress))
            {
                txtIPAddress.Text = IsIPAddress==true ? CryptographyHelper.Md5Decrypt(CurrentEntity.IPAddress) : string.Empty;
            }
            else
            {
                txtIPAddress.Text = string.Empty;
            }

            if (CurrentEntity.Port != 0)
                txtPort.Text = CurrentEntity.Port.ToString();


            string sshuser = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentEntity.SSHUserName), GetStaticGuidFromSession());
            txtSSHUser.Text = String.IsNullOrEmpty(sshuser) ? string.Empty : sshuser;

            string sshPass2 = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), GetStaticGuidFromSession()) : "";
            txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass2)) ? string.Empty : sshPass2;

            //string sshpassword = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword), hdfStaticGuid.Value);
            //txtSSHPassword.Text = String.IsNullOrEmpty(sshpassword) ? string.Empty : sshpassword;

            ddlServerRole.SelectedValue = Convert.ToString(CurrentEntity.ServerRole);

            chkIsASM.Checked = Convert.ToBoolean(CurrentEntity.IsASMGrid);
            if (chkIsASM.Checked == true)
            {
                ddlASMInstance.SelectedValue = Convert.ToString(CurrentEntity.ASMInstanceId);
                divASMInstance.Visible = true;
                updateISASM.Update();
            }
            else
            {
                ddlASMInstance.SelectedIndex = -1;
                divASMInstance.Visible = false;
                updateISASM.Update();
            }

            if (CurrentEntity.IsVirtualGuestOS == 1)
                chkVirtualGuestOS.Checked = true;

            // ASU-Sudo Su Chnages 
            chkSubsAuth.Checked = CurrentEntity.EnableSudoAccess > 0 ? true : false;
            Session["SubsAuthList"] = null;
            SubsAuthList.Clear();
            if (chkSubsAuth.Checked)
            {
                SubsAuthList = Facade.GetAllByServerIdSubstituteAuthn(CurrentEntity.Id);
                Session["SubsAuthList"] = SubsAuthList;
                ShowList();
                chkSubsAuth_CheckedChanged(null, null);
            }
            ChkSudoCheckedChanged(null, null);



            if (!string.IsNullOrEmpty(CurrentEntity.SudoUser))
            {
                txtSudoUser.Text = Utility.getHashKeyByString(CurrentEntity.SudoUser, GetStaticGuidFromSession());
            }
            if (!string.IsNullOrEmpty(CurrentEntity.SudoPassword))
            {
                string strSudoPass = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentEntity.SudoPassword), GetStaticGuidFromSession());
                txtSudoUserPassword.Attributes["value"] = String.IsNullOrEmpty(strSudoPass) ? string.Empty : strSudoPass;
            }

            //  txtSudoUser.Text = CurrentEntity.SudoUser;


            //string strSudoPass = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentEntity.SudoPassword), hdfStaticGuid.Value);
            //txtSudoUserPassword.Attributes["value"] = String.IsNullOrEmpty(strSudoPass) ? string.Empty : strSudoPass;


            //  txtSudoUserPassword.Attributes["value"] = String.IsNullOrEmpty(CurrentEntity.SudoPassword) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SudoPassword);

            // ddlOS.SelectedIndex = EnumHelper.GetValue(CurrentEntity.OSType, typeof(OSTypes));
            ddlOS.SelectedValue = Convert.ToString(EnumHelper.GetValue(CurrentEntity.OSType, typeof(OSTypes)));
            //if (ddlOS.SelectedIndex > 7)
            if (Convert.ToInt32(ddlOS.SelectedValue) > 7)
            {
                lblAuthentication.Visible = false;
            }
            if (ddlServerType.SelectedValue == "PRESXIServer" || ddlServerType.SelectedValue == "DRESXIServer")
            {
                panelVmDetails.Visible = true;
                txtDataStoreName.Text = CurrentEntity.DataStoreName.ToString();
                txtVmPath.Text = CurrentEntity.VmPath.ToString();
                txtDisks.Text = CurrentEntity.Disk.ToString();
            }
            AuthenticationType(ddlOS.SelectedValue);
            if (CurrentEntity.IsUseSshKeyAuthentication.Equals(1))
            {
                if (CurrentEntity.SSOEnabled == 1)
                {

                    pnlSSoProfilrDrp.Visible = true;
                    pnlSSOType.Visible = true;
                    dvUser.Visible = true;
                    lblSSHUser.Text = "SSH Key User";
                    dsSSHPassword.Visible = false;
                    pnlsshkeypath.Visible = false;
                    ChkSSOEnable.Checked = true;
                    ddlSignOnType.SelectedValue = Convert.ToString(CurrentEntity.SSOTypeId);
                    ddlSSOProfile.SelectedValue = Convert.ToString(CurrentEntity.SSOProfileId);
                    // ddlSSOProfile.SelectedIndex = CurrentEntity.SSOProfileId;

                    ddlAuthenticationType.SelectedValue = Convert.ToString(CurrentEntity.IsUseSshKeyAuthentication);
                    //txtSSHKeyPath.Text = CryptographyHelper.Md5Decrypt(CurrentEntity.SshKeyPath);
                    //txtSSHKeyPassword.Attributes["value"] = (string.IsNullOrEmpty(CurrentEntity.SshKeyPassword)) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SshKeyPassword);

                    rfvtxtSSHKeyPath.Enabled = true;
                    //rfvtxtSSHKeyPassword.Enabled = true;
                    dsSSHPassword.Visible = false;
                    rfvtxtSSHPassword.Enabled = false;

                }
                else
                {
                    // chkSSHKey.Checked = true;
                    ddlAuthenticationType.SelectedValue = Convert.ToString(CurrentEntity.IsUseSshKeyAuthentication);
                    dvUser.Visible = true;
                    dSSHKeyPassword.Visible = true;
                    dSSHKeyPath.Visible = true;
                    dsSSHPassword.Visible = false;
                    pnlSSoProfilrDrp.Visible = false;
                    lblSSHUser.Text = "SSH Key User";
                    //lblSSHPassword.Text = "SSH Key Password";
                    txtSSHKeyPath.Text = !string.IsNullOrEmpty(CurrentEntity.SshKeyPath) ? CryptographyHelper.Md5Decrypt(CurrentEntity.SshKeyPath) : string.Empty;
                    string sshPass = !string.IsNullOrEmpty(CurrentEntity.SshKeyPassword) ? Utility.getHashKeyByString(CurrentEntity.SshKeyPassword, GetStaticGuidFromSession()) : "";
                    txtSSHKeyPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;
                    txtSSHKeyPassword.Attributes["value"] = string.IsNullOrEmpty(CurrentEntity.SshKeyPassword) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SshKeyPassword);


                    rfvtxtSSHKeyPath.Enabled = true;
                    //rfvtxtSSHKeyPassword.Enabled = true;
                    dsSSHPassword.Visible = false;
                    rfvtxtSSHPassword.Enabled = false;
                    lblSshStatus.Text = " ";

                }

            }
            else if (CurrentEntity.IsUseSshKeyAuthentication.Equals(0))
            {
                if (CurrentEntity.SSOTypeId > 0)
                {
                    pnlSSOType.Visible = true;
                    dsSSHPassword.Visible = false;
                    ChkSSOEnable.Checked = true;
                    pnlSSoProfilrDrp.Visible = true;

                    ddlSignOnType.SelectedValue = Convert.ToString(CurrentEntity.SSOTypeId);
                    ddlSSOProfile.SelectedValue = Convert.ToString(CurrentEntity.SSOProfileId);
                    ddlAuthenticationType.SelectedValue = Convert.ToString(CurrentEntity.IsUseSshKeyAuthentication);
                    //done buy priyanka
                    dvUser.Visible = true;
                    dsSSHPassword.Visible = true;
                    lblSSHUser.Text = "SSH  User";
                    lblSSHPassword.Text = "SSH Password";
                    rfvtxtSSHPassword.Enabled = true;
                    lblSshStatus.Text = "*";

                    string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), GetStaticGuidFromSession()) : "";
                    txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;
                    //txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(CurrentEntity.SSHPassword.ToString())) ? string.Empty : CurrentEntity.SSHPassword.ToString(); // CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);
                }
                else
                {
                    pnlSSOType.Visible = false;
                    ChkSSOEnable.Checked = false;
                    pnlSSoProfilrDrp.Visible = false;
                    ddlAuthenticationType.SelectedValue = Convert.ToString(CurrentEntity.IsUseSshKeyAuthentication);
                    dSSHKeyPath.Visible = false;
                    dSSHKeyPassword.Visible = false;
                    dvUser.Visible = true;
                    dsSSHPassword.Visible = true;
                    lblSSHUser.Text = "SSH  User";
                    lblSSHPassword.Text = "SSH Password";
                    rfvtxtSSHPassword.Enabled = true;
                    lblSshStatus.Text = "*";
                    string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), GetStaticGuidFromSession()) : "";
                    txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass; // CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);


                }
            }
            else if (CurrentEntity.IsUseSshKeyAuthentication.Equals(2))
            {
                ddlAuthenticationType.SelectedValue = Convert.ToString(CurrentEntity.IsUseSshKeyAuthentication);
                dSSHKeyPath.Visible = false;
                dSSHKeyPassword.Visible = false;
                dvUser.Visible = true;
                dsSSHPassword.Visible = true;
                lblSSHUser.Text = "WMI User";
                lblSSHPassword.Text = "WMI Password";
                rfvtxtSSHPassword.Enabled = true;
                lblSshStatus.Text = "*";
                string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), GetStaticGuidFromSession()) : "";
                txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;
                //txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(CurrentEntity.SSHPassword.ToString())) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);

            }
            else if (CurrentEntity.IsUseSshKeyAuthentication.Equals(3))
            {
                ddlAuthenticationType.SelectedValue = CurrentEntity.IsUseSshKeyAuthentication.ToString();
                dSSHKeyPath.Visible = false;
                dSSHKeyPassword.Visible = false;
                dvUser.Visible = true;
                dsSSHPassword.Visible = true;
                lblSSHUser.Text = "PowerShell User";
                lblSSHPassword.Text = "PowerShell Password";
                rfvtxtSSHPassword.Enabled = true;
                lblSshStatus.Text = "*";
                string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), GetStaticGuidFromSession()) : "";
                txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;
                pnlPowerShell.Visible = true;
            }
            else
            {
                if (CurrentEntity.SSOEnabled == 1)
                {
                    pnlSSOType.Visible = true;
                    dsSSHPassword.Visible = false;
                    pnlSSoProfilrDrp.Visible = true;
                    ddlSignOnType.SelectedValue = Convert.ToString(CurrentEntity.SSOTypeId);
                    ddlSSOProfile.SelectedValue = Convert.ToString(CurrentEntity.SSOProfileId);
                }
                else
                {
                    pnlSSoProfilrDrp.Visible = false;
                    dSSHKeyPassword.Visible = false;
                    dSSHKeyPath.Visible = false;
                    dvUser.Visible = true;
                    dsSSHPassword.Visible = true;
                    lblSSHUser.Text = "SSH  User";
                    lblSSHPassword.Text = "SSH Password";
                    rfvtxtSSHPassword.Enabled = true;
                    lblSshStatus.Text = "*";
                    string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), GetStaticGuidFromSession()) : "";
                    txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;
                    //txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(CurrentEntity.SSHPassword.ToString())) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);

                }
            }
            txtShellPrompt.Text = CurrentEntity.ShellPrompt;
            txtLicenceKey.Text = CurrentEntity.LicenseKey;

            if (CurrentEntity.SSOEnabled == 1)
            {
                pnlSSOType.Visible = true;
                ChkSSOEnable.Checked = true;
                dsSSHPassword.Visible = false;

                ddlSignOnType.SelectedValue = Convert.ToString(CurrentEntity.SSOTypeId);
                if (ddlSignOnType.SelectedValue == "2")
                {
                    pnlCyberark.Visible = true;
                    pnlSSoProfilrDrp.Visible = true;
                    ddlSSOProfile.SelectedValue = Convert.ToString(CurrentEntity.SSOProfileId);
                    txtSafe.Text = CurrentEntity.Safe;
                    txtObject.Text = CurrentEntity.Object;
                    txtFolder.Text = CurrentEntity.Folder;
                    txtReason.Text = CurrentEntity.Reason;
                }
                else
                {
                    pnlCyberark.Visible = false;
                    pnlSSoProfilrDrp.Visible = true;
                    ddlSSOProfile.SelectedValue = Convert.ToString(CurrentEntity.SSOProfileId);
                    // dsSSHPassword.Visible = true;
                }
            }
            else
            {
                pnlSSOType.Visible = false;
                ChkSSOEnable.Checked = false;
                pnlSSoProfilrDrp.Visible = false;
                //dsSSHPassword.Visible = false;
            }

            //if (CurrentEntity.IsUseSshKeyAuthentication.Equals(1))
            //{
            //    if (CurrentEntity.SSOEnabled == 1)
            //    {
            //        pnlSSoProfilrDrp.Visible = true;
            //        pnlSSOType.Visible = true;
            //        dvUser.Visible = true;
            //        lblSSHUser.Text = "SSH Key User";
            //        dsSSHPassword.Visible = false;
            //        pnlsshkeypath.Visible = false;
            //        ddlAuthenticationType.SelectedValue = CurrentEntity.IsUseSshKeyAuthentication.ToString();
            //        rfvtxtSSHKeyPath.Enabled = true;
            //        dsSSHPassword.Visible = false;
            //        rfvtxtSSHPassword.Enabled = false;
            //    }
            //    else
            //    {
            //        ddlAuthenticationType.SelectedValue = CurrentEntity.IsUseSshKeyAuthentication.ToString();
            //        dvUser.Visible = true;
            //        dSSHKeyPassword.Visible = true;
            //        dSSHKeyPath.Visible = true;
            //        dsSSHPassword.Visible = false;
            //        pnlSSoProfilrDrp.Visible = false;
            //        lblSSHUser.Text = "SSH Key User";



            //        txtSSHKeyPath.Text = CryptographyHelper.Md5Decrypt(CurrentEntity.SshKeyPath);

            //        string sshPass = !string.IsNullOrEmpty(CurrentEntity.SshKeyPassword) ? Utility.getHashKeyByString(CurrentEntity.SshKeyPassword, hdfStaticGuid.Value) : "";
            //        txtSSHKeyPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;

            //        //if (sshPass != null)
            //        //{
            //        //    string dsshpass = !string.IsNullOrEmpty(CurrentEntity.DSSSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.DSSSHPassword), hdfStaticGuid.Value) : "";
            //        //    txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(dsshpass)) ? string.Empty : dsshpass;
            //        //}
            //        //  txtSSHKeyPassword.Attributes["value"] = (string.IsNullOrEmpty(CurrentEntity.SshKeyPassword)) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SshKeyPassword); //string.IsNullOrEmpty(CurrentEntity.SshKeyPassword) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SshKeyPassword);
            //        rfvtxtSSHKeyPath.Enabled = true;
            //        dsSSHPassword.Visible = false;
            //        rfvtxtSSHPassword.Enabled = false;
            //        lblSshStatus.Text = " ";
            //    }

            //}
            //else if (CurrentEntity.IsUseSshKeyAuthentication.Equals(0))
            //{
            //    pnlSSOType.Visible = false;
            //    pnlSSoProfilrDrp.Visible = false;
            //    ddlAuthenticationType.SelectedValue = CurrentEntity.IsUseSshKeyAuthentication.ToString();
            //    dSSHKeyPath.Visible = false;
            //    dSSHKeyPassword.Visible = false;
            //    dvUser.Visible = true;
            //    dsSSHPassword.Visible = true;
            //    lblSSHUser.Text = "SSH  User";
            //    lblSSHPassword.Text = "SSH Password";
            //    rfvtxtSSHPassword.Enabled = true;
            //    lblSshStatus.Text = "*";

            //    //string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), hdfStaticGuid.Value) : "";
            //    //txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;

            //    txtSSHPassword.Text = CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);

            //    if (!string.IsNullOrEmpty(CurrentEntity.SSHPassword))
            //    {

            //        string sshPass1 = !string.IsNullOrEmpty(txtSSHPassword.Text) ? CryptographyHelper.Md5Decrypt(Convert.ToString(CurrentEntity.SSHPassword)) : "";
            //        CurrentEntity.DSSSHPassword = CryptoHelperCsJava.Encrypt(sshPass1);
            //    }
            //    else
            //    {

            //        CurrentEntity.DSSSHPassword = CryptoHelperCsJava.Encrypt(txtSSHPassword.Text);
            //    }


            //    //if (sshPass != null)
            //    //{
            //    //    string dsshpass = !string.IsNullOrEmpty(CurrentEntity.DSSSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.DSSSHPassword), hdfStaticGuid.Value) : "";
            //    //    txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(dsshpass)) ? string.Empty : dsshpass;
            //    //}
            //    //  string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), hdfStaticGuid.Value) : "";

            //    //  txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(CurrentEntity.SSHPassword.ToString())) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), hdfStaticGuid.Value) : ""; // CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);
            //}
            //else if (CurrentEntity.IsUseSshKeyAuthentication.Equals(2))
            //{
            //    ddlAuthenticationType.SelectedValue = CurrentEntity.IsUseSshKeyAuthentication.ToString();
            //    dSSHKeyPath.Visible = false;
            //    dSSHKeyPassword.Visible = false;
            //    dvUser.Visible = true;
            //    dsSSHPassword.Visible = true;
            //    lblSSHUser.Text = "WMI User";
            //    lblSSHPassword.Text = "WMI Password";
            //    rfvtxtSSHPassword.Enabled = true;
            //    lblSshStatus.Text = "*";
            //    string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), hdfStaticGuid.Value) : "";
            //    txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;

            //    //if (sshPass != null)
            //    //{
            //    //    string dsshpass = !string.IsNullOrEmpty(CurrentEntity.DSSSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.DSSSHPassword), hdfStaticGuid.Value) : "";
            //    //    txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(dsshpass)) ? string.Empty : dsshpass;
            //    //}
            //}
            //else
            //{
            //    pnlSSoProfilrDrp.Visible = false;
            //    dSSHKeyPassword.Visible = false;
            //    dSSHKeyPath.Visible = false;
            //    dvUser.Visible = true;
            //    dsSSHPassword.Visible = true;
            //    lblSSHUser.Text = "SSH  User";
            //    lblSSHPassword.Text = "SSH Password";
            //    rfvtxtSSHPassword.Enabled = true;
            //    lblSshStatus.Text = "*";
            //    //  txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(CurrentEntity.SSHPassword.ToString())) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), hdfStaticGuid.Value) : ""; // CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);  // (string.IsNullOrEmpty(CurrentEntity.SSHPassword.ToString())) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);

            //    string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), hdfStaticGuid.Value) : "";
            //    txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;
            //    //if (sshPass != null)
            //    //{
            //    //    string dsshpass = !string.IsNullOrEmpty(CurrentEntity.DSSSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.DSSSHPassword), hdfStaticGuid.Value) : "";
            //    //    txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(dsshpass)) ? string.Empty : dsshpass;
            //    //}
            //}
            txtShellPrompt.Text = CurrentEntity.ShellPrompt;
            txtLicenceKey.Text = CurrentEntity.LicenseKey;
            ddlWinRMPort.SelectedValue = CurrentEntity.WinRMPort.ToString();
            ddlProxyAccess.SelectedValue = CurrentEntity.ProxyAccessType.ToString();
        }




        //private void BindControlsValue()
        //{
        //    Utility.PopulateSSOProfile(ddlSSOProfile, true, CurrentEntity.SSOTypeId);
        //    txtHostName.Text = CurrentEntity.Name;
        //    txtHostName1.Text = CurrentEntity.HostName;
        //    ddlSite.SelectedValue = Convert.ToString(CurrentEntity.SiteId);
        //    ddlServerType.SelectedValue = CurrentEntity.Type;
        //    chkCluster.Checked = CurrentEntity.IsPartOfCluster;
        //    txtIPAddress.Text = CryptographyHelper.Md5Decrypt(CurrentEntity.IPAddress);
        //    if (CurrentEntity.Port != 0)
        //        txtPort.Text = Convert.ToString(CurrentEntity.Port);

        //    if (!string.IsNullOrEmpty(CurrentEntity.SSHUserName))
        //    {
        //        string sshuser = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentEntity.SSHUserName), hdfStaticGuid.Value);
        //        txtSSHUser.Text = String.IsNullOrEmpty(sshuser) ? string.Empty : sshuser;
        //    }


        //    //chkSudo.Checked = CurrentEntity.EnableSudoAccess;
        //    chkSubsAuth.Checked = CurrentEntity.EnableSudoAccess > 0 ? true : false;
        //    //  ddlSudosu.SelectedValue = Convert.ToString(CurrentEntity.EnableSudoAccess);

        //    ddlServerRole.SelectedValue = Convert.ToString(CurrentEntity.ServerRole);

        //    if (CurrentEntity.IsVirtualGuestOS == 1)
        //        chkVirtualGuestOS.Checked = true;

        //    if (ddlSudosu.SelectedValue == "2")
        //    {
        //        lblSudo.Text = "Su User";
        //        lblsupassword.Text = "Su User Password";
        //        //chkSuUserPass.Visible = true;
        //        //chkSuUserPass.Checked = true;
        //        //lblsupassword.Visible = true;
        //        //txtSudoUserPassword.Visible = true;
        //    }
        //    else
        //    {
        //        lblSudo.Text = "Sudo User";
        //        lblsupassword.Text = "Sudo User Password";
        //        //chkSuUserPass.Visible = false;
        //        //chkSuUserPass.Checked = false;
        //        //lblsupassword.Visible = true;
        //        //txtSudoUserPassword.Visible = true;
        //    }
        //    ChkSudoCheckedChanged(null, null);
        //    if (!string.IsNullOrEmpty(CurrentEntity.SudoUser))
        //    {
        //        txtSudoUser.Text = Utility.getHashKeyByString(CurrentEntity.SudoUser, hdfStaticGuid.Value);
        //    }
        //    if (!string.IsNullOrEmpty(CurrentEntity.SudoPassword))
        //    {
        //        string strSudoPass = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentEntity.SudoPassword), hdfStaticGuid.Value);
        //        txtSudoUserPassword.Attributes["value"] = String.IsNullOrEmpty(strSudoPass) ? string.Empty : strSudoPass;
        //    }

        //    //ddlOS.SelectedIndex = EnumHelper.GetValue(CurrentEntity.OSType, typeof(OSTypes));
        //    ddlOS.SelectedValue = Convert.ToString(EnumHelper.GetValue(CurrentEntity.OSType, typeof(OSTypes)));

        //    if (ddlOS.SelectedIndex > 4)
        //    {
        //        lblAuthentication.Visible = false;
        //        ddlSudosu.Visible = false;
        //    }
        //    if (ddlServerType.SelectedValue == "PRESXIServer" || ddlServerType.SelectedValue == "DRESXIServer")
        //    {
        //        panelVmDetails.Visible = true;
        //        txtDataStoreName.Text = Convert.ToString(CurrentEntity.DataStoreName);
        //        txtVmPath.Text = Convert.ToString(CurrentEntity.VmPath);
        //        txtDisks.Text = Convert.ToString(CurrentEntity.Disk);
        //    }
        //    AuthenticationType(ddlOS.SelectedValue);
        //    if (CurrentEntity.IsUseSshKeyAuthentication.Equals(1))
        //    {
        //        if (CurrentEntity.SSOEnabled == 1)
        //        {

        //            pnlSSoProfilrDrp.Visible = true;
        //            pnlSSOType.Visible = true;
        //            dvUser.Visible = true;
        //            lblSSHUser.Text = "SSH Key User";
        //            dsSSHPassword.Visible = false;
        //            pnlsshkeypath.Visible = false;
        //            ChkSSOEnable.Checked = true;
        //            ddlSignOnType.SelectedValue = Convert.ToString(CurrentEntity.SSOTypeId);
        //            ddlSSOProfile.SelectedValue = Convert.ToString(CurrentEntity.SSOProfileId);
        //            // ddlSSOProfile.SelectedIndex = CurrentEntity.SSOProfileId;

        //            ddlAuthenticationType.SelectedValue = Convert.ToString(CurrentEntity.IsUseSshKeyAuthentication);
        //            //txtSSHKeyPath.Text = CryptographyHelper.Md5Decrypt(CurrentEntity.SshKeyPath);
        //            //txtSSHKeyPassword.Attributes["value"] = (string.IsNullOrEmpty(CurrentEntity.SshKeyPassword)) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SshKeyPassword);

        //            rfvtxtSSHKeyPath.Enabled = true;
        //            //rfvtxtSSHKeyPassword.Enabled = true;
        //            dsSSHPassword.Visible = false;
        //            rfvtxtSSHPassword.Enabled = false;

        //        }
        //        else
        //        {
        //chkSSHKey.Checked = true;
        //ddlAuthenticationType.SelectedValue = Convert.ToString(CurrentEntity.IsUseSshKeyAuthentication);
        //dvUser.Visible = true;
        //dSSHKeyPassword.Visible = true;
        //dSSHKeyPath.Visible = true;
        //dsSSHPassword.Visible = false;
        //pnlSSoProfilrDrp.Visible = false;
        //lblSSHUser.Text = "SSH Key User";
        ////lblSSHPassword.Text = "SSH Key Password";
        //txtSSHKeyPath.Text = CryptographyHelper.Md5Decrypt(CurrentEntity.SshKeyPath);
        //string sshPass = !string.IsNullOrEmpty(CurrentEntity.SshKeyPassword) ? Utility.getHashKeyByString(CurrentEntity.SshKeyPassword, hdfStaticGuid.Value) : "";
        //txtSSHKeyPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;
        //txtSSHKeyPassword.Attributes["value"] = string.IsNullOrEmpty(CurrentEntity.SshKeyPassword) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SshKeyPassword);


        //            rfvtxtSSHKeyPath.Enabled = true;
        //            //rfvtxtSSHKeyPassword.Enabled = true;
        //            dsSSHPassword.Visible = false;
        //            rfvtxtSSHPassword.Enabled = false;
        //            lblSshStatus.Text = " ";

        //        }

        //    }
        //    else if (CurrentEntity.IsUseSshKeyAuthentication.Equals(0))
        //    {
        //        if (CurrentEntity.SSOTypeId > 0)
        //        {
        //            pnlSSOType.Visible = true;
        //            dsSSHPassword.Visible = false;
        //            ChkSSOEnable.Checked = true;
        //            pnlSSoProfilrDrp.Visible = true;

        //            ddlSignOnType.SelectedValue = Convert.ToString(CurrentEntity.SSOTypeId);
        //            ddlSSOProfile.SelectedValue = Convert.ToString(CurrentEntity.SSOProfileId);
        //            ddlAuthenticationType.SelectedValue = Convert.ToString(CurrentEntity.IsUseSshKeyAuthentication);
        //            //done buy priyanka
        //            dvUser.Visible = true;
        //            dsSSHPassword.Visible = true;
        //            lblSSHUser.Text = "SSH  User";
        //            lblSSHPassword.Text = "SSH Password";
        //            rfvtxtSSHPassword.Enabled = true;
        //            lblSshStatus.Text = "*";

        //            string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), hdfStaticGuid.Value) : "";
        //            txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;
        //            //txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(CurrentEntity.SSHPassword.ToString())) ? string.Empty : CurrentEntity.SSHPassword.ToString(); // CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);
        //        }
        //        else
        //        {
        //            pnlSSOType.Visible = false;
        //            ChkSSOEnable.Checked = false;
        //            pnlSSoProfilrDrp.Visible = false;
        //            ddlAuthenticationType.SelectedValue = Convert.ToString(CurrentEntity.IsUseSshKeyAuthentication);
        //            dSSHKeyPath.Visible = false;
        //            dSSHKeyPassword.Visible = false;
        //            dvUser.Visible = true;
        //            dsSSHPassword.Visible = true;
        //            lblSSHUser.Text = "SSH  User";
        //            lblSSHPassword.Text = "SSH Password";
        //            rfvtxtSSHPassword.Enabled = true;
        //            lblSshStatus.Text = "*";
        //            string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), hdfStaticGuid.Value) : "";
        //            txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass; // CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);


        //        }
        //    }
        //    else if (CurrentEntity.IsUseSshKeyAuthentication.Equals(2))
        //    {
        //        ddlAuthenticationType.SelectedValue = Convert.ToString(CurrentEntity.IsUseSshKeyAuthentication);
        //        dSSHKeyPath.Visible = false;
        //        dSSHKeyPassword.Visible = false;
        //        dvUser.Visible = true;
        //        dsSSHPassword.Visible = true;
        //        lblSSHUser.Text = "WMI User";
        //        lblSSHPassword.Text = "WMI Password";
        //        rfvtxtSSHPassword.Enabled = true;
        //        lblSshStatus.Text = "*";
        //        string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), hdfStaticGuid.Value) : "";
        //        txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;
        //        //txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(CurrentEntity.SSHPassword.ToString())) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);

        //    }
        //    else
        //    {
        //        if (CurrentEntity.SSOEnabled == 1)
        //        {
        //            pnlSSOType.Visible = true;
        //            dsSSHPassword.Visible = false;
        //            pnlSSoProfilrDrp.Visible = true;
        //            ddlSignOnType.SelectedValue = Convert.ToString(CurrentEntity.SSOTypeId);
        //            ddlSSOProfile.SelectedValue = Convert.ToString(CurrentEntity.SSOProfileId);
        //        }
        //        else
        //        {
        //            pnlSSoProfilrDrp.Visible = false;
        //            dSSHKeyPassword.Visible = false;
        //            dSSHKeyPath.Visible = false;
        //            dvUser.Visible = true;
        //            dsSSHPassword.Visible = true;
        //            lblSSHUser.Text = "SSH  User";
        //            lblSSHPassword.Text = "SSH Password";
        //            rfvtxtSSHPassword.Enabled = true;
        //            lblSshStatus.Text = "*";
        //            string sshPass = !string.IsNullOrEmpty(CurrentEntity.SSHPassword) ? Utility.getHashKeyByString(Convert.ToString(CurrentEntity.SSHPassword), hdfStaticGuid.Value) : "";
        //            txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(sshPass)) ? string.Empty : sshPass;
        //            //txtSSHPassword.Attributes["value"] = (string.IsNullOrEmpty(CurrentEntity.SSHPassword.ToString())) ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentEntity.SSHPassword);

        //        }
        //    }
        //    txtShellPrompt.Text = CurrentEntity.ShellPrompt;
        //    txtLicenceKey.Text = CurrentEntity.LicenseKey;

        //    if (CurrentEntity.SSOEnabled == 1)
        //    {
        //        pnlSSOType.Visible = true;
        //        ChkSSOEnable.Checked = true;
        //        dsSSHPassword.Visible = false;

        //        ddlSignOnType.SelectedValue = Convert.ToString(CurrentEntity.SSOTypeId);
        //        if (ddlSignOnType.SelectedValue == "2")
        //        {
        //            pnlCyberark.Visible = true;
        //            pnlSSoProfilrDrp.Visible = true;
        //            ddlSSOProfile.SelectedValue = Convert.ToString(CurrentEntity.SSOProfileId);
        //            txtSafe.Text = CurrentEntity.Safe;
        //            txtObject.Text = CurrentEntity.Object;
        //            txtFolder.Text = CurrentEntity.Folder;
        //            txtReason.Text = CurrentEntity.Reason;
        //        }
        //        else
        //        {
        //            pnlCyberark.Visible = false;
        //            pnlSSoProfilrDrp.Visible = true;
        //            ddlSSOProfile.SelectedValue = Convert.ToString(CurrentEntity.SSOProfileId);
        //            // dsSSHPassword.Visible = true;
        //        }
        //    }
        //    else
        //    {
        //        pnlSSOType.Visible = false;
        //        ChkSSOEnable.Checked = false;
        //        pnlSSoProfilrDrp.Visible = false;
        //        //dsSSHPassword.Visible = false;
        //    }
        //}




        protected void chkSubsAuth_CheckedChanged(object sender, EventArgs e)
        {
            pnlConSubsAuthn.Visible = chkSubsAuth.Checked;
            ShowList();
            UpnlConSubsAuthn.Update();
        }

        private void ShowList()
        {
            if (Session["SubsAuthList"] != null)
            {
                SubsAuthList = (List<Substitute_Authentication>)Session["SubsAuthList"];
            }
            lvsubaunth.DataSource = SubsAuthList;
            lvsubaunth.DataBind();
        }




        protected void TxtHostNameTextChanged(object sender, EventArgs e)
        {
            if (txtHostName.Text == string.Empty) return;
            lblName.Text = CheckSiteNameExist() ? "Server already exist" : "";
            if (lblName.Text != string.Empty)
            {
                btnSave.Enabled = false;
            }
            else
            {
                btnSave.Enabled = true;
                btnSave.CssClass = "btn btn-primary";
            }
        }

        #region LicenseKey Generator

        public bool IsNumber(string str)
        {
            return str.All(Char.IsNumber);
        }
        private bool LicenseKeyValidation()
        {
            List<bool> lstvalidate = new List<bool>();
            string[] address = txtIPAddress.Text.Split('.');
            string[] lic = txtLicenceKey.Text.Split('-');
            string lic1 = txtLicenceKey.Text;
            string concot;
            string con4;
            lblErr.Text = "";
            string address1 = string.Empty;
            // PTSL.CPEncryptionClass PtsLKey = new PTSL.CPEncryptionClass();
            PTSL.CPClass PtsLKey = new PTSL.CPClass();
            try
            {

                if (Session["SSHPassword"] != null)
                {
                    txtSSHPassword.Attributes["value"] = Session["SSHPassword"].ToString();
                }
                if (!string.IsNullOrEmpty(txtIPAddress.Text) || !string.IsNullOrEmpty(txtHostName1.Text))
                {
                    if (ddlServerType.SelectedItem.Text == "DSCLIServer" || ddlServerType.SelectedItem.Text == "HMCServer" || ddlServerType.SelectedItem.Text == "PRAppServer" || ddlServerType.SelectedItem.Text == "DRAppServer" || ddlServerType.SelectedItem.Text == "PRESXIServer" || ddlServerType.SelectedItem.Text == "DRESXIServer" || ddlServerType.SelectedItem.Text == "DNSServer" || ddlServerType.SelectedItem.Text == "SymCLIServer")
                    {
                        if (!string.IsNullOrEmpty(txtIPAddress.Text) && !string.IsNullOrEmpty(txtHostName1.Text))
                            address1 = txtIPAddress.Text;
                        else if (string.IsNullOrEmpty(txtIPAddress.Text) && !string.IsNullOrEmpty(txtHostName1.Text))
                            address1 = txtHostName1.Text;
                        else
                            address1 = txtIPAddress.Text;
                        _licensevalidator = PtsLKey.verifyPTSLicenseKey(address1, "Application", lic1, "");

                        if (_licensevalidator)
                        {
                            lblErr.Visible = true;
                            _licensevalidator = true;
                        }
                        else
                        {
                            _licensevalidator = false;
                            lblErr.Text += "InValid License Key";
                            lblErr.Visible = true;
                        }

                    }
                    else if (txtLicenceKey.Text.Contains(" "))
                    {
                        _licensevalidator = false;
                        lblErr.Visible = true;
                        lblErr.Text += "Remove blank spaces from license key";
                        lblErr.CssClass = "error";
                    }
                    else if (ddlServerType.SelectedItem.Text == "PRDBServer" || ddlServerType.SelectedItem.Text == "DRDBServer")
                    {
                        if (lic1.Length > 40)
                        {
                            lic1 = lic1.Substring(0, 40);
                            string address2 = string.Empty;
                            if (!string.IsNullOrEmpty(txtIPAddress.Text) && !string.IsNullOrEmpty(txtHostName1.Text))
                                address2 = txtIPAddress.Text;
                            else if (string.IsNullOrEmpty(txtIPAddress.Text) && !string.IsNullOrEmpty(txtHostName1.Text))
                                address2 = txtHostName1.Text;
                            else
                                address2 = txtIPAddress.Text;
                            //string address2 = txtIPAddress.Text;
                            _licensevalidator = PtsLKey.verifyPTSLicenseKey(address2, "Database", lic1, "");
                            if (_licensevalidator)
                            {
                                _licensevalidator = true;
                            }
                            else
                            {
                                _licensevalidator = false;
                                lblErr.Visible = true;
                                lblErr.Text += "InValid License Key";
                                lblErr.CssClass = "error";
                            }
                        }
                        else
                        {
                            _licensevalidator = false;
                            lblErr.Visible = true;
                            lblErr.Text += "InValid License Key";
                            lblErr.CssClass = "error";
                        }
                    }
                    else
                    {
                        string encrpt = string.Empty;
                        string lic2 = txtLicenceKey.Text;
                        string HostName = txtHostName1.Text;

                        if (!string.IsNullOrEmpty(HostName))
                        {
                            if (ddlServerType.SelectedItem.Text == "DSCLIServer" || ddlServerType.SelectedItem.Text == "HMCServer" || ddlServerType.SelectedItem.Text == "PRAppServer" || ddlServerType.SelectedItem.Text == "DRAppServer" || ddlServerType.SelectedItem.Text == "PRESXIServer" || ddlServerType.SelectedItem.Text == "DRESXIServer" || ddlServerType.SelectedItem.Text == "DNSServer" || ddlServerType.SelectedItem.Text == "SymCLIServer")
                            {
                                if (!string.IsNullOrEmpty(txtIPAddress.Text) && !string.IsNullOrEmpty(txtHostName1.Text))
                                    address1 = txtIPAddress.Text;
                                else if (string.IsNullOrEmpty(txtIPAddress.Text) && !string.IsNullOrEmpty(txtHostName1.Text))
                                    address1 = txtHostName1.Text;
                                else
                                    address1 = txtIPAddress.Text;
                                _licensevalidator = PtsLKey.verifyPTSLicenseKey(address1, "Application", lic1, "");

                                if (_licensevalidator)
                                {
                                    lblErr.Visible = false;
                                    _licensevalidator = true;
                                }
                                else
                                {
                                    _licensevalidator = false;
                                    lblErr.Text += "InValid License Key";
                                    lblErr.Visible = true;
                                    lblErr.CssClass = "error";

                                }
                            }
                            else if (txtLicenceKey.Text.Contains(" "))
                            {
                                _licensevalidator = false;
                                lblErr.Visible = true;
                                lblErr.Text += "Remove blank spaces from license key";
                                lblErr.CssClass = "error";
                            }
                            else if (ddlServerType.SelectedItem.Text == "PRDBServer" || ddlServerType.SelectedItem.Text == "DRDBServer")
                            {
                                if (lic1.Length > 40)
                                {
                                    lic1 = lic1.Substring(0, 40);
                                    //string address2 = HostName;
                                    string address2 = string.Empty;
                                    if (!string.IsNullOrEmpty(txtIPAddress.Text) && !string.IsNullOrEmpty(txtHostName1.Text))
                                        address2 = txtIPAddress.Text;
                                    else if (string.IsNullOrEmpty(txtIPAddress.Text) && !string.IsNullOrEmpty(txtHostName1.Text))
                                        address2 = txtHostName1.Text;
                                    else
                                        address2 = txtIPAddress.Text;
                                    _licensevalidator = PtsLKey.verifyPTSLicenseKey(address2, "Database", lic1, "");
                                    if (_licensevalidator)
                                    {
                                        _licensevalidator = true;
                                    }
                                    else
                                    {
                                        _licensevalidator = false;
                                        lblErr.Visible = true;
                                        lblErr.Text += "InValid License Key";
                                        lblErr.CssClass = "error";
                                    }
                                }
                                else
                                {
                                    _licensevalidator = false;
                                    lblErr.Visible = true;
                                    lblErr.Text += "InValid License Key";
                                    lblErr.CssClass = "error";
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lblErr.Visible = true;
                lblErr.Text = "Invalid License Key";
                _licensevalidator = false;
            }
            return _licensevalidator;
        }


        #endregion

        protected void TxtLicenseTextChanged(object sender, EventArgs e)
        {
            LicenseKeyValidation();
            //try
            //{

            //    if (Session["SSHPassword"] != null)
            //    {
            //        txtSSHPassword.Attributes["value"] = Session["SSHPassword"].ToString();
            //    }

            //    string license = CryptographyHelper.Md5Decrypt(txtLicenceKey.Text);
            //    string ipaddress = txtIPAddress.Text;

            //    if (license.Contains(ipaddress))
            //    {
            //        lblErr.Visible = false;
            //    }
            //    else
            //    {
            //        lblErr.Visible = true;
            //        lblErr.Text = "Invalid License Key";
            //    }
            //}
            //catch (Exception ex)
            //{
            //    lblErr.Visible = true;
            //    lblErr.Text = "Invalid License Key";
            //}
        }

        private bool CheckSiteNameExist()
        {
            if (CurrentEntity.Id > 0)
            {
                if (txtHostName.Text.ToLower().Equals(CurrentEntity.Name.ToLower()))
                {
                    return false;
                }
            }

            return Facade.IsExistServerByName(txtHostName.Text);
        }
        protected void BtnSaveClick(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtIPAddress.Text) && string.IsNullOrEmpty(txtHostName1.Text))
            {
                lbltextid.Visible = true;
                return;
            }
            else
                lbltextid.Visible = false;

            var server = new Server();
            CheckValidation_PnlSudouser();
            if (LicenseKeyValidation() && !CheckSiteNameExist() && ValidateRequest("Server", UserActionType.CreateServerComponent))
            {
                //  CheckValidation_License();
                //CheckValidation_Credetials();
                if (_pnlsudovalidator == false)
                {
                    txtSudoUser.Focus();
                    lbltextid.Visible = true;
                }
                else if (_licensevalidator == false)
                {
                    lblErr.Visible = true;
                    // lblErr.Text = "Invalid License Key";
                }
                else
                {
                    // if (!Page.IsValid) return;
                    if (!Page.IsValid && (ViewState["_token"] == null) && CheckSiteNameExist() && !ValidateRequest("ServerConfiguration", UserActionType.CreateServerComponent)) return;

                    if (!ValidateInput())
                    {
                        string returnUrl1 = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl1);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {

                        var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                        if (returnUrl.IsNullOrEmpty())
                        {
                            returnUrl = ReturnUrl;
                        }
                        var submitButton = (Button)sender;
                        var buttionText = " " + submitButton.Text.ToLower() + " ";
                        var currentTransactionType = TransactionType.Undefined;
                        if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                        {
                            currentTransactionType = TransactionType.Save;
                        }
                        else if (buttionText.Contains(" update "))
                        {
                            currentTransactionType = TransactionType.Update;
                        }

                        try
                        {
                            if (currentTransactionType != TransactionType.Undefined)
                            {
                                BuildEntities();
                                StartTransaction();
                                SaveEditor();
                                EndTransaction();
                                Session["SubsAuthList"] = null;
                            }
                            string message = MessageInitials + " " + '"' + CurrentEntity.Name + '"';
                            ErrorSuccessNotifier.AddSuccessMessage(
                                Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                    currentTransactionType));
                        }
                        catch (CpException ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            ExceptionManager.Manage(ex, this);
                        }
                        catch (Exception ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                            {
                                ExceptionManager.Manage((CpException)ex.InnerException, this);
                            }
                            else
                            {
                                var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                    "Unhandled exception occurred while saving data", ex);

                                ExceptionManager.Manage(customEx, this);
                            }
                        }
                        //if (returnUrl.IsNotNullOrEmpty())
                        //{
                        //    Helper.Url.Redirect(new SecureUrl(returnUrl));
                        //}
                        if (returnUrl.IsNotNullOrEmpty())
                        {

                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ServerId, CurrentEntity.Id);

                            Helper.Url.Redirect(returnUrl);
                        }
                    }
                }
            }
        }

        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.SereverList);
        }

        //protected void BtnTestConnectionClick(object sender, EventArgs e)
        //{
        //    spnRunning.Visible = true;
        //    if (CheckValidation_Credetials())
        //    {
        //        spnRunning.Visible = false;
        //        lblValidateConnection.Visible = true;
        //        lblValidateConnection.Text = "Connection is Valid";
        //    }
        //    else
        //    {
        //        spnRunning.Visible = false;
        //        lblValidateConnection.Visible = true;
        //        lblValidateConnection.Text = "Connection is Invalid";
        //    }
        //}

        protected void ChkClusterCheckedChanged(object sender, EventArgs e)
        {
            ipAddress.InnerText = chkCluster.Checked ? "Virtual IP Address" : "IP Address";
        }

        protected void DdlServerTypeSelectedIndexChanged(object sender, EventArgs e)
        {
            lblErr.Visible = false;
            switch (ddlServerType.SelectedValue)
            {
                case "HMCServer":
                    lblSSHUser.Text = "HMC User";
                    lblSSHPassword.Text = "HMC Password";
                    panelVmDetails.Visible = false;
                    break;

                case "DSCLIServer":
                    lblSSHUser.Text = "DSCLI User";
                    lblSSHPassword.Text = "DSCLI Password";
                    txtSudoUserPassword.Text = string.Empty;
                    panelVmDetails.Visible = false;
                    break;

                case "PRESXIServer":
                    lblSSHUser.Text = "User";
                    lblSSHPassword.Text = "Password";
                    panelVmDetails.Visible = true;
                    break;

                case "DRESXIServer":
                    lblSSHUser.Text = "User";
                    lblSSHPassword.Text = "Password";
                    panelVmDetails.Visible = true;
                    break;

                case "DNSServer":
                    lblSSHUser.Text = "DNSServer User";
                    lblSSHPassword.Text = "DNSServer Password";
                    panelVmDetails.Visible = false;
                    break;

                default:
                    lblSSHUser.Text = "SSH User";
                    lblSSHPassword.Text = "SSH Password";
                    rfvtxtSSHPassword.Enabled = true;
                    lblSshStatus.Text = "*";
                    panelVmDetails.Visible = false;
                    break;
            }
        }

        //protected void ChkSudoCheckedChanged(object sender, EventArgs e)
        //{
        //    if (Convert.ToInt32(ddlSudosu.SelectedValue) > 0)
        //        pnlSudo.Visible = true;

        //    if (Convert.ToInt32(ddlSudosu.SelectedValue) <= 0)
        //    {
        //        txtSudoUser.Text = string.Empty;
        //    }
        //    if (Session["SSHPassword"] != null)
        //        txtSSHPassword.Attributes["value"] = Convert.ToString(Session["SSHPassword"]);

        //    if (Session["SSHKeyPassword"] != null)
        //        txtSSHKeyPassword.Attributes["value"] = Convert.ToString(Session["SSHKeyPassword"]);
        //}
        protected void ChkSudoCheckedChanged(object sender, EventArgs e)
        {
            if (Session["SSHPassword"] != null)
                txtSSHPassword.Attributes["value"] = Session["SSHPassword"].ToString();

            if (Session["SSHKeyPassword"] != null)
                txtSSHKeyPassword.Attributes["value"] = Session["SSHKeyPassword"].ToString();
        }
        //private bool CheckValidation_PnlSudouser()
        //{
        //    if ((Convert.ToInt32(ddlSudosu.SelectedValue) > 0) && txtSudoUser.Text == string.Empty)
        //    {
        //        _pnlsudovalidator = false;
        //        LblsudouserError.Visible = true;
        //    }
        //    return _pnlsudovalidator;
        //}
        private bool CheckValidation_PnlSudouser()
        {
            return _pnlsudovalidator;
        }

        private bool CheckValidation_License()
        {
            try
            {
                string license = CryptographyHelper.Md5Decrypt(txtLicenceKey.Text);
                string ipaddress = txtIPAddress.Text;

                if (license.Contains(ipaddress))
                {
                    _licensevalidator = true;
                }
                else
                {
                    _licensevalidator = false;
                }
            }
            catch (Exception ex)
            {
                lblErr.Visible = true;
                lblErr.Text = "Invalid License Key";

                _licensevalidator = false;
            }

            return _licensevalidator;
        }

        private void AuthenticationType(string OsType)
        {
            if (OsType.Equals("5") || OsType.Equals("6") || OsType.Equals("7") || OsType.Equals("8") || OsType.Equals("9") || OsType.Equals("10") || OsType.Equals("11"))
            {
                ddlAuthenticationType.Items.Clear();
                ddlAuthenticationType.Items.Add(new ListItem("-Select Authentication Type-"));
                ddlAuthenticationType.Items.Add(new ListItem("SSH Password", "0"));
                ddlAuthenticationType.Items.Add(new ListItem(" SSH Key", "1"));
                ddlAuthenticationType.Items.Add(new ListItem("WMI", "2"));
                ddlAuthenticationType.Items.Add(new ListItem("PowerShell", "3"));
            }
            else
            {
                ddlAuthenticationType.Items.Clear();
                ddlAuthenticationType.Items.Add(new ListItem("-Select Authentication Type-"));
                ddlAuthenticationType.Items.Add(new ListItem("SSH Password", "0"));
                ddlAuthenticationType.Items.Add(new ListItem(" SSH Key", "1"));

            }
        }

        [WebMethod]
        public static string CheckValidation_Credetials(string ipaddress, string sshuser, string sshpassword)
        {
            return SSHHelper.Connect(ipaddress, sshuser, sshpassword, "", "");
        }

        protected void TxtSSHPasswordTextChanged(object sender, EventArgs e)
        {
            Session["SSHPassword"] = null;

            Session["SSHPassword"] = txtSSHPassword.Text;


        }

        protected void txtSSHKeyPasswordTextChanged(object sender, EventArgs e)
        {
            Session["SSHPassword"] = null;

            Session["SSHKeyPassword"] = txtSSHKeyPassword.Text;
        }

        protected void txtSudoUser_TextChanged(object sender, EventArgs e)
        {
            //   if (txtSudoUser.Text == string.Empty) return;
            if (txtSudoUser.Text != string.Empty)
            {
                lbltextid.Visible = false;
                lbltextid.Text = "";
            }
            else
            {
                lbltextid.Visible = true;
                lbltextid.Text = "Enter UserName";
            }
        }

        protected void ChkSSOEnableCheckedChanged(object sender, EventArgs e)
        {
            if (ChkSSOEnable.Checked)
            {
                pnlSSOType.Visible = true;
                dsSSHPassword.Visible = false;
                pnlsshkeypath.Visible = false;
                pnlSSoProfilrDrp.Visible = true;
            }
            else
            {
                pnlSSOType.Visible = false;
                pnlSSoProfilrDrp.Visible = false;
                ddlSignOnType.SelectedValue = Convert.ToString(0);
                ddlSignOnType_SelectedIndexChanged(null, null);
                ddlAuthenticationType_SelectedIndexChanged(null, null);
                //sshkeypassword = txtSSHKeyPassword.Text;
            }
        }

        protected void ddlOS_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlOS.SelectedIndex > 5)
            {
                // ddlSudosu.SelectedValue = "0";
                pnlSudo.Visible = false;
                // ddlSudosu.Visible = false;
                // chkSudo.Visible = false;
                chkSubsAuth.Visible = false;
                lblAuthentication.Visible = false;
                pnlPowerShell.Visible = false;
            }
            else
            {
                //chkSudo.Visible = true;
                //  ddlSudosu.Visible = true;
                //Label2.Visible = false;
                chkSubsAuth.Visible = true;
                lblAuthentication.Visible = true;
                pnlPowerShell.Visible = false;
            }

            AuthenticationType(ddlOS.SelectedValue);
            if (ddlAuthenticationType.SelectedValue.Equals("-Select Authentication Type-"))
            {
                dSSHKeyPassword.Visible = false;
                dSSHKeyPath.Visible = false;
                dvUser.Visible = true;
                dsSSHPassword.Visible = true;
                lblSSHUser.Text = "SSH  User";
                lblSSHPassword.Text = "SSH Password";
                rfvtxtSSHPassword.Enabled = true;
                lblSshStatus.Text = "*";

            }
        }

        //protected void ddlSudosu_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //    if (Convert.ToInt32(ddlSudosu.SelectedValue) > 0)
        //        pnlSudo.Visible = true;

        //    if (Convert.ToInt32(ddlSudosu.SelectedValue) <= 0)
        //    {
        //        txtSudoUser.Text = string.Empty;
        //        pnlSudo.Visible = false;
        //    }
        //    if (Session["SSHPassword"] != null)
        //    {
        //        txtSSHPassword.Attributes["value"] = Convert.ToString(Session["SSHPassword"]);
        //    }
        //    if (ddlSudosu.SelectedValue == "2")
        //    {
        //        lblSudo.Text = "Su User";
        //        lblsupassword.Text = "Su User Password";
        //        //chkSuUserPass.Visible = true;
        //        //lblsupassword.Visible = false;
        //        //txtSudoUserPassword.Visible = false;
        //    }
        //    else
        //    {
        //        lblSudo.Text = "Sudo User";
        //        lblsupassword.Text = "Sudo User Password";
        //        //chkSuUserPass.Visible = false;
        //        //chkSuUserPass.Checked = false;
        //        //lblsupassword.Visible = true;
        //        //txtSudoUserPassword.Visible = true;
        //    }
        //    if (Session["SSHKeyPassword"] != null)
        //        txtSSHKeyPassword.Attributes["value"] = Convert.ToString(Session["SSHKeyPassword"]);


        //}

        protected void ddlSudosu_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (Session["SSHKeyPassword"] != null)
                txtSSHKeyPassword.Attributes["value"] = Session["SSHKeyPassword"].ToString();
        }
        protected void ddlAuthenticationType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlAuthenticationType.SelectedValue.Equals("0"))
            {
                if (!ChkSSOEnable.Checked)
                {
                    dSSHKeyPassword.Visible = false;
                    dSSHKeyPath.Visible = false;
                    dvUser.Visible = true;
                    dsSSHPassword.Visible = true;
                    lblSSHUser.Text = "SSH  User";
                    lblSSHPassword.Text = "SSH Password";
                    rfvtxtSSHPassword.Enabled = true;
                    lblSshStatus.Text = "*";

                }
                else
                {
                    //dsSSHPassword.Visible = false;
                    dvUser.Visible = true;
                    lblSSHUser.Text = "SSH  User";
                    lblSSHPassword.Text = "SSH Password";
                    rfvtxtSSHPassword.Enabled = true;
                    lblSshStatus.Text = "*";
                }
            }
            else if (ddlAuthenticationType.SelectedValue.Equals("1"))
            {

                if (!ChkSSOEnable.Checked)
                {
                    pnlsshkeypath.Visible = true;
                    dSSHKeyPassword.Visible = true;
                    dvUser.Visible = true;
                    dsSSHPassword.Visible = false;
                    dSSHKeyPath.Visible = true;
                    lblSSHUser.Text = "SSH Key User";
                    //rfvtxtSSHKeyPassword.Enabled = true;
                    //lblSSHPassword.Text = "SSH Key Password";
                }
                else
                {
                    //pnlsshkeypath.Visible = false;
                    lblSSHUser.Text = "SSH Key User";
                    //rfvtxtSSHKeyPassword.Enabled = false;
                }
            }
            else if (ddlAuthenticationType.SelectedValue.Equals("2"))
            {
                pnlPowerShell.Visible = false;
                dSSHKeyPassword.Visible = false;
                dSSHKeyPath.Visible = false;
                dvUser.Visible = true;
                dsSSHPassword.Visible = true;
                lblSSHUser.Text = "WMI User";
                lblSSHPassword.Text = "WMI Password";
            }
            else if (ddlAuthenticationType.SelectedValue.Equals("3"))
            {
                pnlPowerShell.Visible = true;
                dSSHKeyPassword.Visible = false;
                dSSHKeyPath.Visible = false;
                dvUser.Visible = true;
                dsSSHPassword.Visible = true;
                lblSSHUser.Text = "PowerShell  User";
                lblSSHPassword.Text = "PowerShell Password";
            }
            else
            {
                dSSHKeyPassword.Visible = false;
                dSSHKeyPath.Visible = false;
                dvUser.Visible = true;
                dsSSHPassword.Visible = true;
                lblSSHUser.Text = "SSH  User";
                lblSSHPassword.Text = "SSH Password";
                rfvtxtSSHPassword.Enabled = true;
                lblSshStatus.Text = "*";

            }
        }

        protected void ddlSignOnType_SelectedIndexChanged(object sender, EventArgs e)
        {
            dsSSHPassword.Visible = false;
            Utility.PopulateSSOProfile(ddlSSOProfile, true, Convert.ToInt32(ddlSignOnType.SelectedValue));
            pnlCyberark.Visible = (ddlSignOnType.SelectedValue == "2") ? true : false;
            //CurrentEntity.SSOTypeId
        }

        protected void ddlServerRole_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        protected void chkIsASM_CheckedChanged(object sender, EventArgs e)
        {
            if (chkIsASM.Checked == true)
            {
                divASMInstance.Visible = true;
                updateISASM.Update();
            }
            else
            {
                divASMInstance.Visible = false;
                updateISASM.Update();
            }
        }

        protected void ddlASMInstance_SelectedIndexChanged(object sender, EventArgs e)
        {

        }
        /*  protected void chkSSHKey_CheckedChanged(object sender, EventArgs e)
          {
              Session["SSHKeyPassword"] = null;
              if (chkSSHKey.Checked)
              {
                  dSSHKeyPath.Visible = true;
                  dSSHKeyPassword.Visible = true;
                  rfvtxtSSHKeyPath.Enabled = true;
                  //rfvtxtSSHKeyPassword.Enabled = true;
                  dsSSHPassword.Visible = false;
                  rfvtxtSSHPassword.Enabled = false;
                  txtSSHPassword.Text = string.Empty;
              }
              else
              {
                  dSSHKeyPath.Visible = false;
                  dSSHKeyPassword.Visible = false;
                  rfvtxtSSHKeyPath.Enabled = false;
                  // rfvtxtSSHKeyPassword.Enabled = false;
                  dsSSHPassword.Visible = true;
                  rfvtxtSSHPassword.Enabled = true;
              }
          }*/

        //protected void chkSuUserPass_CheckedChanged(object sender, EventArgs e)
        //{
        //   if(chkSuUserPass.Checked)
        //   {
        //       lblsupassword.Text = "Su User Password";
        //       lblsupassword.Visible = true;
        //       txtSudoUserPassword.Visible = true;

        //   }
        //   else
        //   {
        //       lblsupassword.Visible = false;
        //       txtSudoUserPassword.Visible = false;

        //   }
        //}

        //protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        //{
        //    if ((ViewState["_token"] != null))
        //    {
        //        // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
        //        if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
        //        {
        //            ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
        //            Logout _Logout = new Logout();
        //            _Logout.PrepareView();
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
        //        Logout _Logout = new Logout();
        //        _Logout.PrepareView();
        //        return false;
        //    }
        //    return true;
        //}
    }
}