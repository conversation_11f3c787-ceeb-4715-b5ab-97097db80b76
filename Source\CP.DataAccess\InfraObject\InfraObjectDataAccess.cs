﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class InfraObjectDataAccess : BaseDataAccess, IInfraObjectDataAccess
    {
        #region Constructors

        public InfraObjectDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<InfraObject> CreateEntityBuilder<InfraObject>()
        {
            return (new InfraObjectBuilder()) as IEntityBuilder<InfraObject>;
        }

        #endregion Constructors

        #region Methods

        InfraObject IInfraObjectDataAccess.Add(InfraObject infraObject)
        {
            try
            {
                const string sp = "InfraObject_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, infraObject.Name);
                    Database.AddInParameter(cmd, Dbstring + "iDescription", DbType.AnsiString, infraObject.Description);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, infraObject.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessFunctionId", DbType.Int32, infraObject.BusinessFunctionId);
                    Database.AddInParameter(cmd, Dbstring + "iDRReady", DbType.Int32, infraObject.DRReady);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.Int32, infraObject.Type);
                    Database.AddInParameter(cmd, Dbstring + "iSubType", DbType.Int32, infraObject.SubType);
                    Database.AddInParameter(cmd, Dbstring + "iRecoveryType", DbType.Int32, infraObject.RecoveryType);
                    Database.AddInParameter(cmd, Dbstring + "iPRServerId", DbType.Int32, infraObject.PRServerId);
                    Database.AddInParameter(cmd, Dbstring + "iDRServerId", DbType.Int32, infraObject.DRServerId);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseId", DbType.Int32, infraObject.PRDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseId", DbType.Int32, infraObject.DRDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iPRReplicationId", DbType.Int32, infraObject.PRReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iDRReplicationId", DbType.Int32, infraObject.DRReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iPriority", DbType.Int32, infraObject.Priority);
                    Database.AddInParameter(cmd, Dbstring + "iState", DbType.AnsiString, infraObject.State);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationStatus", DbType.Int32, infraObject.ReplicationStatus);
                    Database.AddInParameter(cmd, Dbstring + "iSiteSolutionTypeId", DbType.Int32, infraObject.SiteSolutionTypeId);
                    Database.AddInParameter(cmd, Dbstring + "iNearGroupId", DbType.Int32, infraObject.NearGroupId);
                    Database.AddInParameter(cmd, Dbstring + "iMonitoringWorkflow", DbType.Int32, infraObject.MonitoringWorkflow);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, infraObject.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iDrMonitorApplicationCheck", DbType.Int32, infraObject.DrMonitorApplicationCheck);
                    Database.AddInParameter(cmd, Dbstring + "iDrMonitoringWorkflow", DbType.Int32, infraObject.DrMonitoringWorkflow);
                    Database.AddInParameter(cmd, Dbstring + "iPRServerId2", DbType.Int32, infraObject.PRServerId2);
                    Database.AddInParameter(cmd, Dbstring + "iDRServerId2", DbType.Int32, infraObject.DRServerId2);
                    Database.AddInParameter(cmd, Dbstring + "iIsPair", DbType.Int32, infraObject.IsPair);
                    Database.AddInParameter(cmd, Dbstring + "iPairInfraObjectId", DbType.Int32, infraObject.PairInfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iIsAssociate", DbType.Int32, infraObject.IsAssociate);
                    Database.AddInParameter(cmd, Dbstring + "iClusterPRServerId", DbType.Int32, infraObject.ClusterPRServerId);
                    Database.AddInParameter(cmd, Dbstring + "iClusterDRServerId", DbType.Int32, infraObject.ClusterDRServerId);
                    Database.AddInParameter(cmd, Dbstring + "iIsCluster", DbType.Int32, infraObject.IsCluster);
                    Database.AddInParameter(cmd, Dbstring + "iClusterName", DbType.AnsiString, infraObject.ClusterName);
                    Database.AddInParameter(cmd, Dbstring + "iIsPRCloud", DbType.Int32, infraObject.IsPRCloud);
                    Database.AddInParameter(cmd, Dbstring + "iIsDRCloud", DbType.Int32, infraObject.IsDRCloud);
                    Database.AddInParameter(cmd, Dbstring + "iIsQueueMonitor", DbType.Int32, infraObject.IsQueueMonitor);
                    Database.AddInParameter(cmd, Dbstring + "iIsPRHostName", DbType.Int32, infraObject.IsPRHostName);
                    Database.AddInParameter(cmd, Dbstring + "iIsDRHostName", DbType.Int32, infraObject.IsDRHostName);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        infraObject = reader.Read() ? CreateEntityBuilder<InfraObject>().BuildEntity(reader, infraObject) : null;
                    }

                    if (infraObject == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("InfraObject already exists. Please specify another infraobject.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this infraobject.");
                                }
                        }
                    }

                    return infraObject;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation, ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.Add"
                     , exc);
            }
        }

        InfraObject IInfraObjectDataAccess.Update(InfraObject infraObject)
        {
            try
            {
                const string sp = "InfraObject_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, infraObject.Id);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, infraObject.Name);
                    Database.AddInParameter(cmd, Dbstring + "iDescription", DbType.AnsiString, infraObject.Description);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, infraObject.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessFunctionId", DbType.Int32, infraObject.BusinessFunctionId);
                    Database.AddInParameter(cmd, Dbstring + "iDRReady", DbType.Int32, infraObject.DRReady);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.Int32, infraObject.Type);
                    Database.AddInParameter(cmd, Dbstring + "iSubType", DbType.Int32, infraObject.SubType);
                    Database.AddInParameter(cmd, Dbstring + "iRecoveryType", DbType.Int32, infraObject.RecoveryType);
                    Database.AddInParameter(cmd, Dbstring + "iPRServerId", DbType.Int32, infraObject.PRServerId);
                    Database.AddInParameter(cmd, Dbstring + "iDRServerId", DbType.Int32, infraObject.DRServerId);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseId", DbType.Int32, infraObject.PRDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseId", DbType.Int32, infraObject.DRDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iPRReplicationId", DbType.Int32, infraObject.PRReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iDRReplicationId", DbType.Int32, infraObject.DRReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iPriority", DbType.Int32, infraObject.Priority);
                    Database.AddInParameter(cmd, Dbstring + "iState", DbType.AnsiString, infraObject.State);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationStatus", DbType.Int32, infraObject.ReplicationStatus);
                    Database.AddInParameter(cmd, Dbstring + "iSiteSolutionTypeId", DbType.Int32, infraObject.SiteSolutionTypeId);
                    Database.AddInParameter(cmd, Dbstring + "iNearGroupId", DbType.Int32, infraObject.NearGroupId);
                    Database.AddInParameter(cmd, Dbstring + "iMonitoringWorkflow", DbType.Int32, infraObject.MonitoringWorkflow);
                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, infraObject.IsActive);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, infraObject.UpdatorId);
                    Database.AddInParameter(cmd, Dbstring + "iDrMonitorApplicationCheck", DbType.Int32, infraObject.DrMonitorApplicationCheck);
                    Database.AddInParameter(cmd, Dbstring + "iDrMonitoringWorkflow", DbType.Int32, infraObject.DrMonitoringWorkflow);
                    //Custom Changes
                    Database.AddInParameter(cmd, Dbstring + "iPRServerId2", DbType.Int32, infraObject.PRServerId2);
                    Database.AddInParameter(cmd, Dbstring + "iDRServerId2", DbType.Int32, infraObject.DRServerId2);
                    Database.AddInParameter(cmd, Dbstring + "iIsPair", DbType.Int32, infraObject.IsPair);
                    Database.AddInParameter(cmd, Dbstring + "iPairInfraObjectId", DbType.Int32, infraObject.PairInfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iIsAssociate", DbType.Int32, infraObject.IsAssociate);

                    Database.AddInParameter(cmd, Dbstring + "iClusterPRServerId", DbType.Int32, infraObject.ClusterPRServerId);
                    Database.AddInParameter(cmd, Dbstring + "iClusterDRServerId", DbType.Int32, infraObject.ClusterDRServerId);
                    Database.AddInParameter(cmd, Dbstring + "iIsCluster", DbType.Int32, infraObject.IsCluster);
                    Database.AddInParameter(cmd, Dbstring + "iClusterName", DbType.AnsiString, infraObject.ClusterName);
                    Database.AddInParameter(cmd, Dbstring + "iIsPRCloud", DbType.Int32, infraObject.IsPRCloud);
                    Database.AddInParameter(cmd, Dbstring + "iIsDRCloud", DbType.Int32, infraObject.IsDRCloud);
                    Database.AddInParameter(cmd, Dbstring + "iIsQueueMonitor", DbType.Int32, infraObject.IsQueueMonitor);
                    Database.AddInParameter(cmd, Dbstring + "iIsPRHostName", DbType.Int32, infraObject.IsPRHostName);
                    Database.AddInParameter(cmd, Dbstring + "iIsDRHostName", DbType.Int32, infraObject.IsDRHostName);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        infraObject = reader.Read() ? CreateEntityBuilder<InfraObject>().BuildEntity(reader, infraObject) : null;
                    }

                    if (infraObject == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Group already exists. Please specify another group.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while updating this group.");
                                }
                        }
                    }

                    return infraObject;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation, ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.Update"
                     , exc);
            }
        }

        InfraObject IInfraObjectDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObject_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<InfraObject>()).BuildEntity(reader, new InfraObject()) : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetById(" + id + ")", exc);
            }
        }

        InfraObject IInfraObjectDataAccess.GetInfraObjectByPRReplicationId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObject_GetByPRRepliId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iReplicationId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<InfraObject>()).BuildEntity(reader, new InfraObject()) : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetById(" + id + ")", exc);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetByBusinessServiceIdAndBusinessFunctionId(int businessServiceId, int businessFunctionId)
        {
            try
            {
                //if (businessServiceId < 1 || businessFunctionId < 1)
                //{
                if (businessServiceId < 1 && businessFunctionId < 1)
                {
                    throw new ArgumentNullException("businessServiceId,businessFunctionId");
                }

                const string sp = "InfraObject_BsnSerIdnBsnFuncId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int16, businessServiceId);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessFunctionId", DbType.Int16, businessFunctionId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetByBusinessServiceIdAndBusinessFunctionId(" + businessServiceId + " " + businessFunctionId + ")", exc);
            }
        }

        InfraObject IInfraObjectDataAccess.GetByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "InfraObject_GetByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<InfraObject>()).BuildEntity(reader, new InfraObject()) : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetByName(" + name + ")", exc);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetAll()
        {
            try
            {
                const string sp = "InfraObject_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetAll", exc);
            }
        }

        bool IInfraObjectDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObject_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, id);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeSuccessDelete:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeErrorChildExists:
                            {
                                throw new ArgumentException("Cannot delete a infraobject which has association.");
                            }
                        default:
                            {
                                throw new SystemException("An unexpected error has occurred while deleting this infraobject.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation, ExceptionManager.CommonMessage.UserAlertMessageDeletedata, "Error In DAL While Deleting InfraObject Entry ", ex);
            }
        }

        bool IInfraObjectDataAccess.IsExistByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "InfraObject_IsExistByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException("An unexpected error has occurred while deleting this companyProfile.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata, "Error In DAL While Executing Function Signature IInfraObjectDataAccess.IsExistByName (" + name + ")", ex);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetByLoginId(int id)
        {
            try
            {
                const string sp = "InfraObject_GetByLoginId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IGroupDataAccess.GetByLoginId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetByLoggedInUserId(int userId)
        {
            try
            {
                const string sp = "InfraObject_GetByLogdInUsrId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, userId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IGroupDataAccess.GetByLoggedInUserId(" + userId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetAllInfraObjectIdRPT()
        {
            try
            {
                const string sp = "InfraObject_GetAllIDRPT";

                var ListInfra = new List<InfraObject>();

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                        while (reader.Read())
                        {
                            InfraObject inf = new InfraObject()
                            {
                                Id = Convert.IsDBNull(reader["InfraId"]) ? 0 : Convert.ToInt32(reader["InfraId"]),
                                Name = Convert.IsDBNull(reader["InfraName"]) ? string.Empty : Convert.ToString(reader["InfraName"]),
                                RecoveryType = Convert.IsDBNull(reader["RecoveryType"]) ? 0 : Convert.ToInt32(reader["RecoveryType"]),
                                BusinessServiceId = Convert.IsDBNull(reader["BusinessServiceId"]) ? 0 : Convert.ToInt32(reader["BusinessServiceId"])

                            };

                            ListInfra.Add(inf);

                        }
                    }
                }
                return ListInfra;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetAllInfraObjectIdRPT()", exc);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetAllByCompanyId(int companyId, bool isParent)
        {
            try
            {
                const string sp = "InfraObject_GetbyUserCompanyId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.AnsiString, companyId);
                    //Database.AddInParameter(cmd, Dbstring+"iIsParent", DbType.Int32, isParent);
                    Database.AddInParameter(cmd, Dbstring + "iIsParent", DbType.Int32, Convert.ToInt32(isParent));

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetByCompanyId(" + companyId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        bool IInfraObjectDataAccess.DeleteGlobalMirrorInfraObjectById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObject_DltGblMiorInfrById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting InfraObjects Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        bool IInfraObjectDataAccess.DeleteHitachiInfraObjectById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObject_DeleteHitachiInfraObjectById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting InfraObject Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetByBusinessServiceId(int businessServiceId)
        {
            try
            {
                if (businessServiceId < 1)
                {
                    throw new ArgumentNullException("businessServiceId");
                }

                const string sp = "InfraObject_GetByBsnesSrvcId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int16, businessServiceId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetByBusinessServiceId(" + businessServiceId + ")", exc);
            }
        }

        /// <summary>
        ///     update <see cref="InfraObject" /> into infraObject table by infraObjectId,state and replStatus.
        /// </summary>
        /// <param name="infraObjectId">infraObjectId</param>
        /// <param name="state">state</param>
        /// <param name="replStatus">replStatus</param>
        /// <returns>bool</returns>
        /// <author>Kuntesh Thakker </author>
        bool IInfraObjectDataAccess.UpdateByState(int infraObjectId, string state, int replStatus)
        {
            try
            {
                if (infraObjectId < 1)
                {
                    throw new ArgumentNullException("infraObjectId");
                }
                if (state == string.Empty)
                {
                    throw new ArgumentNullException("state");
                }
                const string sp = "InfraObject_UpdateByState";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "istate", DbType.AnsiString, state);
                    Database.AddInParameter(cmd, Dbstring + "ireplStatus", DbType.Int32, replStatus);
                    int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return returnCode < 0;
#else
                    return returnCode > 0;
#endif
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Infra Object Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        /// <summary>
        ///     update <see cref="InfraObject" /> into infraObject table by state,repl and infraapplicationId.
        /// </summary>
        /// <param name="state">Pass Id of the infraobject </param>
        /// <param name="repl">Pass replication id</param>
        /// <param name="infraapplicationId">Pass application id</param>
        /// <returns>bool</returns>
        /// <author>Kuntesh Thakker </author>
        bool IInfraObjectDataAccess.UpdateAllByState(string state, int repl, int infraapplicationId)
        {
            try
            {
                if (state == string.Empty)
                {
                    throw new ArgumentNullException("state");
                }
                const string sp = "InfraObject_UpdateAllByState";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "istate", DbType.AnsiString, state);
                    Database.AddInParameter(cmd, Dbstring + "irepl", DbType.Int32, repl);
                    Database.AddInParameter(cmd, Dbstring + "iInfraapplicationId", DbType.Int32, infraapplicationId);

                    int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return returnCode < 0;
#else
                    return returnCode > 0;
#endif
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Infra Object Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        /// <summary>
        ///     update <see cref="InfraObject" /> into infraObject table by infraObjectId and EnableDataSync.
        /// </summary>
        /// <param name="infraObjectId">Pass Id of the infraObject </param>
        /// <param name="enableDataSync">Pass EnableDataSync</param>
        /// <returns>bool</returns>
        /// <author>Kuntesh Thakker </author>
        bool IInfraObjectDataAccess.UpdateEnableDataSync(int infraObjectId, bool enableDataSync)
        {
            try
            {
                if (infraObjectId < 1)
                {
                    throw new ArgumentNullException("infraObjectId");
                }

                const string sp = "InfraObject_UpdateByEnableDataSync";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iEnableDataSync", DbType.Int32, enableDataSync);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Infra Object Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        bool IInfraObjectDataAccess.UnlockByTime(int infraObjectId, int minutes, string eventName)
        {
#if ORACLE
            return true;
#else
            try
            {
                string sp =
                    string.Format(
                        "CREATE EVENT {0} ON SCHEDULE at now() + INTERVAL {1} MINUTE DO update infraobject set state = 'Active' where  id = {2}",
                        eventName, minutes, infraObjectId);
                using (DbCommand cmd = Database.GetSqlStringCommand(sp))
                {
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceDataAccess.UnlockByTime (" +
                    infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
#endif
        }

        InfraObject IInfraObjectDataAccess.GetByServerId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObject_GetByServerId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<InfraObject>()).BuildEntity(reader, new InfraObject());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetByServerId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetByServerId_New(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "InfraObject_GetByServerId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetByServerId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        IList<InfraObject> IInfraObjectDataAccess.GetInfraObjectByDatabaseId(int databaseId)
        {
            try
            {
                const string sp = "INFRAOBJECT_GETBYDBID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseId", DbType.Int32, databaseId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IGroupDataAccess.GetByDatabaseId(" + databaseId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetAllByRole(int id)
        {
            try
            {
                const string sp = "ALERT_GETALERTBYROLE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IGroupDataAccess.GetByLoginId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetInfraByStorageImageId(string storageImageId)
        {
            try
            {
                IList<InfraObject> group = new List<InfraObject>();
                const string sp = "INFRAOBJECTJOB_GETBYSTRGEIMGID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iStorageImageId", DbType.String, storageImageId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var gp = new InfraObject { Name = Convert.ToString(reader[1]) };

                            group.Add(gp);
                        }
                        return group;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetInfraByStorageImageId((" +
                    storageImageId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetInfraobjectByReplicationId(int ReplicationId)
        {
            try
            {


                const string sp = "InfraObject_GetByReplicationId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iReplicationId", DbType.Int16, ReplicationId);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetInfraobjectByReplicationId(" + ReplicationId + ")", exc);
            }

        }

        IList<InfraObject> IInfraObjectDataAccess.GetIOByServerId(int serverId)
        {
            try
            {


                const string sp = "InfraObjects_GetByServerId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iID", DbType.Int16, serverId);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetByServerId(" + serverId + ")", exc);
            }

        }

        InfraObject IInfraObjectDataAccess.AddInfraId(InfraObject infraObject)
        {
            try
            {
                const string sp = "InfraObjectId_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, infraObject.Id);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.String, infraObject.Name);
                    Database.AddInParameter(cmd, Dbstring + "iRecoveryType", DbType.Int32, infraObject.RecoveryType);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, infraObject.BusinessServiceId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        infraObject = reader.Read() ? CreateEntityBuilder<InfraObject>().BuildEntity(reader, infraObject) : null;
                    }

                    return infraObject;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation, ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.AddInfraId"
                     , exc);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetReplicationId(int ReplicationId)
        {
            try
            {


                const string sp = "InfraObject_GetReplicationId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iRepId", DbType.Int16, ReplicationId);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetInfraobjectByReplicationId(" + ReplicationId + ")", exc);
            }

        }

        IList<InfraObject> IInfraObjectDataAccess.GetJobByReplicationId(int ReplicationId)
        {
            try
            {


                const string sp = "InfraJob_GetByRepId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iRepId", DbType.Int16, ReplicationId);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetInfraobjectByReplicationId(" + ReplicationId + ")", exc);
            }

        }

        IList<InfraObject> IInfraObjectDataAccess.GetByBusinessServiceIdAndBusinessFunctionIdAndUserId(int businessServiceId, int businessFunctionId, int LoggedInUserId)
        {
            try
            {
                if (businessServiceId < 1 || businessFunctionId < 1)
                {
                    throw new ArgumentNullException("businessServiceId,businessFunctionId");
                }

                const string sp = "Infraobject_GetByBSBFUserId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iServiceId", DbType.Int32, businessServiceId);
                    Database.AddInParameter(cmd, Dbstring + "iFunctionId", DbType.Int32, businessFunctionId);
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, LoggedInUserId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetByBusinessServiceIdAndBusinessFunctionId(" + businessServiceId + " " + businessFunctionId + ")", exc);
            }
        }

        IList<InfraObject> IInfraObjectDataAccess.GetInfraObjectByBusinessServiceIdUserId(int businessServiceId, int LoggedInUserId)
        {
            try
            {
                if (businessServiceId < 1)
                {
                    throw new ArgumentNullException("businessServiceId");
                }

                const string sp = "Infraobject_GetByBSUserId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iServiceId", DbType.Int32, businessServiceId);
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, LoggedInUserId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfraObject>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetByBusinessServiceIdAndBusinessFunctionId(" + businessServiceId + ")", exc);
            }
        }

        bool IInfraObjectDataAccess.DeleteByIdTemp()
        {
            try
            {
                //if (id < 1)
                //{
                //    throw new ArgumentNullException("id");
                //}

                const string sp = "InfraTemp_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    //Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting Application Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        int IInfraObjectDataAccess.GetServiceCountDRReadyCurrentMonth()
        {
            try
            {
                const string sp = "SERVICECOUNTDRREADYBYMNTH";
                int BreachCount = 0;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            return Convert.IsDBNull(reader["serviceCount"]) ? 0 : Convert.ToInt32(reader["serviceCount"]);
                        }
                    }
                    return BreachCount;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetInfraobjServiceCountDRReadyCurrentMonth()" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        int IInfraObjectDataAccess.GetServiceCountNotDRReadyCurrentMonth()
        {
            try
            {
                const string sp = "SERVICECOUNTNNOTDRREADYBYMNTH";
                int BreachCount = 0;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            return Convert.IsDBNull(reader["serviceCount"]) ? 0 : Convert.ToInt32(reader["serviceCount"]);
                        }
                    }
                    return BreachCount;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetInfraobjServiceCountDRReadyCurrentMonth()" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        int IInfraObjectDataAccess.GetFunctionCountDRReadyCurrentMonth()
        {
            try
            {
                const string sp = "FUNCTIONCOUNTDRREADYBYMNTH";
                int BreachCount = 0;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            return Convert.IsDBNull(reader["functionCount"]) ? 0 : Convert.ToInt32(reader["functionCount"]);
                        }
                    }
                    return BreachCount;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetInfraobjServiceCountDRReadyCurrentMonth()" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        int IInfraObjectDataAccess.GetFunctionCountNotDRReadyCurrentMonth()
        {
            try
            {
                const string sp = "FUNCTIONCUNTNNOTDRREADYBYMNTH";
                int BreachCount = 0;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            return Convert.IsDBNull(reader["functionCount"]) ? 0 : Convert.ToInt32(reader["functionCount"]);
                        }
                    }
                    return BreachCount;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetInfraobjServiceCountDRReadyCurrentMonth()" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        int IInfraObjectDataAccess.GetAllInfraObjectCount_NotRecovered()
        {
            try
            {
                const string sp = "INFRAOBJECTCOUNT_NOTRECOVERED";
                int BreachCount = 0;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            return Convert.IsDBNull(reader["NotRecInfra"]) ? 0 : Convert.ToInt32(reader["NotRecInfra"]);
                        }
                    }
                    return BreachCount;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraObjectDataAccess.GetAllInfraObjectCount_NotRecovered()" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     update <see cref="InfraObject" /> into infraObject table by infraObjectId.
        /// </summary>
        /// <param name="infraObjectId">infraObjectId</param>
        /// <param name="state">Iscall</param>
        /// <returns>bool</returns>
        /// <author>Suryaji shinde </author>
        bool IInfraObjectDataAccess.UpdateByIsCall(int infraObjectId)
        {
            try
            {
                if (infraObjectId < 1)
                {
                    throw new ArgumentNullException("infraObjectId");
                }

                //   const string sp = "InfraObject_UpdateByIsCall";

                const string sp = "INFRAOBJECT_UPDATEBYISCALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "isCall", DbType.Int32, 1);

                    int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return returnCode < 0;
#else
                    return returnCode > 0;
#endif
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Infra Iscall Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message,
                    ex);
            }
        }

        #endregion Methods
    }
}