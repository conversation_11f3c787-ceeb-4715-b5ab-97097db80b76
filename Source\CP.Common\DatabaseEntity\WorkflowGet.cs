﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "WorkflowGet", Namespace = "www.ContinuityPlatform.com")]
    public class WorkflowGet : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string ActionIds { get; set; }

        [DataMember]
        public int WorkflowId { get; set; }
        #endregion Properties
    }
}
