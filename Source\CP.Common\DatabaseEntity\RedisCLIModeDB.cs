﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "RedisCLIMode", Namespace = "http://www.BCMS.com/types")]

    public class RedisCLIModeDB : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BaseDatabaseId { get; set; }

        [DataMember]
        public string DatabaseName { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string SourceLocation { get; set; }

        [DataMember]
        public string PortNumber { get; set; }

        #endregion Properties
        #region Constructor

        public RedisCLIModeDB()
            : base()
        {

        }

        #endregion
    }
}
