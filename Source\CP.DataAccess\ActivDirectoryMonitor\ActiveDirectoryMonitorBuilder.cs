﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using CP.Common.DatabaseEntity;
using System.Data;

namespace CP.DataAccess.ActivDirectoryMonitor
{
    class ActiveDirectoryMonitorBuilder : IEntityBuilder<ActiveDirectoryMonitor>
    {

        IList<ActiveDirectoryMonitor> IEntityBuilder<ActiveDirectoryMonitor>.BuildEntities(IDataReader reader)
        {
            var actdr = new List<ActiveDirectoryMonitor>();

            while (reader.Read())
            {
                actdr.Add(((IEntityBuilder<ActiveDirectoryMonitor>)this).BuildEntity(reader, new ActiveDirectoryMonitor()));
            }

            return (actdr.Count > 0) ? actdr : null;
        }

        ActiveDirectoryMonitor IEntityBuilder<ActiveDirectoryMonitor>.BuildEntity(IDataReader reader, ActiveDirectoryMonitor actdr)
        {

            actdr.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            actdr.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"])
                ? 0
                : Convert.ToInt32(reader["InfraObjectId"]);
            //actdr.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
            //    ? 0
            //    : Convert.ToInt32(reader["ReplicationId"]);

            actdr.PRDomain = Convert.IsDBNull(reader["PRDomain"])
                ? string.Empty
                : Convert.ToString(reader["PRDomain"]);
            actdr.DRDomain = Convert.IsDBNull(reader["DRDomain"])
                ? string.Empty
                : Convert.ToString(reader["DRDomain"]);
            actdr.PRController = Convert.IsDBNull(reader["PRController"])
               ? string.Empty
               : Convert.ToString(reader["PRController"]);
            actdr.DRController = Convert.IsDBNull(reader["DRController"])
                ? string.Empty
                : Convert.ToString(reader["DRController"]);
            actdr.PRRepSite = Convert.IsDBNull(reader["PRRepSite"])
                ? string.Empty
                : Convert.ToString(reader["PRRepSite"]);
            actdr.DRRepSite = Convert.IsDBNull(reader["DRRepSite"])
               ? string.Empty
               : Convert.ToString(reader["DRRepSite"]);
            actdr.PRRepPartnerName = Convert.IsDBNull(reader["PRRepPartnerName"])
                ? string.Empty
                : Convert.ToString(reader["PRRepPartnerName"]);
            actdr.DRRepPartnerName = Convert.IsDBNull(reader["DRRepPartnerName"])
                ? string.Empty
                : Convert.ToString(reader["DRRepPartnerName"]);
            actdr.PRRepPartnerType = Convert.IsDBNull(reader["PRRepPartnerType"])
               ? string.Empty
               : Convert.ToString(reader["PRRepPartnerType"]);
            actdr.DRRepPartnerTYpe = Convert.IsDBNull(reader["DRRepPartnerTYpe"])
               ? string.Empty
               : Convert.ToString(reader["DRRepPartnerTYpe"]);
            actdr.PRConRepFailure = Convert.IsDBNull(reader["PRConRepFailure"])
               ? string.Empty
               : Convert.ToString(reader["PRConRepFailure"]);
            actdr.DRConRepFailure = Convert.IsDBNull(reader["DRConRepFailure"])
               ? string.Empty
               : Convert.ToString(reader["DRConRepFailure"]);
            actdr.PRLastRepAttempt = Convert.IsDBNull(reader["PRLastRepAttempt"])
               ? string.Empty
               : Convert.ToString(reader["PRLastRepAttempt"]);
            actdr.DRLastRepAttempt = Convert.IsDBNull(reader["DRLastRepAttempt"])
               ? string.Empty
               : Convert.ToString(reader["DRLastRepAttempt"]);

            actdr.PRLastRepSuccess = Convert.IsDBNull(reader["PRLastRepSuccess"])
              ? string.Empty
              : Convert.ToString(reader["PRLastRepSuccess"]);
            actdr.DRLastRepSuccess = Convert.IsDBNull(reader["DRLastRepSuccess"])
               ? string.Empty
               : Convert.ToString(reader["DRLastRepSuccess"]);

            actdr.PRLastRepResult = Convert.IsDBNull(reader["PRLastRepResult"])
              ? string.Empty
              : Convert.ToString(reader["PRLastRepResult"]);
            actdr.DRLastRepResult = Convert.IsDBNull(reader["DRLastRepResult"])
               ? string.Empty
               : Convert.ToString(reader["DRLastRepResult"]);
            actdr.PRLastSyncMsg = Convert.IsDBNull(reader["PRLastSyncMsg"])
              ? string.Empty
              : Convert.ToString(reader["PRLastSyncMsg"]);
            actdr.DRLastSyncMsg = Convert.IsDBNull(reader["DRLastSyncMsg"])
               ? string.Empty
               : Convert.ToString(reader["DRLastSyncMsg"]);
            actdr.PRRepFailFirstRecTime = Convert.IsDBNull(reader["PRRepFailFirstRecTime"])
              ? string.Empty
              : Convert.ToString(reader["PRRepFailFirstRecTime"]);
            actdr.DRRepFailFirstRecTime = Convert.IsDBNull(reader["DRRepFailFirstRecTime"])
               ? string.Empty
               : Convert.ToString(reader["DRRepFailFirstRecTime"]);
            actdr.DataLag = Convert.IsDBNull(reader["DataLag"])
              ? string.Empty
              : Convert.ToString(reader["DataLag"]);
            


            return actdr;
        }
    }
}


