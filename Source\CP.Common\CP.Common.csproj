﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{58CE906F-92E6-4F08-BAB4-480DC99C36CC}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CP.Common</RootNamespace>
    <AssemblyName>CP.Common</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;MYSQL</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE;ORACLE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <RequiredTargetFramework>3.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\BaseEntity.cs" />
    <Compile Include="BIAWorkflowFailureWorkflowType.cs" />
    <Compile Include="BusinessEntity\ActionType.cs" />
    <Compile Include="BusinessEntity\ActionTypeBase.cs" />
    <Compile Include="BusinessEntity\AlertDescription.cs" />
    <Compile Include="BusinessEntity\Archive.cs" />
    <Compile Include="BusinessEntity\FunctionInfo.cs" />
    <Compile Include="BusinessEntity\NotificationMessage.cs" />
    <Compile Include="BusinessEntity\RecoveryType.cs" />
    <Compile Include="BusinessEntity\RequestTokenCollection.cs" />
    <Compile Include="BusinessEntity\XmlOperation.cs" />
    <Compile Include="BusinessEntity\XmlParameter.cs" />
    <Compile Include="DashboardAlert.cs" />
    <Compile Include="DatabaseEntity\AccessManagerCustom.cs" />
    <Compile Include="DatabaseEntity\ActionAnalytic.cs" />
    <Compile Include="DatabaseEntity\ActionHumanIntervention.cs" />
    <Compile Include="DatabaseEntity\ActiveDirectory.cs" />
    <Compile Include="DatabaseEntity\ActiveDirectoryMonitor.cs" />
    <Compile Include="DatabaseEntity\ActiveODGReplicationNonOdg.cs" />
    <Compile Include="DatabaseEntity\Analytics.cs" />
    <Compile Include="DatabaseEntity\AodgRepliLogsDetails.cs" />
    <Compile Include="DatabaseEntity\AppDependencyLinks.cs" />
    <Compile Include="DatabaseEntity\AppDepGroupNodes.cs" />
    <Compile Include="DatabaseEntity\AppDepMappingHosts.cs" />
    <Compile Include="DatabaseEntity\AppDepMappingProfileDetails.cs" />
    <Compile Include="DatabaseEntity\AppDepMappingSettings.cs" />
    <Compile Include="DatabaseEntity\AppDepStandardPorts.cs" />
    <Compile Include="DatabaseEntity\ApplicationDiscovery.cs" />
    <Compile Include="DatabaseEntity\ApplicationDiscoveryProfileDetails.cs" />
    <Compile Include="DatabaseEntity\ASMGridConfiguration.cs" />
    <Compile Include="DatabaseEntity\AzureSiteRecovery.cs" />
    <Compile Include="DatabaseEntity\Azure_Monitoring.cs" />
    <Compile Include="DatabaseEntity\Base24Replication.cs" />
    <Compile Include="DatabaseEntity\Base24RepliMonitor.cs" />
    <Compile Include="DatabaseEntity\BFBIAMatrix.cs" />
    <Compile Include="DatabaseEntity\BFBIAMatrixDetails.cs" />
    <Compile Include="DatabaseEntity\BIAActionCountCompletedOutOfRTOBySol.cs" />
    <Compile Include="DatabaseEntity\BIAActionEffiAutoMode.cs" />
    <Compile Include="DatabaseEntity\BIAActionEfficiency.cs" />
    <Compile Include="DatabaseEntity\BIAActionFailureHumanInterventionTrend.cs" />
    <Compile Include="DatabaseEntity\BiaActiontrendComponent.cs" />
    <Compile Include="DatabaseEntity\BIAAlertCountBusinessServiceWise.cs" />
    <Compile Include="DatabaseEntity\BIAAlertsTrendBusinessService.cs" />
    <Compile Include="DatabaseEntity\BIADrillExecution.cs" />
    <Compile Include="DatabaseEntity\BIAFailedAction.cs" />
    <Compile Include="DatabaseEntity\BIAFailedWorkflow.cs" />
    <Compile Include="DatabaseEntity\BIAFailedWorkflowByWorkflowType.cs" />
    <Compile Include="DatabaseEntity\BIAFailureActinBySolType.cs" />
    <Compile Include="DatabaseEntity\BIAFailureActinBySOSB.cs" />
    <Compile Include="DatabaseEntity\BIAFailureActionByWorkflowType.cs" />
    <Compile Include="DatabaseEntity\BIAGetAlertDetails.cs" />
    <Compile Include="DatabaseEntity\BIAImpactCount.cs" />
    <Compile Include="DatabaseEntity\BIAOverallAlertStatistics.cs" />
    <Compile Include="DatabaseEntity\BIAOvervallWorkflowStatistics.cs" />
    <Compile Include="DatabaseEntity\BIAProfileDetailsActionEfficiencyTrend.cs" />
    <Compile Include="DatabaseEntity\BIAProfileImpactTypes.cs" />
    <Compile Include="DatabaseEntity\BIAProfileTimeInterval.cs" />
    <Compile Include="DatabaseEntity\BIASuccessWFvsHI.cs" />
    <Compile Include="DatabaseEntity\BIAWorkflowAnalyatic.cs" />
    <Compile Include="DatabaseEntity\BIAWorkflowCompletedWithinRTO.cs" />
    <Compile Include="DatabaseEntity\BIAWorkflowefficiencyTrend.cs" />
    <Compile Include="DatabaseEntity\BIAWorkflowfailureprofileTrend.cs" />
    <Compile Include="DatabaseEntity\BIAWorkflowProfilesHumanInterventionsTrend.cs" />
    <Compile Include="DatabaseEntity\BIAWorkflowTrendsAll.cs" />
    <Compile Include="DatabaseEntity\BSDRReadyDaily.cs" />
    <Compile Include="DatabaseEntity\BusinessFunctionBIA.cs" />
    <Compile Include="DatabaseEntity\BusinessFunctionBIAActivity.cs" />
    <Compile Include="DatabaseEntity\BusinessFunctionBIADetails.cs" />
    <Compile Include="DatabaseEntity\BusinessFunctionBIARelation.cs" />
    <Compile Include="DatabaseEntity\BusinessFunctionBIASeverity.cs" />
    <Compile Include="DatabaseEntity\AlertManager.cs" />
    <Compile Include="DatabaseEntity\AlertMessage.cs" />
    <Compile Include="DatabaseEntity\AlertNotification.cs" />
    <Compile Include="DatabaseEntity\ApplicationGroupInfo.cs" />
    <Compile Include="DatabaseEntity\ApplicationMonitor.cs" />
    <Compile Include="DatabaseEntity\ApplicationService.cs" />
    <Compile Include="DatabaseEntity\AlertReceiver.cs" />
    <Compile Include="DatabaseEntity\ApplicationGroup.cs" />
    <Compile Include="DatabaseEntity\Audit.cs" />
    <Compile Include="DatabaseEntity\ActionSet.cs" />
    <Compile Include="DatabaseEntity\Alert.cs" />
    <Compile Include="DatabaseEntity\ApplicationDependency.cs" />
    <Compile Include="DatabaseEntity\BusinessFunction.cs" />
    <Compile Include="DatabaseEntity\BusinessImpact.cs" />
    <Compile Include="DatabaseEntity\BusinessProfile.cs" />
    <Compile Include="DatabaseEntity\BusinessService.cs" />
    <Compile Include="DatabaseEntity\BusinessServiceRPOInfo.cs" />
    <Compile Include="DatabaseEntity\BusinessServiceRTOInfo.cs" />
    <Compile Include="DatabaseEntity\BusinessTimeInterval.cs" />
    <Compile Include="DatabaseEntity\CGMonitor.cs" />
    <Compile Include="DatabaseEntity\CGVolumeMonitor.cs" />
    <Compile Include="DatabaseEntity\CloudantDBMonitor.cs" />
    <Compile Include="DatabaseEntity\CloudantDBReplication.cs" />
    <Compile Include="DatabaseEntity\CloudantDBReplicationJob.cs" />
    <Compile Include="DatabaseEntity\Cloudant_LB_Monitor.cs" />
    <Compile Include="DatabaseEntity\Cloudant_Replication_Monitor.cs" />
    <Compile Include="DatabaseEntity\ClustorGroupInfoStatus.cs" />
    <Compile Include="DatabaseEntity\CompanyInfo .cs" />
    <Compile Include="DatabaseEntity\ComponentFailureDaily.cs" />
    <Compile Include="DatabaseEntity\CPLoadMaster.cs" />
    <Compile Include="DatabaseEntity\CPNodeMaster.cs" />
    <Compile Include="DatabaseEntity\CPSLSchedule.cs" />
    <Compile Include="DatabaseEntity\CPSLScript.cs" />
    <Compile Include="DatabaseEntity\CustomRoleSubType.cs" />
    <Compile Include="DatabaseEntity\DAGConfigurationSummary.cs" />
    <Compile Include="DatabaseEntity\DatabaseCloudantNoSQL.cs" />
    <Compile Include="DatabaseEntity\DatabaseHANADB.cs" />
    <Compile Include="DatabaseEntity\DatabaseMaxDB.cs" />
    <Compile Include="DatabaseEntity\DatabaseMirrorMonitor.cs" />
    <Compile Include="DatabaseEntity\DatabaseOceanstorMssql.cs" />
    <Compile Include="DatabaseEntity\DatabaseMssqlMonitor.cs" />
    <Compile Include="DatabaseEntity\DatabaseRole.cs" />
    <Compile Include="DatabaseEntity\DatabaseSqlNative2008.cs" />
    <Compile Include="DatabaseEntity\DatabaseSqlPlus.cs" />
    <Compile Include="DatabaseEntity\DataBaseSyBase.cs" />
    <Compile Include="DatabaseEntity\DatabaseSybaseWithRsHadr.cs" />
    <Compile Include="DatabaseEntity\DatabaseSybaseWithSrs.cs" />
    <Compile Include="DatabaseEntity\DefaultMoniService.cs" />
    <Compile Include="DatabaseEntity\DiscoverScan.cs" />
    <Compile Include="DatabaseEntity\DiscoveryConfiguration.cs" />
    <Compile Include="DatabaseEntity\DiscoveryHostLogs.cs" />
    <Compile Include="DatabaseEntity\DropDown.cs" />
    <Compile Include="DatabaseEntity\eBDRProfileMonitoring.cs" />
    <Compile Include="DatabaseEntity\EBDRProfileDetails.cs" />
    <Compile Include="DatabaseEntity\eBDRProfileReplication.cs" />
    <Compile Include="DatabaseEntity\EbdrProfileStatus.cs" />
    <Compile Include="DatabaseEntity\EC2S3DataSyncComponentDetails.cs" />
    <Compile Include="DatabaseEntity\EC2S3DataSyncReplication.cs" />
    <Compile Include="DatabaseEntity\Ec2s3dsReplicationMonitor.cs" />
    <Compile Include="DatabaseEntity\EmailReport.cs" />
    <Compile Include="DatabaseEntity\EmcISilonPolicy.cs" />
    <Compile Include="DatabaseEntity\EmcISilonReplication.cs" />
    <Compile Include="DatabaseEntity\EmcISilon_Monitor.cs" />
    <Compile Include="DatabaseEntity\EmcMirrorView.cs" />
    <Compile Include="DatabaseEntity\EmcMirrorView_Repli_Monitor.cs" />
    <Compile Include="DatabaseEntity\EMCSRDFCG.cs" />
    <Compile Include="DatabaseEntity\EMCSRDFCGMonitoring.cs" />
    <Compile Include="DatabaseEntity\EMCSRDFSG.cs" />
    <Compile Include="DatabaseEntity\EmcsrdfSGLogs.cs" />
    <Compile Include="DatabaseEntity\EMCSRDFStar.cs" />
    <Compile Include="DatabaseEntity\EMCSRDFStarMonitoring.cs" />
    <Compile Include="DatabaseEntity\EmcUnity.cs" />
    <Compile Include="DatabaseEntity\EmcUnity_Repli_Monitor.cs" />
    <Compile Include="DatabaseEntity\Emc_MV_Mirror_Monitor.cs" />
    <Compile Include="DatabaseEntity\ExChangeHealthSumry.cs" />
    <Compile Include="DatabaseEntity\ExChangMntrStats.cs" />
    <Compile Include="DatabaseEntity\GoldenGate.cs" />
    <Compile Include="DatabaseEntity\GoldenGateDBMonitor.cs" />
    <Compile Include="DatabaseEntity\GoldenGateGroupDetails.cs" />
    <Compile Include="DatabaseEntity\GoldenGateReplicationMonitor.cs" />
    <Compile Include="DatabaseEntity\HACMPCluster.cs" />
    <Compile Include="DatabaseEntity\HACMPClusterDetailsMonitor.cs" />
    <Compile Include="DatabaseEntity\HACMPResourceGroupsMonitor.cs" />
    <Compile Include="DatabaseEntity\HanaDBDatabaseServices.cs" />
    <Compile Include="DatabaseEntity\HanaDBMonitor.cs" />
    <Compile Include="DatabaseEntity\HanaDbReplicationMode.cs" />
    <Compile Include="DatabaseEntity\HanaDbSystemOperationMode.cs" />
    <Compile Include="DatabaseEntity\HP3PARStorage.cs" />
    <Compile Include="DatabaseEntity\HP3PAR_Monitor.cs" />
    <Compile Include="DatabaseEntity\HuaweiStorage.cs" />
    <Compile Include="DatabaseEntity\Humintervention.cs" />
    <Compile Include="DatabaseEntity\HuwaiStorageMonitorStatslogs.cs" />
    <Compile Include="DatabaseEntity\HypervClusterNodeMonitor.cs" />
    <Compile Include="DatabaseEntity\HypervClusterSummaryMonitor.cs" />
    <Compile Include="DatabaseEntity\HyperVDetails.cs" />
    <Compile Include="DatabaseEntity\ImportCMDBLogs.cs" />
    <Compile Include="DatabaseEntity\ImportCMDBStatus.cs" />
    <Compile Include="DatabaseEntity\IncidentCIOMontly.cs" />
    <Compile Include="DatabaseEntity\Incidentmanagement.cs" />
    <Compile Include="DatabaseEntity\IncidentManagementBIASummary.cs" />
    <Compile Include="DatabaseEntity\IncidentManagementNew.cs" />
    <Compile Include="DatabaseEntity\IncidentManagementSummary.cs" />
    <Compile Include="DatabaseEntity\ImpactRelType.cs" />
    <Compile Include="DatabaseEntity\InfraobjectCGDetails.cs" />
    <Compile Include="DatabaseEntity\InfraobjectDiskMonitor.cs" />
    <Compile Include="DatabaseEntity\InfraobjectGlobalMirrorluns.cs" />
    <Compile Include="DatabaseEntity\InfraobjectGlobalMirrorLunsDetails.cs" />
    <Compile Include="DatabaseEntity\InfraobjectSchedularLogs.cs" />
    <Compile Include="DatabaseEntity\InfraobjectSchedularStatus.cs" />
    <Compile Include="DatabaseEntity\InfraObjectscheduleWorkFlows.cs" />
    <Compile Include="DatabaseEntity\InfraobjectVolumeDetails.cs" />
    <Compile Include="DatabaseEntity\InfrascheduleWfdeatils.cs" />
    <Compile Include="DatabaseEntity\LogDetails.cs" />
    <Compile Include="DatabaseEntity\LogFileDetails.cs" />
    <Compile Include="DatabaseEntity\LogFiles.cs" />
    <Compile Include="DatabaseEntity\LogView.cs" />
    <Compile Include="DatabaseEntity\MariaDB.cs" />
    <Compile Include="DatabaseEntity\MariaDBMontor.cs" />
    <Compile Include="DatabaseEntity\MaxDBApplyLogPage.cs" />
    <Compile Include="DatabaseEntity\MaxDBMonitor.cs" />
    <Compile Include="DatabaseEntity\MaxDBReplication.cs" />
    <Compile Include="DatabaseEntity\MaxEmcSrdfFullDB.cs" />
    <Compile Include="DatabaseEntity\Mimix.cs" />
    <Compile Include="DatabaseEntity\MimixAlerts.cs" />
    <Compile Include="DatabaseEntity\MimixAvilability.cs" />
    <Compile Include="DatabaseEntity\MimixDatalag.cs" />
    <Compile Include="DatabaseEntity\MimixHealth.cs" />
    <Compile Include="DatabaseEntity\MimixManager.cs" />
    <Compile Include="DatabaseEntity\MongoDB.cs" />
    <Compile Include="DatabaseEntity\MongoDBDMonitorStatus.cs" />
    <Compile Include="DatabaseEntity\MonitorQueue.cs" />
    <Compile Include="DatabaseEntity\MonitorServiceStatusLogs.cs" />
    <Compile Include="DatabaseEntity\DatabaseBase.cs" />
    <Compile Include="DatabaseEntity\DatabaseDB2.cs" />
    <Compile Include="DatabaseEntity\DatabaseExchange.cs" />
    <Compile Include="DatabaseEntity\DataBaseExchangeDAG.cs" />
    <Compile Include="DatabaseEntity\DatabaseMySql.cs" />
    <Compile Include="DatabaseEntity\DatabaseNodes.cs" />
    <Compile Include="DatabaseEntity\DatabaseOracle.cs" />
    <Compile Include="DatabaseEntity\DatabaseOracleRac.cs" />
    <Compile Include="DatabaseEntity\DatabaseSql.cs" />
    <Compile Include="DatabaseEntity\DatabaseVersion.cs" />
    <Compile Include="DatabaseEntity\DataGuardMonitor.cs" />
    <Compile Include="DatabaseEntity\DataGuard.cs" />
    <Compile Include="DatabaseEntity\DataSyncProperties.cs" />
    <Compile Include="DatabaseEntity\DB2DataSyncMonitor.cs" />
    <Compile Include="DatabaseEntity\EMCDevicesDetails.cs" />
    <Compile Include="DatabaseEntity\EMCSRDF.cs" />
    <Compile Include="DatabaseEntity\EventManagement.cs" />
    <Compile Include="DatabaseEntity\EventManagementList.cs" />
    <Compile Include="DatabaseEntity\ExchangeDAGComponantMonitor.cs" />
    <Compile Include="DatabaseEntity\ExchangeDAGMonitoring.cs" />
    <Compile Include="DatabaseEntity\ExchangeDAGReplHealthStatus.cs" />
    <Compile Include="DatabaseEntity\ExchangeDAGReplication.cs" />
    <Compile Include="DatabaseEntity\ExchangeDAGServiceMonitoring.cs" />
    <Compile Include="DatabaseEntity\FastCopyJob.cs" />
    <Compile Include="DatabaseEntity\GroupDatabaseNodes.cs" />
    <Compile Include="DatabaseEntity\HADR.cs" />
    <Compile Include="DatabaseEntity\HADRReplication.cs" />
    <Compile Include="DatabaseEntity\Heatmap.cs" />
    <Compile Include="DatabaseEntity\HitachiURDeviceMonitoring.cs" />
    <Compile Include="DatabaseEntity\HitachiUrLuns.cs" />
    <Compile Include="DatabaseEntity\HitachiURMonitoring.cs" />
    <Compile Include="DatabaseEntity\HitachiUrReplication.cs" />
    <Compile Include="DatabaseEntity\ImpactAnalysis.cs" />
    <Compile Include="DatabaseEntity\ImpactMaster.cs" />
    <Compile Include="DatabaseEntity\ImpactTypeMaster.cs" />
    <Compile Include="DatabaseEntity\Incident.cs" />
    <Compile Include="DatabaseEntity\InfraObject.cs" />
    <Compile Include="DatabaseEntity\InfraObjectJob.cs" />
    <Compile Include="DatabaseEntity\InfraObjectsLuns.cs" />
    <Compile Include="DatabaseEntity\Infrastructure.cs" />
    <Compile Include="DatabaseEntity\Job.cs" />
    <Compile Include="DatabaseEntity\JobGroup.cs" />
    <Compile Include="DatabaseEntity\JobName.cs" />
    <Compile Include="DatabaseEntity\JobTypeReplicationType.cs" />
    <Compile Include="DatabaseEntity\MonitorServices.cs" />
    <Compile Include="DatabaseEntity\MSSQLAlwaysOnReplication.cs" />
    <Compile Include="DatabaseEntity\MSSQLAlwaysOnServerMoniter.cs" />
    <Compile Include="DatabaseEntity\MSSQLDBMirrorReplication.cs" />
    <Compile Include="DatabaseEntity\MSSQLDBMirrorReplicationMonitor.cs" />
    <Compile Include="DatabaseEntity\MsSqlDMXEmcSrdfFullDB.cs" />
    <Compile Include="DatabaseEntity\MSSqlDoubletek.cs" />
    <Compile Include="DatabaseEntity\MsSqlEmcSrdfFullDB.cs" />
    <Compile Include="DatabaseEntity\MsSqlVMAXEmcSrdfFullDB.cs" />
    <Compile Include="DatabaseEntity\MySqlGlobalMirrorMonitor.cs" />
    <Compile Include="DatabaseEntity\MySqlMonitor.cs" />
    <Compile Include="DatabaseEntity\MySqlNative.cs" />
    <Compile Include="DatabaseEntity\MySqlReplication.cs" />
    <Compile Include="DatabaseEntity\NTNXLeapRcblEntReplMonitor.cs" />
    <Compile Include="DatabaseEntity\NTNXLeapRPReplMonitor.cs" />
    <Compile Include="DatabaseEntity\NutanixClusterDetailMonitoring.cs" />
    <Compile Include="DatabaseEntity\NutanixLeapReplication.cs" />
    <Compile Include="DatabaseEntity\NutanixProtectionDomainMonitoring.cs" />
    <Compile Include="DatabaseEntity\NutanixProtectionDomainRepliMonitoring.cs" />
    <Compile Include="DatabaseEntity\NutanixRepli.cs" />
    <Compile Include="DatabaseEntity\ODGMonitor.cs" />
    <Compile Include="DatabaseEntity\OracleEmcsrdf.cs" />
    <Compile Include="DatabaseEntity\ParallelProfile.cs" />
    <Compile Include="DatabaseEntity\ParallelWorkflowProfile.cs" />
    <Compile Include="DatabaseEntity\PostgredbMonitoring.cs" />
    <Compile Include="DatabaseEntity\DatabasePostgre9x.cs" />
    <Compile Include="DatabaseEntity\PostgreReplication.cs" />
    <Compile Include="DatabaseEntity\Postgre9xComponentMonitor.cs" />
    <Compile Include="DatabaseEntity\PostgresClusterHuaweiDBMonitor.cs" />
    <Compile Include="DatabaseEntity\PostgreSql.cs" />
    <Compile Include="DatabaseEntity\Postgre9xMonitorStatus.cs" />
    <Compile Include="DatabaseEntity\PostgresSqlClusterMonitor.cs" />
    <Compile Include="DatabaseEntity\QueueMoniter.cs" />
    <Compile Include="DatabaseEntity\RecoverPointPStateStatistic.cs" />
    <Compile Include="DatabaseEntity\RecoverPStateMonitor.cs" />
    <Compile Include="DatabaseEntity\RecoverPStatisticMonitor.cs" />
    <Compile Include="DatabaseEntity\RecoveryPoint.cs" />
    <Compile Include="DatabaseEntity\RecoveryPointMulti.cs" />
    <Compile Include="DatabaseEntity\RedisCLIModeDB.cs" />
    <Compile Include="DatabaseEntity\ReplicatedGroupMontor.cs" />
    <Compile Include="DatabaseEntity\ReplicationBase.cs" />
    <Compile Include="DatabaseEntity\ReportSchedule.cs" />
    <Compile Include="DatabaseEntity\RlinkMonitorRepliPerformance.cs" />
    <Compile Include="DatabaseEntity\RLinkMonitorSecUpdate.cs" />
    <Compile Include="DatabaseEntity\RoboCopy.cs" />
    <Compile Include="DatabaseEntity\RoboCopyJob.cs" />
    <Compile Include="DatabaseEntity\RoboCopyLogs.cs" />
    <Compile Include="DatabaseEntity\RoboCopyOptions.cs" />
    <Compile Include="DatabaseEntity\RPOTimeSpan.cs" />
    <Compile Include="DatabaseEntity\RSyncJob.cs" />
    <Compile Include="DatabaseEntity\RSyncMonitor.cs" />
    <Compile Include="DatabaseEntity\RSyncOptions.cs" />
    <Compile Include="DatabaseEntity\RSyncReplication.cs" />
    <Compile Include="DatabaseEntity\RTOMTRConfiguration.cs" />
    <Compile Include="DatabaseEntity\ScheduleDiscoveryProfDetails.cs" />
    <Compile Include="DatabaseEntity\ScheduleWorkflow.cs" />
    <Compile Include="DatabaseEntity\ServiceAvailability.cs" />
    <Compile Include="DatabaseEntity\ServiceDiagram.cs" />
    <Compile Include="DatabaseEntity\ServiceDRProtection.cs" />
    <Compile Include="DatabaseEntity\ServiceProfile.cs" />
    <Compile Include="DatabaseEntity\ServiceRTODaily.cs" />
    <Compile Include="DatabaseEntity\Settings.cs" />
    <Compile Include="DatabaseEntity\SiteInchargeInfo.cs" />
    <Compile Include="DatabaseEntity\SMSConfiguration.cs" />
    <Compile Include="DatabaseEntity\SMSReport.cs" />
    <Compile Include="DatabaseEntity\SmtpConfiguration.cs" />
    <Compile Include="DatabaseEntity\SnapMirrorMonitor.cs" />
    <Compile Include="DatabaseEntity\BPAutomation.cs" />
    <Compile Include="DatabaseEntity\BusinessInfo.cs" />
    <Compile Include="DatabaseEntity\BusinessUserFunction.cs" />
    <Compile Include="DatabaseEntity\CompanyProfile.cs" />
    <Compile Include="DatabaseEntity\CustomException.cs" />
    <Compile Include="DatabaseEntity\DatabaseBackupInfo.cs" />
    <Compile Include="DatabaseEntity\DatabaseBackupOperation.cs" />
    <Compile Include="DatabaseEntity\DnsServerDetails.cs" />
    <Compile Include="DatabaseEntity\DomainDetails.cs" />
    <Compile Include="DatabaseEntity\DROperation.cs" />
    <Compile Include="DatabaseEntity\DROperationResult.cs" />
    <Compile Include="DatabaseEntity\ExchangeHealth.cs" />
    <Compile Include="DatabaseEntity\SCR.cs" />
    <Compile Include="DatabaseEntity\ExchangeService.cs" />
    <Compile Include="DatabaseEntity\ExchangeSCRStatus.cs" />
    <Compile Include="DatabaseEntity\FastCopy.cs" />
    <Compile Include="DatabaseEntity\FastCopyMonitor.cs" />
    <Compile Include="DatabaseEntity\GlobalMirror.cs" />
    <Compile Include="DatabaseEntity\GlobalMirrorLuns.cs" />
    <Compile Include="DatabaseEntity\GlobalMirrorMonitor.cs" />
    <Compile Include="DatabaseEntity\Group.cs" />
    <Compile Include="DatabaseEntity\GroupLuns.cs" />
    <Compile Include="DatabaseEntity\GroupWorkflow.cs" />
    <Compile Include="DatabaseEntity\Licencekey.cs" />
    <Compile Include="DatabaseEntity\LogVolume.cs" />
    <Compile Include="DatabaseEntity\Maintenance.cs" />
    <Compile Include="DatabaseEntity\MountPoint.cs" />
    <Compile Include="DatabaseEntity\NetworkIP.cs" />
    <Compile Include="DatabaseEntity\Nodes.cs" />
    <Compile Include="DatabaseEntity\OracleLog.cs" />
    <Compile Include="DatabaseEntity\ParallelDROperation.cs" />
    <Compile Include="DatabaseEntity\ParallelGroupWorkflow.cs" />
    <Compile Include="DatabaseEntity\ParallelServer.cs" />
    <Compile Include="DatabaseEntity\ParallelWorkflowActionResult.cs" />
    <Compile Include="DatabaseEntity\Server.cs" />
    <Compile Include="DatabaseEntity\Site.cs" />
    <Compile Include="DatabaseEntity\SnapMirror.cs" />
    <Compile Include="DatabaseEntity\SqlNative.cs" />
    <Compile Include="DatabaseEntity\SQLNative2008Monitor.cs" />
    <Compile Include="DatabaseEntity\SQLNative2008Replication.cs" />
    <Compile Include="DatabaseEntity\SqlNativeHealth.cs" />
    <Compile Include="DatabaseEntity\SqlNativeHealthParameter.cs" />
    <Compile Include="DatabaseEntity\SqlNativeMonitor.cs" />
    <Compile Include="DatabaseEntity\SqlNativeServices.cs" />
    <Compile Include="DatabaseEntity\SqlServer2000Health.cs" />
    <Compile Include="DatabaseEntity\SqlServer2000Log.cs" />
    <Compile Include="DatabaseEntity\SqlServer2000Service.cs" />
    <Compile Include="DatabaseEntity\SRMVmwareMonitor.cs" />
    <Compile Include="DatabaseEntity\SSOConfiguration.cs" />
    <Compile Include="DatabaseEntity\SubstituteAuthentication.cs" />
    <Compile Include="DatabaseEntity\SubstituteNodeAuthentication.cs" />
    <Compile Include="DatabaseEntity\Substitute_Authentication.cs" />
    <Compile Include="DatabaseEntity\SVCcontrollerMonitor.cs" />
    <Compile Include="DatabaseEntity\SVCGlobalMirrorMonitor.cs" />
    <Compile Include="DatabaseEntity\SVCGlobalMirrorORMetroMirror.cs" />
    <Compile Include="DatabaseEntity\SVCGMReplicationM.cs" />
    <Compile Include="DatabaseEntity\SVCNodeDetailedMonitor.cs" />
    <Compile Include="DatabaseEntity\SyBaseMonitor.cs" />
    <Compile Include="DatabaseEntity\SybaseWithRSHADRReplication.cs" />
    <Compile Include="DatabaseEntity\SybaseWithRSHADR_DBMonitor.cs" />
    <Compile Include="DatabaseEntity\SybaseWithRSHADR_RepliMonitor.cs" />
    <Compile Include="DatabaseEntity\SybaseWithRSHADR_RepliMonitorNew.cs" />
    <Compile Include="DatabaseEntity\SybaseWithSRSMonitor.cs" />
    <Compile Include="DatabaseEntity\SybaseWithSRSReplication.cs" />
    <Compile Include="DatabaseEntity\TagEntityDetails.cs" />
    <Compile Include="DatabaseEntity\TPRCMonitor.cs" />
    <Compile Include="DatabaseEntity\TPRCReplication.cs" />
    <Compile Include="DatabaseEntity\User.cs" />
    <Compile Include="DatabaseEntity\UserActivity.cs" />
    <Compile Include="DatabaseEntity\UserGroup.cs" />
    <Compile Include="DatabaseEntity\UserInfo.cs" />
    <Compile Include="DatabaseEntity\UserInfraObject.cs" />
    <Compile Include="DatabaseEntity\UserServices.cs" />
    <Compile Include="DatabaseEntity\vCenterMonitorStatus.cs" />
    <Compile Include="DatabaseEntity\vCenterProfile.cs" />
    <Compile Include="DatabaseEntity\vCenterProfileDetails.cs" />
    <Compile Include="DatabaseEntity\VeeamMonitorStatus.cs" />
    <Compile Include="DatabaseEntity\VeeamReplication.cs" />
    <Compile Include="DatabaseEntity\VeeamVMJob.cs" />
    <Compile Include="DatabaseEntity\VeritasCluster.cs" />
    <Compile Include="DatabaseEntity\VeritasClusterMonitor.cs" />
    <Compile Include="DatabaseEntity\VirtualVolumeMonitoring.cs" />
    <Compile Include="DatabaseEntity\VmwareMonitor.cs" />
    <Compile Include="DatabaseEntity\VMWareNetSnapMirrorComponentMonitor.cs" />
    <Compile Include="DatabaseEntity\VMWarePathDetails.cs" />
    <Compile Include="DatabaseEntity\VmwareVsphereMonitor.cs" />
    <Compile Include="DatabaseEntity\VmwareVsphereRepli.cs" />
    <Compile Include="DatabaseEntity\VVRReplication.cs" />
    <Compile Include="DatabaseEntity\Workflow.cs" />
    <Compile Include="DatabaseEntity\WorkflowAction.cs" />
    <Compile Include="DatabaseEntity\WorkflowGet.cs" />
    <Compile Include="DatabaseEntity\WorkflowManagementDetails.cs" />
    <Compile Include="DatabaseEntity\WrokFlowEfficiency.cs" />
    <Compile Include="DatabaseEntity\XIVConfiguration.cs" />
    <Compile Include="DatabaseEntity\XIVMirror.cs" />
    <Compile Include="DatabaseEntity\XIVMirrorMoniSatatus.cs" />
    <Compile Include="DatabaseEntity\XIVReplicationMonitor.cs" />
    <Compile Include="DatabaseEntity\zertomonitorstatus.cs" />
    <Compile Include="DatabaseEntity\ZetroSiteReplication.cs" />
    <Compile Include="DatabaseEntity\ZFSReplicationMonitorLogs.cs" />
    <Compile Include="DatabaseEntity\ZFSStorageReplication.cs" />
    <Compile Include="Shared\MySqlConstants.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Shared\CommonConstants.cs" />
    <Compile Include="Shared\Context.cs" />
    <Compile Include="Shared\ContextConstants.cs" />
    <Compile Include="Shared\Enums.cs" />
    <Compile Include="Shared\PagedRequest.cs" />
    <Compile Include="Shared\PagedResponse.cs" />
    <Compile Include="Shared\UIConstants.cs" />
    <Compile Include="Shared\UrlConstants.cs" />
    <Compile Include="Shared\ValidationConstants.cs" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CP.Helper\CP.Helper.csproj">
      <Project>{ced08c2f-93fd-4ba6-8d2c-e79e45fe6a4f}</Project>
      <Name>CP.Helper</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>