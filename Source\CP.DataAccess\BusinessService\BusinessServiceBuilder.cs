﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class BusinessServiceBuilder : IEntityBuilder<BusinessService>
    {
        IList<BusinessService> IEntityBuilder<BusinessService>.BuildEntities(IDataReader reader)
        {
            var businessServices = new List<BusinessService>();

            while (reader.Read())
            {
                businessServices.Add(((IEntityBuilder<BusinessService>)this).BuildEntity(reader, new BusinessService()));
            }

            return (businessServices.Count > 0) ? businessServices : null;
        }

        BusinessService IEntityBuilder<BusinessService>.BuildEntity(IDataReader reader, BusinessService businessService)
        {
            businessService.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            businessService.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
            businessService.Description = Convert.IsDBNull(reader["Description"])
                ? string.Empty
                : Convert.ToString(reader["Description"]);
            businessService.DRReadyness = !Convert.IsDBNull(reader["DRReadyness"]) &&
                                          Convert.ToBoolean(reader["DRReadyness"]);
            businessService.CompanyId = Convert.IsDBNull(reader["CompanyId"]) ? 0 : Convert.ToInt32(reader["CompanyId"]);
            businessService.SiteId = Convert.IsDBNull(reader["SiteId"]) ? 0 : Convert.ToInt32(reader["SiteId"]);
            businessService.ConfiguredRPO = Convert.IsDBNull(reader["ConfiguredRPO"])
                ? string.Empty
                : Convert.ToString(reader["ConfiguredRPO"]);
            businessService.ConfiguredRTO = Convert.IsDBNull(reader["ConfiguredRTO"])
                ? string.Empty
                : Convert.ToString(reader["ConfiguredRTO"]);
            businessService.ConfiguredMAO = Convert.IsDBNull(reader["ConfiguredMTPOD"])
                ? string.Empty
                : Convert.ToString(reader["ConfiguredMTPOD"]);

            businessService.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);

            businessService.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            businessService.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            businessService.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            businessService.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());

            businessService.Priority = Convert.IsDBNull(reader["Priority"]) ? 0 : Convert.ToInt32(reader["Priority"]);

            return businessService;
        }
    }
}