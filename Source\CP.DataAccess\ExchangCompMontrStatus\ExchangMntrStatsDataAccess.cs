﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
   
    internal class ExchangMntrStatsDataAccess : BaseDataAccess, IExchngMntrStatusDataAccess
    {
        #region Constructors

        public ExchangMntrStatsDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ExChangMntrStats> CreateEntityBuilder<ExChangMntrStats>()
        {

            return (new ExchngMontrStatsBuilder()) as IEntityBuilder<ExChangMntrStats>;
        }

        #endregion Constructors

        #region IExchangeDataAccess Members

        IList<ExChangMntrStats> IExchngMntrStatusDataAccess.GetExChangMntrStatushByInfraObjectID(int InfraObjectID)
        {
            try
            {

                const string sp = "EXCDAG_MNTR_STATS_GETBYIFRAOBJID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, InfraObjectID);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                        return CreateEntityBuilder<ExChangMntrStats>().BuildEntities(reader);
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IExChangeHealthSumryDataAccess.GetExChangeHealthByInfraObjectID" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<ExChangMntrStats> IExchngMntrStatusDataAccess.GetExChangMntrLogByInfraObjectID(int InfraObjectID)
        {
            try
            {

                const string sp = "EXCDAG_MNTR_Log_GETBYIFRAOBJID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, InfraObjectID);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                        return CreateEntityBuilder<ExChangMntrStats>().BuildEntities(reader);
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IExChangeHealthSumryDataAccess.GetExChangMntrLogByInfraObjectID" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }



        IList<ExChangMntrStats> IExchngMntrStatusDataAccess.ExchangeDagComp_GetByDate(int InfraObjectID, string startDate,
           string endDate)
        {
            try
            {
                const string sp = "ExchangeDagStatus_GetByDate";
                //ExChangMntrStats exchangeMntrStatus;
                //IList<ExChangMntrStats> lstExcnahdagDB = new List<ExChangMntrStats>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    startDate = Convert.ToDateTime(startDate).ToString("dd-MM-yy");
                    endDate = Convert.ToDateTime(endDate).ToString("dd-MM-yy");
#endif
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, InfraObjectID);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.AnsiString, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.AnsiString, endDate);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ExChangMntrStats>().BuildEntities(reader);
                    }

                }
                //return lstExcnahdagDB;
            }

            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IExchngMntrStatusDataAccess.GetByDate(" + InfraObjectID + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion IExchangeDataAccess Members
    }
}
