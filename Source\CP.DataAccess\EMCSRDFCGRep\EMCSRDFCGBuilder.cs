﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;


namespace CP.DataAccess
{
    internal sealed class EMCSRDFCGBuilder : IEntityBuilder<EMCSRDFCG>
    {

        IList<EMCSRDFCG> IEntityBuilder<EMCSRDFCG>.BuildEntities(IDataReader reader)
        {
            var emcsrdfReplication = new List<EMCSRDFCG>();

            while (reader.Read())
            {
                emcsrdfReplication.Add(((IEntityBuilder<EMCSRDFCG>)this).BuildEntity(reader, new EMCSRDFCG()));
            }

            return (emcsrdfReplication.Count > 0) ? emcsrdfReplication : null;
        }

        EMCSRDFCG IEntityBuilder<EMCSRDFCG>.BuildEntity(IDataReader reader, EMCSRDFCG emcsrdfReplication)
        {
            emcsrdfReplication.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["Id"]);
            emcsrdfReplication.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0 : Convert.ToInt32(reader["ReplicationId"]);
            emcsrdfReplication.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
            emcsrdfReplication.SYMRDFBinPath = Convert.IsDBNull(reader["SYMRDFBinPath"]) ? string.Empty : Convert.ToString(reader["SYMRDFBinPath"]);
            emcsrdfReplication.GroupName = Convert.IsDBNull(reader["GroupName"]) ? string.Empty : Convert.ToString(reader["GroupName"]);
            emcsrdfReplication.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            emcsrdfReplication.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            emcsrdfReplication.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            emcsrdfReplication.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());

            return emcsrdfReplication;
        }
    }
}